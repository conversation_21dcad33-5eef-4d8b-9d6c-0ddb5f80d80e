/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var Ui=function(t,e){return Ui=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,s){i.__proto__=s}||function(i,s){for(var r in s)Object.prototype.hasOwnProperty.call(s,r)&&(i[r]=s[r])},Ui(t,e)};export function __extends(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");Ui(t,e);function i(){this.constructor=t}t.prototype=e===null?Object.create(e):(i.prototype=e.prototype,new i)}export var __assign=function(){return __assign=Object.assign||function(e){for(var i,s=1,r=arguments.length;s<r;s++){i=arguments[s];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e},__assign.apply(this,arguments)};export function __rest(t,e){var i={};for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&e.indexOf(s)<0&&(i[s]=t[s]);if(t!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,s=Object.getOwnPropertySymbols(t);r<s.length;r++)e.indexOf(s[r])<0&&Object.prototype.propertyIsEnumerable.call(t,s[r])&&(i[s[r]]=t[s[r]]);return i}export function __decorate(t,e,i,s){var r=arguments.length,n=r<3?e:s===null?s=Object.getOwnPropertyDescriptor(e,i):s,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")n=Reflect.decorate(t,e,i,s);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(n=(r<3?o(n):r>3?o(e,i,n):o(e,i))||n);return r>3&&n&&Object.defineProperty(e,i,n),n}export function __param(t,e){return function(i,s){e(i,s,t)}}export function __esDecorate(t,e,i,s,r,n){function o(v){if(v!==void 0&&typeof v!="function")throw new TypeError("Function expected");return v}for(var a=s.kind,c=a==="getter"?"get":a==="setter"?"set":"value",h=!e&&t?s.static?t:t.prototype:null,u=e||(h?Object.getOwnPropertyDescriptor(h,s.name):{}),l,f=!1,d=i.length-1;d>=0;d--){var p={};for(var g in s)p[g]=g==="access"?{}:s[g];for(var g in s.access)p.access[g]=s.access[g];p.addInitializer=function(v){if(f)throw new TypeError("Cannot add initializers after decoration has completed");n.push(o(v||null))};var w=(0,i[d])(a==="accessor"?{get:u.get,set:u.set}:u[c],p);if(a==="accessor"){if(w===void 0)continue;if(w===null||typeof w!="object")throw new TypeError("Object expected");(l=o(w.get))&&(u.get=l),(l=o(w.set))&&(u.set=l),(l=o(w.init))&&r.unshift(l)}else(l=o(w))&&(a==="field"?r.unshift(l):u[c]=l)}h&&Object.defineProperty(h,s.name,u),f=!0}export function __runInitializers(t,e,i){for(var s=arguments.length>2,r=0;r<e.length;r++)i=s?e[r].call(t,i):e[r].call(t);return s?i:void 0}export function __propKey(t){return typeof t=="symbol"?t:"".concat(t)}export function __setFunctionName(t,e,i){return typeof e=="symbol"&&(e=e.description?"[".concat(e.description,"]"):""),Object.defineProperty(t,"name",{configurable:!0,value:i?"".concat(i," ",e):e})}export function __metadata(t,e){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(t,e)}export function __awaiter(t,e,i,s){function r(n){return n instanceof i?n:new i(function(o){o(n)})}return new(i||(i=Promise))(function(n,o){function a(u){try{h(s.next(u))}catch(l){o(l)}}function c(u){try{h(s.throw(u))}catch(l){o(l)}}function h(u){u.done?n(u.value):r(u.value).then(a,c)}h((s=s.apply(t,e||[])).next())})}export function __generator(t,e){var i={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},s,r,n,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(h){return function(u){return c([h,u])}}function c(h){if(s)throw new TypeError("Generator is already executing.");for(;o&&(o=0,h[0]&&(i=0)),i;)try{if(s=1,r&&(n=h[0]&2?r.return:h[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,h[1])).done)return n;switch(r=0,n&&(h=[h[0]&2,n.value]),h[0]){case 0:case 1:n=h;break;case 4:return i.label++,{value:h[1],done:!1};case 5:i.label++,r=h[1],h=[0];continue;case 7:h=i.ops.pop(),i.trys.pop();continue;default:if(n=i.trys,!(n=n.length>0&&n[n.length-1])&&(h[0]===6||h[0]===2)){i=0;continue}if(h[0]===3&&(!n||h[1]>n[0]&&h[1]<n[3])){i.label=h[1];break}if(h[0]===6&&i.label<n[1]){i.label=n[1],n=h;break}if(n&&i.label<n[2]){i.label=n[2],i.ops.push(h);break}n[2]&&i.ops.pop(),i.trys.pop();continue}h=e.call(t,i)}catch(u){h=[6,u],r=0}finally{s=n=0}if(h[0]&5)throw h[1];return{value:h[0]?h[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(t,e,i,s){s===void 0&&(s=i);var r=Object.getOwnPropertyDescriptor(e,i);(!r||("get"in r?!e.__esModule:r.writable||r.configurable))&&(r={enumerable:!0,get:function(){return e[i]}}),Object.defineProperty(t,s,r)}:function(t,e,i,s){s===void 0&&(s=i),t[s]=e[i]};export function __exportStar(t,e){for(var i in t)i!=="default"&&!Object.prototype.hasOwnProperty.call(e,i)&&__createBinding(e,t,i)}export function __values(t){var e=typeof Symbol=="function"&&Symbol.iterator,i=e&&t[e],s=0;if(i)return i.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&s>=t.length&&(t=void 0),{value:t&&t[s++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(t,e){var i=typeof Symbol=="function"&&t[Symbol.iterator];if(!i)return t;var s=i.call(t),r,n=[],o;try{for(;(e===void 0||e-- >0)&&!(r=s.next()).done;)n.push(r.value)}catch(a){o={error:a}}finally{try{r&&!r.done&&(i=s.return)&&i.call(s)}finally{if(o)throw o.error}}return n}export function __spread(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(__read(arguments[e]));return t}export function __spreadArrays(){for(var t=0,e=0,i=arguments.length;e<i;e++)t+=arguments[e].length;for(var s=Array(t),r=0,e=0;e<i;e++)for(var n=arguments[e],o=0,a=n.length;o<a;o++,r++)s[r]=n[o];return s}export function __spreadArray(t,e,i){if(i||arguments.length===2)for(var s=0,r=e.length,n;s<r;s++)(n||!(s in e))&&(n||(n=Array.prototype.slice.call(e,0,s)),n[s]=e[s]);return t.concat(n||Array.prototype.slice.call(e))}export function __await(t){return this instanceof __await?(this.v=t,this):new __await(t)}export function __asyncGenerator(t,e,i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s=i.apply(t,e||[]),r,n=[];return r={},a("next"),a("throw"),a("return",o),r[Symbol.asyncIterator]=function(){return this},r;function o(d){return function(p){return Promise.resolve(p).then(d,l)}}function a(d,p){s[d]&&(r[d]=function(g){return new Promise(function(w,v){n.push([d,g,w,v])>1||c(d,g)})},p&&(r[d]=p(r[d])))}function c(d,p){try{h(s[d](p))}catch(g){f(n[0][3],g)}}function h(d){d.value instanceof __await?Promise.resolve(d.value.v).then(u,l):f(n[0][2],d)}function u(d){c("next",d)}function l(d){c("throw",d)}function f(d,p){d(p),n.shift(),n.length&&c(n[0][0],n[0][1])}}export function __asyncDelegator(t){var e,i;return e={},s("next"),s("throw",function(r){throw r}),s("return"),e[Symbol.iterator]=function(){return this},e;function s(r,n){e[r]=t[r]?function(o){return(i=!i)?{value:__await(t[r](o)),done:!1}:n?n(o):o}:n}}export function __asyncValues(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],i;return e?e.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),i={},s("next"),s("throw"),s("return"),i[Symbol.asyncIterator]=function(){return this},i);function s(n){i[n]=t[n]&&function(o){return new Promise(function(a,c){o=t[n](o),r(a,c,o.done,o.value)})}}function r(n,o,a,c){Promise.resolve(c).then(function(h){n({value:h,done:a})},o)}}export function __makeTemplateObject(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}var bo=Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e};export function __importStar(t){if(t&&t.__esModule)return t;var e={};if(t!=null)for(var i in t)i!=="default"&&Object.prototype.hasOwnProperty.call(t,i)&&__createBinding(e,t,i);return bo(e,t),e}export function __importDefault(t){return t&&t.__esModule?t:{default:t}}export function __classPrivateFieldGet(t,e,i,s){if(i==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return i==="m"?s:i==="a"?s.call(t):s?s.value:e.get(t)}export function __classPrivateFieldSet(t,e,i,s,r){if(s==="m")throw new TypeError("Private method is not writable");if(s==="a"&&!r)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?t!==e||!r:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return s==="a"?r.call(t,i):r?r.value=i:e.set(t,i),i}export function __classPrivateFieldIn(t,e){if(e===null||typeof e!="object"&&typeof e!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof t=="function"?e===t:t.has(e)}export function __addDisposableResource(t,e,i){if(e!=null){if(typeof e!="object"&&typeof e!="function")throw new TypeError("Object expected.");var s,r;if(i){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");s=e[Symbol.asyncDispose]}if(s===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");s=e[Symbol.dispose],i&&(r=s)}if(typeof s!="function")throw new TypeError("Object not disposable.");r&&(s=function(){try{r.call(this)}catch(n){return Promise.reject(n)}}),t.stack.push({value:e,dispose:s,async:i})}else i&&t.stack.push({async:!0});return e}var Co=typeof SuppressedError=="function"?SuppressedError:function(t,e,i){var s=new Error(i);return s.name="SuppressedError",s.error=t,s.suppressed=e,s};export function __disposeResources(t){function e(s){t.error=t.hasError?new Co(s,t.error,"An error was suppressed during disposal."):s,t.hasError=!0}function i(){for(;t.stack.length;){var s=t.stack.pop();try{var r=s.dispose&&s.dispose.call(s.value);if(s.async)return Promise.resolve(r).then(i,function(n){return e(n),i()})}catch(n){e(n)}}if(t.hasError)throw t.error}return i()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};var Eo=Object.create,Vs=Object.defineProperty,xo=Object.getOwnPropertyDescriptor,Xs=Object.getOwnPropertyNames,So=Object.getPrototypeOf,Po=Object.prototype.hasOwnProperty,$o=(t,e)=>function(){return e||(0,t[Xs(t)[0]])((e={exports:{}}).exports,e),e.exports},Lo=(t,e,i,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of Xs(e))!Po.call(t,r)&&r!==i&&Vs(t,r,{get:()=>e[r],enumerable:!(s=xo(e,r))||s.enumerable});return t},ko=(t,e,i)=>(i=t!=null?Eo(So(t)):{},Lo(e||!t||!t.__esModule?Vs(i,"default",{value:t,enumerable:!0}):i,t)),Do=$o({"node_modules/minimist/index.js"(t,e){"use strict";function i(n,o){var a=n;o.slice(0,-1).forEach(function(h){a=a[h]||{}});var c=o[o.length-1];return c in a}function s(n){return typeof n=="number"||/^0x[0-9a-f]+$/i.test(n)?!0:/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)(e[-+]?\d+)?$/.test(n)}function r(n,o){return o==="constructor"&&typeof n[o]=="function"||o==="__proto__"}e.exports=function(n,o){o||(o={});var a={bools:{},strings:{},unknownFn:null};typeof o.unknown=="function"&&(a.unknownFn=o.unknown),typeof o.boolean=="boolean"&&o.boolean?a.allBools=!0:[].concat(o.boolean).filter(Boolean).forEach(function(E){a.bools[E]=!0});var c={};function h(E){return c[E].some(function(N){return a.bools[N]})}Object.keys(o.alias||{}).forEach(function(E){c[E]=[].concat(o.alias[E]),c[E].forEach(function(N){c[N]=[E].concat(c[E].filter(function(st){return N!==st}))})}),[].concat(o.string).filter(Boolean).forEach(function(E){a.strings[E]=!0,c[E]&&[].concat(c[E]).forEach(function(N){a.strings[N]=!0})});var u=o.default||{},l={_:[]};function f(E,N){return a.allBools&&/^--[^=]+$/.test(N)||a.strings[E]||a.bools[E]||c[E]}function d(E,N,st){for(var F=E,Wt=0;Wt<N.length-1;Wt++){var b=N[Wt];if(r(F,b))return;F[b]===void 0&&(F[b]={}),(F[b]===Object.prototype||F[b]===Number.prototype||F[b]===String.prototype)&&(F[b]={}),F[b]===Array.prototype&&(F[b]=[]),F=F[b]}var m=N[N.length-1];r(F,m)||((F===Object.prototype||F===Number.prototype||F===String.prototype)&&(F={}),F===Array.prototype&&(F=[]),F[m]===void 0||a.bools[m]||typeof F[m]=="boolean"?F[m]=st:Array.isArray(F[m])?F[m].push(st):F[m]=[F[m],st])}function p(E,N,st){if(!(st&&a.unknownFn&&!f(E,st)&&a.unknownFn(st)===!1)){var F=!a.strings[E]&&s(N)?Number(N):N;d(l,E.split("."),F),(c[E]||[]).forEach(function(Wt){d(l,Wt.split("."),F)})}}Object.keys(a.bools).forEach(function(E){p(E,u[E]===void 0?!1:u[E])});var g=[];n.indexOf("--")!==-1&&(g=n.slice(n.indexOf("--")+1),n=n.slice(0,n.indexOf("--")));for(var w=0;w<n.length;w++){var v=n[w],D,T;if(/^--.+=/.test(v)){var ie=v.match(/^--([^=]+)=([\s\S]*)$/);D=ie[1];var jt=ie[2];a.bools[D]&&(jt=jt!=="false"),p(D,jt,v)}else if(/^--no-.+/.test(v))D=v.match(/^--no-(.+)/)[1],p(D,!1,v);else if(/^--.+/.test(v))D=v.match(/^--(.+)/)[1],T=n[w+1],T!==void 0&&!/^(-|--)[^-]/.test(T)&&!a.bools[D]&&!a.allBools&&(!c[D]||!h(D))?(p(D,T,v),w+=1):/^(true|false)$/.test(T)?(p(D,T==="true",v),w+=1):p(D,a.strings[D]?"":!0,v);else if(/^-[^-]+/.test(v)){for(var at=v.slice(1,-1).split(""),P=!1,$=0;$<at.length;$++){if(T=v.slice($+2),T==="-"){p(at[$],T,v);continue}if(/[A-Za-z]/.test(at[$])&&T[0]==="="){p(at[$],T.slice(1),v),P=!0;break}if(/[A-Za-z]/.test(at[$])&&/-?\d+(\.\d*)?(e-?\d+)?$/.test(T)){p(at[$],T,v),P=!0;break}if(at[$+1]&&at[$+1].match(/\W/)){p(at[$],v.slice($+2),v),P=!0;break}else p(at[$],a.strings[at[$]]?"":!0,v)}D=v.slice(-1)[0],!P&&D!=="-"&&(n[w+1]&&!/^(-|--)[^-]/.test(n[w+1])&&!a.bools[D]&&(!c[D]||!h(D))?(p(D,n[w+1],v),w+=1):n[w+1]&&/^(true|false)$/.test(n[w+1])?(p(D,n[w+1]==="true",v),w+=1):p(D,a.strings[D]?"":!0,v))}else if((!a.unknownFn||a.unknownFn(v)!==!1)&&l._.push(a.strings._||!s(v)?v:Number(v)),o.stopEarly){l._.push.apply(l._,n.slice(w+1));break}}return Object.keys(u).forEach(function(E){i(l,E.split("."))||(d(l,E.split("."),u[E]),(c[E]||[]).forEach(function(N){d(l,N.split("."),u[E])}))}),o["--"]?l["--"]=g.slice():g.forEach(function(E){l._.push(E)}),l}}}),se=class{constructor(t){this.d=t,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(t){this.c=t}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}},Ao=class{constructor(){this.b=[],this.a=function(t){setTimeout(()=>{throw t.stack?re.isErrorNoTelemetry(t)?new re(t.message+`

`+t.stack):new Error(t.message+`

`+t.stack):t},0)}}addListener(t){return this.b.push(t),()=>{this.d(t)}}c(t){this.b.forEach(e=>{e(t)})}d(t){this.b.splice(this.b.indexOf(t),1)}setUnexpectedErrorHandler(t){this.a=t}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(t){this.a(t),this.c(t)}onUnexpectedExternalError(t){this.a(t)}},Mo=new Ao;function ni(t){Io(t)||Mo.onUnexpectedError(t)}var ji="Canceled";function Io(t){return t instanceof _t?!0:t instanceof Error&&t.name===ji&&t.message===ji}var _t=class extends Error{constructor(){super(ji),this.name=this.message}},Pu=class Oi extends Error{static{this.a="PendingMigrationError"}static is(e){return e instanceof Oi||e instanceof Error&&e.name===Oi.a}constructor(e){super(e),this.name=Oi.a}};function Oo(t){return t?new Error(`Illegal state: ${t}`):new Error("Illegal state")}var re=class Us extends Error{constructor(e){super(e),this.name="CodeExpectedError"}static fromError(e){if(e instanceof Us)return e;const i=new Us;return i.message=e.message,i.stack=e.stack,i}static isErrorNoTelemetry(e){return e.name==="CodeExpectedError"}},_o=class G1 extends Error{constructor(e){super(e||"An unexpected bug occurred."),Object.setPrototypeOf(this,G1.prototype)}};function Ro(t,e,i=0,s=t.length){let r=i,n=s;for(;r<n;){const o=Math.floor((r+n)/2);e(t[o])?r=o+1:n=o}return r-1}var $u=class Q1{static{this.assertInvariants=!1}constructor(e){this.e=e,this.c=0}findLastMonotonous(e){if(Q1.assertInvariants){if(this.d){for(const s of this.e)if(this.d(s)&&!e(s))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=e}const i=Ro(this.e,e,this.c);return this.c=i+1,i===-1?void 0:this.e[i]}};function No(t){return t.filter(e=>!!e)}function Gs(t,e){let i;if(typeof e=="number"){let s=e;i=()=>{const r=Math.sin(s++)*179426549;return r-Math.floor(r)}}else i=Math.random;for(let s=t.length-1;s>0;s-=1){const r=Math.floor(i()*(s+1)),n=t[s];t[s]=t[r],t[r]=n}}function To(t){return t[Math.floor(Math.random()*t.length)]}var Wi;(function(t){function e(n){return n<0}t.isLessThan=e;function i(n){return n<=0}t.isLessThanOrEqual=i;function s(n){return n>0}t.isGreaterThan=s;function r(n){return n===0}t.isNeitherLessOrGreaterThan=r,t.greaterThan=1,t.lessThan=-1,t.neitherLessOrGreaterThan=0})(Wi||(Wi={}));function Bo(t,e){return(i,s)=>e(t(i),t(s))}var Fo=(t,e)=>t-e,Lu=class _i{static{this.empty=new _i(e=>{})}constructor(e){this.iterate=e}forEach(e){this.iterate(i=>(e(i),!0))}toArray(){const e=[];return this.iterate(i=>(e.push(i),!0)),e}filter(e){return new _i(i=>this.iterate(s=>e(s)?i(s):!0))}map(e){return new _i(i=>this.iterate(s=>i(e(s))))}some(e){let i=!1;return this.iterate(s=>(i=e(s),!i)),i}findFirst(e){let i;return this.iterate(s=>e(s)?(i=s,!1):!0),i}findLast(e){let i;return this.iterate(s=>(e(s)&&(i=s),!0)),i}findLastMaxBy(e){let i,s=!0;return this.iterate(r=>((s||Wi.isGreaterThan(e(r,i)))&&(s=!1,i=r),!0)),i}},Qs;function Uo(t,e){const i=Object.create(null);for(const s of t){const r=e(s);let n=i[r];n||(n=i[r]=[]),n.push(s)}return i}var ku=class{static{Qs=Symbol.toStringTag}constructor(t,e){this.b=e,this.a=new Map,this[Qs]="SetWithKey";for(const i of t)this.add(i)}get size(){return this.a.size}add(t){const e=this.b(t);return this.a.set(e,t),this}delete(t){return this.a.delete(this.b(t))}has(t){return this.a.has(this.b(t))}*entries(){for(const t of this.a.values())yield[t,t]}keys(){return this.values()}*values(){for(const t of this.a.values())yield t}clear(){this.a.clear()}forEach(t,e){this.a.forEach(i=>t.call(e,i,i,this))}[Symbol.iterator](){return this.values()}},Ys,Js,Ks,jo=class{constructor(t,e){this.uri=t,this.value=e}};function Wo(t){return Array.isArray(t)}var zi=class Je{static{this.c=e=>e.toString()}constructor(e,i){if(this[Ys]="ResourceMap",e instanceof Je)this.d=new Map(e.d),this.e=i??Je.c;else if(Wo(e)){this.d=new Map,this.e=i??Je.c;for(const[s,r]of e)this.set(s,r)}else this.d=new Map,this.e=e??Je.c}set(e,i){return this.d.set(this.e(e),new jo(e,i)),this}get(e){return this.d.get(this.e(e))?.value}has(e){return this.d.has(this.e(e))}get size(){return this.d.size}clear(){this.d.clear()}delete(e){return this.d.delete(this.e(e))}forEach(e,i){typeof i<"u"&&(e=e.bind(i));for(const[s,r]of this.d)e(r.value,r.uri,this)}*values(){for(const e of this.d.values())yield e.value}*keys(){for(const e of this.d.values())yield e.uri}*entries(){for(const e of this.d.values())yield[e.uri,e.value]}*[(Ys=Symbol.toStringTag,Symbol.iterator)](){for(const[,e]of this.d)yield[e.uri,e.value]}},Du=class{constructor(t,e){this[Js]="ResourceSet",!t||typeof t=="function"?this.c=new zi(t):(this.c=new zi(e),t.forEach(this.add,this))}get size(){return this.c.size}add(t){return this.c.set(t,t),this}clear(){this.c.clear()}delete(t){return this.c.delete(t)}forEach(t,e){this.c.forEach((i,s)=>t.call(e,s,s,this))}has(t){return this.c.has(t)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(Js=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},Zs;(function(t){t[t.None=0]="None",t[t.AsOld=1]="AsOld",t[t.AsNew=2]="AsNew"})(Zs||(Zs={}));var zo=class{constructor(){this[Ks]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(t){return this.c.has(t)}get(t,e=0){const i=this.c.get(t);if(i)return e!==0&&this.n(i,e),i.value}set(t,e,i=0){let s=this.c.get(t);if(s)s.value=e,i!==0&&this.n(s,i);else{switch(s={key:t,value:e,next:void 0,previous:void 0},i){case 0:this.l(s);break;case 1:this.k(s);break;case 2:this.l(s);break;default:this.l(s);break}this.c.set(t,s),this.f++}return this}delete(t){return!!this.remove(t)}remove(t){const e=this.c.get(t);if(e)return this.c.delete(t),this.m(e),this.f--,e.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const t=this.d;return this.c.delete(t.key),this.m(t),this.f--,t.value}forEach(t,e){const i=this.g;let s=this.d;for(;s;){if(e?t.bind(e)(s.value,s.key,this):t(s.value,s.key,this),this.g!==i)throw new Error("LinkedMap got modified during iteration.");s=s.next}}keys(){const t=this,e=this.g;let i=this.d;const s={[Symbol.iterator](){return s},next(){if(t.g!==e)throw new Error("LinkedMap got modified during iteration.");if(i){const r={value:i.key,done:!1};return i=i.next,r}else return{value:void 0,done:!0}}};return s}values(){const t=this,e=this.g;let i=this.d;const s={[Symbol.iterator](){return s},next(){if(t.g!==e)throw new Error("LinkedMap got modified during iteration.");if(i){const r={value:i.value,done:!1};return i=i.next,r}else return{value:void 0,done:!0}}};return s}entries(){const t=this,e=this.g;let i=this.d;const s={[Symbol.iterator](){return s},next(){if(t.g!==e)throw new Error("LinkedMap got modified during iteration.");if(i){const r={value:[i.key,i.value],done:!1};return i=i.next,r}else return{value:void 0,done:!0}}};return s}[(Ks=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(t){if(t>=this.size)return;if(t===0){this.clear();return}let e=this.d,i=this.size;for(;e&&i>t;)this.c.delete(e.key),e=e.next,i--;this.d=e,this.f=i,e&&(e.previous=void 0),this.g++}j(t){if(t>=this.size)return;if(t===0){this.clear();return}let e=this.e,i=this.size;for(;e&&i>t;)this.c.delete(e.key),e=e.previous,i--;this.e=e,this.f=i,e&&(e.next=void 0),this.g++}k(t){if(!this.d&&!this.e)this.e=t;else if(this.d)t.next=this.d,this.d.previous=t;else throw new Error("Invalid list");this.d=t,this.g++}l(t){if(!this.d&&!this.e)this.d=t;else if(this.e)t.previous=this.e,this.e.next=t;else throw new Error("Invalid list");this.e=t,this.g++}m(t){if(t===this.d&&t===this.e)this.d=void 0,this.e=void 0;else if(t===this.d){if(!t.next)throw new Error("Invalid list");t.next.previous=void 0,this.d=t.next}else if(t===this.e){if(!t.previous)throw new Error("Invalid list");t.previous.next=void 0,this.e=t.previous}else{const e=t.next,i=t.previous;if(!e||!i)throw new Error("Invalid list");e.previous=i,i.next=e}t.next=void 0,t.previous=void 0,this.g++}n(t,e){if(!this.d||!this.e)throw new Error("Invalid list");if(!(e!==1&&e!==2)){if(e===1){if(t===this.d)return;const i=t.next,s=t.previous;t===this.e?(s.next=void 0,this.e=s):(i.previous=s,s.next=i),t.previous=void 0,t.next=this.d,this.d.previous=t,this.d=t,this.g++}else if(e===2){if(t===this.e)return;const i=t.next,s=t.previous;t===this.d?(i.previous=void 0,this.d=i):(i.previous=s,s.next=i),t.next=void 0,t.previous=this.e,this.e.next=t,this.e=t,this.g++}}}toJSON(){const t=[];return this.forEach((e,i)=>{t.push([i,e])}),t}fromJSON(t){this.clear();for(const[e,i]of t)this.set(e,i)}},Ho=class extends zo{constructor(t,e=1){super(),this.o=t,this.p=Math.min(Math.max(0,e),1)}get limit(){return this.o}set limit(t){this.o=t,this.q()}get ratio(){return this.p}set ratio(t){this.p=Math.min(Math.max(0,t),1),this.q()}get(t,e=2){return super.get(t,e)}peek(t){return super.get(t,0)}set(t,e){return super.set(t,e,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},tr=class extends Ho{constructor(t,e=1){super(t,e)}r(t){this.h(t)}set(t,e){return super.set(t,e),this.q(),this}},qo=class{constructor(){this.c=new Map}add(t,e){let i=this.c.get(t);i||(i=new Set,this.c.set(t,i)),i.add(e)}delete(t,e){const i=this.c.get(t);i&&(i.delete(e),i.size===0&&this.c.delete(t))}forEach(t,e){const i=this.c.get(t);i&&i.forEach(e)}get(t){const e=this.c.get(t);return e||new Set}};function Vo(t,e){if(t===e)return!0;if(t.size!==e.size)return!1;for(const[i,s]of t)if(!e.has(i)||e.get(i)!==s)return!1;for(const[i]of e)if(!t.has(i))return!1;return!0}function Hi(t,e){const i=this;let s=!1,r;return function(){if(s)return r;if(s=!0,e)try{r=t.apply(i,arguments)}finally{e()}else r=t.apply(i,arguments);return r}}function qi(t,e){if(!t)throw new Error(e?`Assertion failed (${e})`:"Assertion Failed")}function er(t,e="unexpected state"){if(!t)throw typeof e=="string"?new _o(`Assertion Failed: ${e}`):e}function ne(t){return typeof t=="string"}function Xo(t){return typeof t=="object"&&t!==null&&!Array.isArray(t)&&!(t instanceof RegExp)&&!(t instanceof Date)}function ir(t){return typeof t=="number"&&!isNaN(t)}function Go(t){return!!t&&typeof t[Symbol.iterator]=="function"}function Qo(t){return typeof t>"u"}function Yo(t){return Qo(t)||t===null}function Jo(t,e){if(!t)throw new Error(e?`Unexpected type, expected '${e}'`:"Unexpected type")}function Vi(t){return typeof t=="function"}var Xi;(function(t){function e(P){return!!P&&typeof P=="object"&&typeof P[Symbol.iterator]=="function"}t.is=e;const i=Object.freeze([]);function s(){return i}t.empty=s;function*r(P){yield P}t.single=r;function n(P){return e(P)?P:r(P)}t.wrap=n;function o(P){return P||i}t.from=o;function*a(P){for(let $=P.length-1;$>=0;$--)yield P[$]}t.reverse=a;function c(P){return!P||P[Symbol.iterator]().next().done===!0}t.isEmpty=c;function h(P){return P[Symbol.iterator]().next().value}t.first=h;function u(P,$){let E=0;for(const N of P)if($(N,E++))return!0;return!1}t.some=u;function l(P,$){let E=0;for(const N of P)if(!$(N,E++))return!1;return!0}t.every=l;function f(P,$){for(const E of P)if($(E))return E}t.find=f;function*d(P,$){for(const E of P)$(E)&&(yield E)}t.filter=d;function*p(P,$){let E=0;for(const N of P)yield $(N,E++)}t.map=p;function*g(P,$){let E=0;for(const N of P)yield*$(N,E++)}t.flatMap=g;function*w(...P){for(const $ of P)Go($)?yield*$:yield $}t.concat=w;function v(P,$,E){let N=E;for(const st of P)N=$(N,st);return N}t.reduce=v;function D(P){let $=0;for(const E of P)$++;return $}t.length=D;function*T(P,$,E=P.length){for($<-P.length&&($=0),$<0&&($+=P.length),E<0?E+=P.length:E>P.length&&(E=P.length);$<E;$++)yield P[$]}t.slice=T;function ie(P,$=Number.POSITIVE_INFINITY){const E=[];if($===0)return[E,P];const N=P[Symbol.iterator]();for(let st=0;st<$;st++){const F=N.next();if(F.done)return[E,t.empty()];E.push(F.value)}return[E,{[Symbol.iterator](){return N}}]}t.consume=ie;async function jt(P){const $=[];for await(const E of P)$.push(E);return $}t.asyncToArray=jt;async function at(P){let $=[];for await(const E of P)$=$.concat(E);return $}t.asyncToArrayFlat=at})(Xi||(Xi={}));var Ko=!1,oe=null,Au=class Y1{constructor(){this.b=new Map}static{this.a=0}c(e){let i=this.b.get(e);return i||(i={parent:null,source:null,isSingleton:!1,value:e,idx:Y1.a++},this.b.set(e,i)),i}trackDisposable(e){const i=this.c(e);i.source||(i.source=new Error().stack)}setParent(e,i){const s=this.c(e);s.parent=i}markAsDisposed(e){this.b.delete(e)}markAsSingleton(e){this.c(e).isSingleton=!0}f(e,i){const s=i.get(e);if(s)return s;const r=e.parent?this.f(this.c(e.parent),i):e;return i.set(e,r),r}getTrackedDisposables(){const e=new Map;return[...this.b.entries()].filter(([,s])=>s.source!==null&&!this.f(s,e).isSingleton).flatMap(([s])=>s)}computeLeakingDisposables(e=10,i){let s;if(i)s=i;else{const c=new Map,h=[...this.b.values()].filter(l=>l.source!==null&&!this.f(l,c).isSingleton);if(h.length===0)return;const u=new Set(h.map(l=>l.value));if(s=h.filter(l=>!(l.parent&&u.has(l.parent))),s.length===0)throw new Error("There are cyclic diposable chains!")}if(!s)return;function r(c){function h(l,f){for(;l.length>0&&f.some(d=>typeof d=="string"?d===l[0]:l[0].match(d));)l.shift()}const u=c.source.split(`
`).map(l=>l.trim().replace("at ","")).filter(l=>l!=="");return h(u,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),u.reverse()}const n=new qo;for(const c of s){const h=r(c);for(let u=0;u<=h.length;u++)n.add(h.slice(0,u).join(`
`),c)}s.sort(Bo(c=>c.idx,Fo));let o="",a=0;for(const c of s.slice(0,e)){a++;const h=r(c),u=[];for(let l=0;l<h.length;l++){let f=h[l];f=`(shared with ${n.get(h.slice(0,l+1).join(`
`)).size}/${s.length} leaks) at ${f}`;const p=n.get(h.slice(0,l).join(`
`)),g=Uo([...p].map(w=>r(w)[l]),w=>w);delete g[h[l]];for(const[w,v]of Object.entries(g))u.unshift(`    - stacktraces of ${v.length} other leaks continue with ${w}`);u.unshift(f)}o+=`


==================== Leaking disposable ${a}/${s.length}: ${c.value.constructor.name} ====================
${u.join(`
`)}
============================================================

`}return s.length>e&&(o+=`


... and ${s.length-e} more leaking disposables

`),{leaks:s,details:o}}};function Zo(t){oe=t}if(Ko){const t="__is_disposable_tracked__";Zo(new class{trackDisposable(e){const i=new Error("Potentially leaked disposable").stack;setTimeout(()=>{e[t]||console.log(i)},3e3)}setParent(e,i){if(e&&e!==V.None)try{e[t]=!0}catch{}}markAsDisposed(e){if(e&&e!==V.None)try{e[t]=!0}catch{}}markAsSingleton(e){}})}function Ce(t){return oe?.trackDisposable(t),t}function Ee(t){oe?.markAsDisposed(t)}function Ht(t,e){oe?.setParent(t,e)}function ta(t,e){if(oe)for(const i of t)oe.setParent(i,e)}function ea(t){return typeof t=="object"&&t!==null&&typeof t.dispose=="function"&&t.dispose.length===0}function Rt(t){if(Xi.is(t)){const e=[];for(const i of t)if(i)try{i.dispose()}catch(s){e.push(s)}if(e.length===1)throw e[0];if(e.length>1)throw new AggregateError(e,"Encountered errors while disposing of store");return Array.isArray(t)?[]:t}else if(t)return t.dispose(),t}function ia(...t){const e=wt(()=>Rt(t));return ta(t,e),e}function wt(t){const e=Ce({dispose:Hi(()=>{Ee(e),t()})});return e}var qt=class J1{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,Ce(this)}dispose(){this.g||(Ee(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{Rt(this.f)}finally{this.f.clear()}}add(e){if(!e)return e;if(e===this)throw new Error("Cannot register a disposable on itself!");return Ht(e,this),this.g?J1.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(e),e}delete(e){if(e){if(e===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(e),e.dispose()}}deleteAndLeak(e){e&&this.f.has(e)&&(this.f.delete(e),Ht(e,null))}},V=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new qt,Ce(this),Ht(this.q,this)}dispose(){Ee(this),this.q.dispose()}B(t){if(t===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(t)}},sr=class{constructor(){this.b=!1,Ce(this)}get value(){return this.b?void 0:this.a}set value(t){this.b||t===this.a||(this.a?.dispose(),t&&Ht(t,this),this.a=t)}clear(){this.value=void 0}dispose(){this.b=!0,Ee(this),this.a?.dispose(),this.a=void 0}clearAndLeak(){const t=this.a;return this.a=void 0,t&&Ht(t,null),t}},sa=class{constructor(t){this.a=new sr,this.b=!1,this.a.value=t}get value(){return this.a.value}set value(t){this.b||t===this.a.value||(this.a.value=t)}dispose(){this.b=!0,this.a.dispose()}},ra=class{constructor(){this.a=new Map,this.b=!1,Ce(this)}dispose(){Ee(this),this.b=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this.a.size)try{Rt(this.a.values())}finally{this.a.clear()}}has(t){return this.a.has(t)}get size(){return this.a.size}get(t){return this.a.get(t)}set(t,e,i=!1){this.b&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),i||this.a.get(t)?.dispose(),this.a.set(t,e),Ht(e,this)}deleteAndDispose(t){this.a.get(t)?.dispose(),this.a.delete(t)}deleteAndLeak(t){const e=this.a.get(t);return e&&Ht(e,null),this.a.delete(t),e}keys(){return this.a.keys()}values(){return this.a.values()}[Symbol.iterator](){return this.a[Symbol.iterator]()}},xe=typeof Buffer<"u",na=new se(()=>new Uint8Array(256)),Gi,Qi,gt=class vt{static alloc(e){return xe?new vt(Buffer.allocUnsafe(e)):new vt(new Uint8Array(e))}static wrap(e){return xe&&!Buffer.isBuffer(e)&&(e=Buffer.from(e.buffer,e.byteOffset,e.byteLength)),new vt(e)}static fromString(e,i){return!(i?.dontUseNodeBuffer||!1)&&xe?new vt(Buffer.from(e)):(Gi||(Gi=new TextEncoder),new vt(Gi.encode(e)))}static fromByteArray(e){const i=vt.alloc(e.length);for(let s=0,r=e.length;s<r;s++)i.buffer[s]=e[s];return i}static concat(e,i){if(typeof i>"u"){i=0;for(let n=0,o=e.length;n<o;n++)i+=e[n].byteLength}const s=vt.alloc(i);let r=0;for(let n=0,o=e.length;n<o;n++){const a=e[n];s.set(a,r),r+=a.byteLength}return s}static isNativeBuffer(e){return xe&&Buffer.isBuffer(e)}constructor(e){this.buffer=e,this.byteLength=this.buffer.byteLength}clone(){const e=vt.alloc(this.byteLength);return e.set(this),e}toString(){return xe?this.buffer.toString():(Qi||(Qi=new TextDecoder),Qi.decode(this.buffer))}slice(e,i){return new vt(this.buffer.subarray(e,i))}set(e,i){if(e instanceof vt)this.buffer.set(e.buffer,i);else if(e instanceof Uint8Array)this.buffer.set(e,i);else if(e instanceof ArrayBuffer)this.buffer.set(new Uint8Array(e),i);else if(ArrayBuffer.isView(e))this.buffer.set(new Uint8Array(e.buffer,e.byteOffset,e.byteLength),i);else throw new Error("Unknown argument 'array'")}readUInt32BE(e){return aa(this.buffer,e)}writeUInt32BE(e,i){ha(this.buffer,e,i)}readUInt32LE(e){return ca(this.buffer,e)}writeUInt32LE(e,i){la(this.buffer,e,i)}readUInt8(e){return ua(this.buffer,e)}writeUInt8(e,i){fa(this.buffer,e,i)}indexOf(e,i=0){return oa(this.buffer,e instanceof vt?e.buffer:e,i)}equals(e){return this===e?!0:this.byteLength!==e.byteLength?!1:this.buffer.every((i,s)=>i===e.buffer[s])}};function oa(t,e,i=0){const s=e.byteLength,r=t.byteLength;if(s===0)return 0;if(s===1)return t.indexOf(e[0]);if(s>r-i)return-1;const n=na.value;n.fill(e.length);for(let h=0;h<e.length;h++)n[e[h]]=e.length-h-1;let o=i+e.length-1,a=o,c=-1;for(;o<r;)if(t[o]===e[a]){if(a===0){c=o;break}o--,a--}else o+=Math.max(e.length-a,n[t[o]]),a=e.length-1;return c}function aa(t,e){return t[e]*2**24+t[e+1]*2**16+t[e+2]*2**8+t[e+3]}function ha(t,e,i){t[i+3]=e,e=e>>>8,t[i+2]=e,e=e>>>8,t[i+1]=e,e=e>>>8,t[i]=e}function ca(t,e){return t[e+0]<<0>>>0|t[e+1]<<8>>>0|t[e+2]<<16>>>0|t[e+3]<<24>>>0}function la(t,e,i){t[i+0]=e&255,e=e>>>8,t[i+1]=e&255,e=e>>>8,t[i+2]=e&255,e=e>>>8,t[i+3]=e&255}function ua(t,e){return t[e]}function fa(t,e,i){t[i]=e}var rr="0123456789abcdef";function da({buffer:t}){let e="";for(let i=0;i<t.length;i++){const s=t[i];e+=rr[s>>>4],e+=rr[s&15]}return e}function pa(){return globalThis._VSCODE_NLS_MESSAGES}function nr(){return globalThis._VSCODE_NLS_LANGUAGE}var ga=nr()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function or(t,e){let i;return e.length===0?i=t:i=t.replace(/\{(\d+)\}/g,(s,r)=>{const n=r[0],o=e[n];let a=s;return typeof o=="string"?a=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(a=String(o)),a}),ga&&(i="\uFF3B"+i.replace(/[aouei]/g,"$&$&")+"\uFF3D"),i}function y(t,e,...i){return or(typeof t=="number"?ma(t,e):e,i)}function ma(t,e){const i=pa()?.[t];if(typeof i!="string"){if(typeof e=="string")return e;throw new Error(`!!! NLS MISSING: ${t} !!!`)}return i}var ae="en",Se=!1,Pe=!1,$e=!1,va=!1,ar=!1,Yi=!1,wa=!1,hr=!1,ya=!1,ba=!1,oi=void 0,ai=ae,cr=ae,Ca=void 0,xt=void 0,St=globalThis,lt=void 0;typeof St.vscode<"u"&&typeof St.vscode.process<"u"?lt=St.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(lt=process);var lr=typeof lt?.versions?.electron=="string",Ea=lr&&lt?.type==="renderer";if(typeof lt=="object"){Se=lt.platform==="win32",Pe=lt.platform==="darwin",$e=lt.platform==="linux",va=$e&&!!lt.env.SNAP&&!!lt.env.SNAP_REVISION,wa=lr,ya=!!lt.env.CI||!!lt.env.BUILD_ARTIFACTSTAGINGDIRECTORY||!!lt.env.GITHUB_WORKSPACE,oi=ae,ai=ae;const t=lt.env.VSCODE_NLS_CONFIG;if(t)try{const e=JSON.parse(t);oi=e.userLocale,cr=e.osLocale,ai=e.resolvedLanguage||ae,Ca=e.languagePack?.translationsConfigFile}catch{}ar=!0}else typeof navigator=="object"&&!Ea?(xt=navigator.userAgent,Se=xt.indexOf("Windows")>=0,Pe=xt.indexOf("Macintosh")>=0,hr=(xt.indexOf("Macintosh")>=0||xt.indexOf("iPad")>=0||xt.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,$e=xt.indexOf("Linux")>=0,ba=xt?.indexOf("Mobi")>=0,Yi=!0,ai=nr()||ae,oi=navigator.language.toLowerCase(),cr=oi):console.error("Unable to resolve platform.");var ur;(function(t){t[t.Web=0]="Web",t[t.Mac=1]="Mac",t[t.Linux=2]="Linux",t[t.Windows=3]="Windows"})(ur||(ur={}));var hi=0;Pe?hi=1:Se?hi=3:$e&&(hi=2);var R=Se,Nt=Pe,Le=$e,xa=ar,Ji=Yi,Sa=Yi&&typeof St.importScripts=="function",Pa=Sa?St.origin:void 0,$a=hi,bt=xt,Tt=ai,fr;(function(t){function e(){return Tt}t.value=e;function i(){return Tt.length===2?Tt==="en":Tt.length>=3?Tt[0]==="e"&&Tt[1]==="n"&&Tt[2]==="-":!1}t.isDefaultVariant=i;function s(){return Tt==="en"}t.isDefault=s})(fr||(fr={}));var La=typeof St.postMessage=="function"&&!St.importScripts,ka=(()=>{if(La){const t=[];St.addEventListener("message",i=>{if(i.data&&i.data.vscodeScheduleAsyncWork)for(let s=0,r=t.length;s<r;s++){const n=t[s];if(n.id===i.data.vscodeScheduleAsyncWork){t.splice(s,1),n.callback();return}}});let e=0;return i=>{const s=++e;t.push({id:s,callback:i}),St.postMessage({vscodeScheduleAsyncWork:s},"*")}}return t=>setTimeout(t)})(),dr;(function(t){t[t.Windows=1]="Windows",t[t.Macintosh=2]="Macintosh",t[t.Linux=3]="Linux"})(dr||(dr={}));var pr=Pe||hr?2:Se?1:3,gr=!!(bt&&bt.indexOf("Chrome")>=0),Da=!!(bt&&bt.indexOf("Firefox")>=0),Aa=!!(!gr&&bt&&bt.indexOf("Safari")>=0),Ma=!!(bt&&bt.indexOf("Edg/")>=0),Mu=!!(bt&&bt.indexOf("Android")>=0),Vt,Ki=globalThis.vscode;if(typeof Ki<"u"&&typeof Ki.process<"u"){const t=Ki.process;Vt={get platform(){return t.platform},get arch(){return t.arch},get env(){return t.env},cwd(){return t.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?Vt={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:Vt={get platform(){return R?"win32":Nt?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var ke=Vt.cwd,Pt=Vt.env,mr=Vt.platform,Iu=Vt.arch,Ia=65,Oa=97,_a=90,Ra=122,Xt=46,it=47,ut=92,$t=58,Na=63,vr=class extends Error{constructor(t,e,i){let s;typeof e=="string"&&e.indexOf("not ")===0?(s="must not be",e=e.replace(/^not /,"")):s="must be";const r=t.indexOf(".")!==-1?"property":"argument";let n=`The "${t}" ${r} ${s} of type ${e}`;n+=`. Received type ${typeof i}`,super(n),this.code="ERR_INVALID_ARG_TYPE"}};function Ta(t,e){if(t===null||typeof t!="object")throw new vr(e,"Object",t)}function J(t,e){if(typeof t!="string")throw new vr(e,"string",t)}var ft=mr==="win32";function A(t){return t===it||t===ut}function Zi(t){return t===it}function Lt(t){return t>=Ia&&t<=_a||t>=Oa&&t<=Ra}function ci(t,e,i,s){let r="",n=0,o=-1,a=0,c=0;for(let h=0;h<=t.length;++h){if(h<t.length)c=t.charCodeAt(h);else{if(s(c))break;c=it}if(s(c)){if(!(o===h-1||a===1))if(a===2){if(r.length<2||n!==2||r.charCodeAt(r.length-1)!==Xt||r.charCodeAt(r.length-2)!==Xt){if(r.length>2){const u=r.lastIndexOf(i);u===-1?(r="",n=0):(r=r.slice(0,u),n=r.length-1-r.lastIndexOf(i)),o=h,a=0;continue}else if(r.length!==0){r="",n=0,o=h,a=0;continue}}e&&(r+=r.length>0?`${i}..`:"..",n=2)}else r.length>0?r+=`${i}${t.slice(o+1,h)}`:r=t.slice(o+1,h),n=h-o-1;o=h,a=0}else c===Xt&&a!==-1?++a:a=-1}return r}function Ba(t){return t?`${t[0]==="."?"":"."}${t}`:""}function wr(t,e){Ta(e,"pathObject");const i=e.dir||e.root,s=e.base||`${e.name||""}${Ba(e.ext)}`;return i?i===e.root?`${i}${s}`:`${i}${t}${s}`:s}var K={resolve(...t){let e="",i="",s=!1;for(let r=t.length-1;r>=-1;r--){let n;if(r>=0){if(n=t[r],J(n,`paths[${r}]`),n.length===0)continue}else e.length===0?n=ke():(n=Pt[`=${e}`]||ke(),(n===void 0||n.slice(0,2).toLowerCase()!==e.toLowerCase()&&n.charCodeAt(2)===ut)&&(n=`${e}\\`));const o=n.length;let a=0,c="",h=!1;const u=n.charCodeAt(0);if(o===1)A(u)&&(a=1,h=!0);else if(A(u))if(h=!0,A(n.charCodeAt(1))){let l=2,f=l;for(;l<o&&!A(n.charCodeAt(l));)l++;if(l<o&&l!==f){const d=n.slice(f,l);for(f=l;l<o&&A(n.charCodeAt(l));)l++;if(l<o&&l!==f){for(f=l;l<o&&!A(n.charCodeAt(l));)l++;(l===o||l!==f)&&(c=`\\\\${d}\\${n.slice(f,l)}`,a=l)}}}else a=1;else Lt(u)&&n.charCodeAt(1)===$t&&(c=n.slice(0,2),a=2,o>2&&A(n.charCodeAt(2))&&(h=!0,a=3));if(c.length>0)if(e.length>0){if(c.toLowerCase()!==e.toLowerCase())continue}else e=c;if(s){if(e.length>0)break}else if(i=`${n.slice(a)}\\${i}`,s=h,h&&e.length>0)break}return i=ci(i,!s,"\\",A),s?`${e}\\${i}`:`${e}${i}`||"."},normalize(t){J(t,"path");const e=t.length;if(e===0)return".";let i=0,s,r=!1;const n=t.charCodeAt(0);if(e===1)return Zi(n)?"\\":t;if(A(n))if(r=!0,A(t.charCodeAt(1))){let a=2,c=a;for(;a<e&&!A(t.charCodeAt(a));)a++;if(a<e&&a!==c){const h=t.slice(c,a);for(c=a;a<e&&A(t.charCodeAt(a));)a++;if(a<e&&a!==c){for(c=a;a<e&&!A(t.charCodeAt(a));)a++;if(a===e)return`\\\\${h}\\${t.slice(c)}\\`;a!==c&&(s=`\\\\${h}\\${t.slice(c,a)}`,i=a)}}}else i=1;else Lt(n)&&t.charCodeAt(1)===$t&&(s=t.slice(0,2),i=2,e>2&&A(t.charCodeAt(2))&&(r=!0,i=3));let o=i<e?ci(t.slice(i),!r,"\\",A):"";if(o.length===0&&!r&&(o="."),o.length>0&&A(t.charCodeAt(e-1))&&(o+="\\"),!r&&s===void 0&&t.includes(":")){if(o.length>=2&&Lt(o.charCodeAt(0))&&o.charCodeAt(1)===$t)return`.\\${o}`;let a=t.indexOf(":");do if(a===e-1||A(t.charCodeAt(a+1)))return`.\\${o}`;while((a=t.indexOf(":",a+1))!==-1)}return s===void 0?r?`\\${o}`:o:r?`${s}\\${o}`:`${s}${o}`},isAbsolute(t){J(t,"path");const e=t.length;if(e===0)return!1;const i=t.charCodeAt(0);return A(i)||e>2&&Lt(i)&&t.charCodeAt(1)===$t&&A(t.charCodeAt(2))},join(...t){if(t.length===0)return".";let e,i;for(let n=0;n<t.length;++n){const o=t[n];J(o,"path"),o.length>0&&(e===void 0?e=i=o:e+=`\\${o}`)}if(e===void 0)return".";let s=!0,r=0;if(typeof i=="string"&&A(i.charCodeAt(0))){++r;const n=i.length;n>1&&A(i.charCodeAt(1))&&(++r,n>2&&(A(i.charCodeAt(2))?++r:s=!1))}if(s){for(;r<e.length&&A(e.charCodeAt(r));)r++;r>=2&&(e=`\\${e.slice(r)}`)}return K.normalize(e)},relative(t,e){if(J(t,"from"),J(e,"to"),t===e)return"";const i=K.resolve(t),s=K.resolve(e);if(i===s||(t=i.toLowerCase(),e=s.toLowerCase(),t===e))return"";if(i.length!==t.length||s.length!==e.length){const p=i.split("\\"),g=s.split("\\");p[p.length-1]===""&&p.pop(),g[g.length-1]===""&&g.pop();const w=p.length,v=g.length,D=w<v?w:v;let T;for(T=0;T<D&&p[T].toLowerCase()===g[T].toLowerCase();T++);return T===0?s:T===D?v>D?g.slice(T).join("\\"):w>D?"..\\".repeat(w-1-T)+"..":"":"..\\".repeat(w-T)+g.slice(T).join("\\")}let r=0;for(;r<t.length&&t.charCodeAt(r)===ut;)r++;let n=t.length;for(;n-1>r&&t.charCodeAt(n-1)===ut;)n--;const o=n-r;let a=0;for(;a<e.length&&e.charCodeAt(a)===ut;)a++;let c=e.length;for(;c-1>a&&e.charCodeAt(c-1)===ut;)c--;const h=c-a,u=o<h?o:h;let l=-1,f=0;for(;f<u;f++){const p=t.charCodeAt(r+f);if(p!==e.charCodeAt(a+f))break;p===ut&&(l=f)}if(f!==u){if(l===-1)return s}else{if(h>u){if(e.charCodeAt(a+f)===ut)return s.slice(a+f+1);if(f===2)return s.slice(a+f)}o>u&&(t.charCodeAt(r+f)===ut?l=f:f===2&&(l=3)),l===-1&&(l=0)}let d="";for(f=r+l+1;f<=n;++f)(f===n||t.charCodeAt(f)===ut)&&(d+=d.length===0?"..":"\\..");return a+=l,d.length>0?`${d}${s.slice(a,c)}`:(s.charCodeAt(a)===ut&&++a,s.slice(a,c))},toNamespacedPath(t){if(typeof t!="string"||t.length===0)return t;const e=K.resolve(t);if(e.length<=2)return t;if(e.charCodeAt(0)===ut){if(e.charCodeAt(1)===ut){const i=e.charCodeAt(2);if(i!==Na&&i!==Xt)return`\\\\?\\UNC\\${e.slice(2)}`}}else if(Lt(e.charCodeAt(0))&&e.charCodeAt(1)===$t&&e.charCodeAt(2)===ut)return`\\\\?\\${e}`;return e},dirname(t){J(t,"path");const e=t.length;if(e===0)return".";let i=-1,s=0;const r=t.charCodeAt(0);if(e===1)return A(r)?t:".";if(A(r)){if(i=s=1,A(t.charCodeAt(1))){let a=2,c=a;for(;a<e&&!A(t.charCodeAt(a));)a++;if(a<e&&a!==c){for(c=a;a<e&&A(t.charCodeAt(a));)a++;if(a<e&&a!==c){for(c=a;a<e&&!A(t.charCodeAt(a));)a++;if(a===e)return t;a!==c&&(i=s=a+1)}}}}else Lt(r)&&t.charCodeAt(1)===$t&&(i=e>2&&A(t.charCodeAt(2))?3:2,s=i);let n=-1,o=!0;for(let a=e-1;a>=s;--a)if(A(t.charCodeAt(a))){if(!o){n=a;break}}else o=!1;if(n===-1){if(i===-1)return".";n=i}return t.slice(0,n)},basename(t,e){e!==void 0&&J(e,"suffix"),J(t,"path");let i=0,s=-1,r=!0,n;if(t.length>=2&&Lt(t.charCodeAt(0))&&t.charCodeAt(1)===$t&&(i=2),e!==void 0&&e.length>0&&e.length<=t.length){if(e===t)return"";let o=e.length-1,a=-1;for(n=t.length-1;n>=i;--n){const c=t.charCodeAt(n);if(A(c)){if(!r){i=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(c===e.charCodeAt(o)?--o===-1&&(s=n):(o=-1,s=a))}return i===s?s=a:s===-1&&(s=t.length),t.slice(i,s)}for(n=t.length-1;n>=i;--n)if(A(t.charCodeAt(n))){if(!r){i=n+1;break}}else s===-1&&(r=!1,s=n+1);return s===-1?"":t.slice(i,s)},extname(t){J(t,"path");let e=0,i=-1,s=0,r=-1,n=!0,o=0;t.length>=2&&t.charCodeAt(1)===$t&&Lt(t.charCodeAt(0))&&(e=s=2);for(let a=t.length-1;a>=e;--a){const c=t.charCodeAt(a);if(A(c)){if(!n){s=a+1;break}continue}r===-1&&(n=!1,r=a+1),c===Xt?i===-1?i=a:o!==1&&(o=1):i!==-1&&(o=-1)}return i===-1||r===-1||o===0||o===1&&i===r-1&&i===s+1?"":t.slice(i,r)},format:wr.bind(null,"\\"),parse(t){J(t,"path");const e={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return e;const i=t.length;let s=0,r=t.charCodeAt(0);if(i===1)return A(r)?(e.root=e.dir=t,e):(e.base=e.name=t,e);if(A(r)){if(s=1,A(t.charCodeAt(1))){let l=2,f=l;for(;l<i&&!A(t.charCodeAt(l));)l++;if(l<i&&l!==f){for(f=l;l<i&&A(t.charCodeAt(l));)l++;if(l<i&&l!==f){for(f=l;l<i&&!A(t.charCodeAt(l));)l++;l===i?s=l:l!==f&&(s=l+1)}}}}else if(Lt(r)&&t.charCodeAt(1)===$t){if(i<=2)return e.root=e.dir=t,e;if(s=2,A(t.charCodeAt(2))){if(i===3)return e.root=e.dir=t,e;s=3}}s>0&&(e.root=t.slice(0,s));let n=-1,o=s,a=-1,c=!0,h=t.length-1,u=0;for(;h>=s;--h){if(r=t.charCodeAt(h),A(r)){if(!c){o=h+1;break}continue}a===-1&&(c=!1,a=h+1),r===Xt?n===-1?n=h:u!==1&&(u=1):n!==-1&&(u=-1)}return a!==-1&&(n===-1||u===0||u===1&&n===a-1&&n===o+1?e.base=e.name=t.slice(o,a):(e.name=t.slice(o,n),e.base=t.slice(o,a),e.ext=t.slice(n,a))),o>0&&o!==s?e.dir=t.slice(0,o-1):e.dir=e.root,e},sep:"\\",delimiter:";",win32:null,posix:null},Fa=(()=>{if(ft){const t=/\\/g;return()=>{const e=ke().replace(t,"/");return e.slice(e.indexOf("/"))}}return()=>ke()})(),H={resolve(...t){let e="",i=!1;for(let s=t.length-1;s>=0&&!i;s--){const r=t[s];J(r,`paths[${s}]`),r.length!==0&&(e=`${r}/${e}`,i=r.charCodeAt(0)===it)}if(!i){const s=Fa();e=`${s}/${e}`,i=s.charCodeAt(0)===it}return e=ci(e,!i,"/",Zi),i?`/${e}`:e.length>0?e:"."},normalize(t){if(J(t,"path"),t.length===0)return".";const e=t.charCodeAt(0)===it,i=t.charCodeAt(t.length-1)===it;return t=ci(t,!e,"/",Zi),t.length===0?e?"/":i?"./":".":(i&&(t+="/"),e?`/${t}`:t)},isAbsolute(t){return J(t,"path"),t.length>0&&t.charCodeAt(0)===it},join(...t){if(t.length===0)return".";const e=[];for(let i=0;i<t.length;++i){const s=t[i];J(s,"path"),s.length>0&&e.push(s)}return e.length===0?".":H.normalize(e.join("/"))},relative(t,e){if(J(t,"from"),J(e,"to"),t===e||(t=H.resolve(t),e=H.resolve(e),t===e))return"";const i=1,s=t.length,r=s-i,n=1,o=e.length-n,a=r<o?r:o;let c=-1,h=0;for(;h<a;h++){const l=t.charCodeAt(i+h);if(l!==e.charCodeAt(n+h))break;l===it&&(c=h)}if(h===a)if(o>a){if(e.charCodeAt(n+h)===it)return e.slice(n+h+1);if(h===0)return e.slice(n+h)}else r>a&&(t.charCodeAt(i+h)===it?c=h:h===0&&(c=0));let u="";for(h=i+c+1;h<=s;++h)(h===s||t.charCodeAt(h)===it)&&(u+=u.length===0?"..":"/..");return`${u}${e.slice(n+c)}`},toNamespacedPath(t){return t},dirname(t){if(J(t,"path"),t.length===0)return".";const e=t.charCodeAt(0)===it;let i=-1,s=!0;for(let r=t.length-1;r>=1;--r)if(t.charCodeAt(r)===it){if(!s){i=r;break}}else s=!1;return i===-1?e?"/":".":e&&i===1?"//":t.slice(0,i)},basename(t,e){e!==void 0&&J(e,"suffix"),J(t,"path");let i=0,s=-1,r=!0,n;if(e!==void 0&&e.length>0&&e.length<=t.length){if(e===t)return"";let o=e.length-1,a=-1;for(n=t.length-1;n>=0;--n){const c=t.charCodeAt(n);if(c===it){if(!r){i=n+1;break}}else a===-1&&(r=!1,a=n+1),o>=0&&(c===e.charCodeAt(o)?--o===-1&&(s=n):(o=-1,s=a))}return i===s?s=a:s===-1&&(s=t.length),t.slice(i,s)}for(n=t.length-1;n>=0;--n)if(t.charCodeAt(n)===it){if(!r){i=n+1;break}}else s===-1&&(r=!1,s=n+1);return s===-1?"":t.slice(i,s)},extname(t){J(t,"path");let e=-1,i=0,s=-1,r=!0,n=0;for(let o=t.length-1;o>=0;--o){const a=t[o];if(a==="/"){if(!r){i=o+1;break}continue}s===-1&&(r=!1,s=o+1),a==="."?e===-1?e=o:n!==1&&(n=1):e!==-1&&(n=-1)}return e===-1||s===-1||n===0||n===1&&e===s-1&&e===i+1?"":t.slice(e,s)},format:wr.bind(null,"/"),parse(t){J(t,"path");const e={root:"",dir:"",base:"",ext:"",name:""};if(t.length===0)return e;const i=t.charCodeAt(0)===it;let s;i?(e.root="/",s=1):s=0;let r=-1,n=0,o=-1,a=!0,c=t.length-1,h=0;for(;c>=s;--c){const u=t.charCodeAt(c);if(u===it){if(!a){n=c+1;break}continue}o===-1&&(a=!1,o=c+1),u===Xt?r===-1?r=c:h!==1&&(h=1):r!==-1&&(h=-1)}if(o!==-1){const u=n===0&&i?1:n;r===-1||h===0||h===1&&r===o-1&&r===n+1?e.base=e.name=t.slice(u,o):(e.name=t.slice(u,r),e.base=t.slice(u,o),e.ext=t.slice(r,o))}return n>0?e.dir=t.slice(0,n-1):i&&(e.dir="/"),e},sep:"/",delimiter:":",win32:null,posix:null};H.win32=K.win32=K,H.posix=K.posix=H;var he=ft?K.normalize:H.normalize,yr=ft?K.isAbsolute:H.isAbsolute,O=ft?K.join:H.join,li=ft?K.resolve:H.resolve,Ua=ft?K.relative:H.relative,De=ft?K.dirname:H.dirname,ui=ft?K.basename:H.basename,Ou=ft?K.extname:H.extname,_u=ft?K.format:H.format,ja=ft?K.parse:H.parse,Ru=ft?K.toNamespacedPath:H.toNamespacedPath,Ae=ft?K.sep:H.sep,br=ft?K.delimiter:H.delimiter,Wa=/^\w[\w\d+.-]*$/,za=/^\//,Ha=/^\/\//;function qa(t,e){if(!t.scheme&&e)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${t.authority}", path: "${t.path}", query: "${t.query}", fragment: "${t.fragment}"}`);if(t.scheme&&!Wa.test(t.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(t.path){if(t.authority){if(!za.test(t.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(Ha.test(t.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function Va(t,e){return!t&&!e?"file":t}function Xa(t,e){switch(t){case"https":case"http":case"file":e?e[0]!==yt&&(e=yt+e):e=yt;break}return e}var X="",yt="/",Ga=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,j=class Ri{static isUri(e){return e instanceof Ri?!0:!e||typeof e!="object"?!1:typeof e.authority=="string"&&typeof e.fragment=="string"&&typeof e.path=="string"&&typeof e.query=="string"&&typeof e.scheme=="string"&&typeof e.fsPath=="string"&&typeof e.with=="function"&&typeof e.toString=="function"}constructor(e,i,s,r,n,o=!1){typeof e=="object"?(this.scheme=e.scheme||X,this.authority=e.authority||X,this.path=e.path||X,this.query=e.query||X,this.fragment=e.fragment||X):(this.scheme=Va(e,o),this.authority=i||X,this.path=Xa(this.scheme,s||X),this.query=r||X,this.fragment=n||X,qa(this,o))}get fsPath(){return fi(this,!1)}with(e){if(!e)return this;let{scheme:i,authority:s,path:r,query:n,fragment:o}=e;return i===void 0?i=this.scheme:i===null&&(i=X),s===void 0?s=this.authority:s===null&&(s=X),r===void 0?r=this.path:r===null&&(r=X),n===void 0?n=this.query:n===null&&(n=X),o===void 0?o=this.fragment:o===null&&(o=X),i===this.scheme&&s===this.authority&&r===this.path&&n===this.query&&o===this.fragment?this:new ce(i,s,r,n,o)}static parse(e,i=!1){const s=Ga.exec(e);return s?new ce(s[2]||X,di(s[4]||X),di(s[5]||X),di(s[7]||X),di(s[9]||X),i):new ce(X,X,X,X,X)}static file(e){let i=X;if(R&&(e=e.replace(/\\/g,yt)),e[0]===yt&&e[1]===yt){const s=e.indexOf(yt,2);s===-1?(i=e.substring(2),e=yt):(i=e.substring(2,s),e=e.substring(s)||yt)}return new ce("file",i,e,X,X)}static from(e,i){return new ce(e.scheme,e.authority,e.path,e.query,e.fragment,i)}static joinPath(e,...i){if(!e.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let s;return R&&e.scheme==="file"?s=Ri.file(K.join(fi(e,!0),...i)).path:s=H.join(e.path,...i),e.with({path:s})}toString(e=!1){return ts(this,e)}toJSON(){return this}static revive(e){if(e){if(e instanceof Ri)return e;{const i=new ce(e);return i._formatted=e.external??null,i._fsPath=e._sep===Cr?e.fsPath??null:null,i}}else return e}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},Cr=R?1:void 0,ce=class extends j{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=fi(this,!1)),this._fsPath}toString(t=!1){return t?ts(this,!0):(this._formatted||(this._formatted=ts(this,!1)),this._formatted)}toJSON(){const t={$mid:1};return this._fsPath&&(t.fsPath=this._fsPath,t._sep=Cr),this._formatted&&(t.external=this._formatted),this.path&&(t.path=this.path),this.scheme&&(t.scheme=this.scheme),this.authority&&(t.authority=this.authority),this.query&&(t.query=this.query),this.fragment&&(t.fragment=this.fragment),t}},Er={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function xr(t,e,i){let s,r=-1;for(let n=0;n<t.length;n++){const o=t.charCodeAt(n);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||e&&o===47||i&&o===91||i&&o===93||i&&o===58)r!==-1&&(s+=encodeURIComponent(t.substring(r,n)),r=-1),s!==void 0&&(s+=t.charAt(n));else{s===void 0&&(s=t.substr(0,n));const a=Er[o];a!==void 0?(r!==-1&&(s+=encodeURIComponent(t.substring(r,n)),r=-1),s+=a):r===-1&&(r=n)}}return r!==-1&&(s+=encodeURIComponent(t.substring(r))),s!==void 0?s:t}function Qa(t){let e;for(let i=0;i<t.length;i++){const s=t.charCodeAt(i);s===35||s===63?(e===void 0&&(e=t.substr(0,i)),e+=Er[s]):e!==void 0&&(e+=t[i])}return e!==void 0?e:t}function fi(t,e){let i;return t.authority&&t.path.length>1&&t.scheme==="file"?i=`//${t.authority}${t.path}`:t.path.charCodeAt(0)===47&&(t.path.charCodeAt(1)>=65&&t.path.charCodeAt(1)<=90||t.path.charCodeAt(1)>=97&&t.path.charCodeAt(1)<=122)&&t.path.charCodeAt(2)===58?e?i=t.path.substr(1):i=t.path[1].toLowerCase()+t.path.substr(2):i=t.path,R&&(i=i.replace(/\//g,"\\")),i}function ts(t,e){const i=e?Qa:xr;let s="",{scheme:r,authority:n,path:o,query:a,fragment:c}=t;if(r&&(s+=r,s+=":"),(n||r==="file")&&(s+=yt,s+=yt),n){let h=n.indexOf("@");if(h!==-1){const u=n.substr(0,h);n=n.substr(h+1),h=u.lastIndexOf(":"),h===-1?s+=i(u,!1,!1):(s+=i(u.substr(0,h),!1,!1),s+=":",s+=i(u.substr(h+1),!1,!0)),s+="@"}n=n.toLowerCase(),h=n.lastIndexOf(":"),h===-1?s+=i(n,!1,!0):(s+=i(n.substr(0,h),!1,!0),s+=n.substr(h))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const h=o.charCodeAt(1);h>=65&&h<=90&&(o=`/${String.fromCharCode(h+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const h=o.charCodeAt(0);h>=65&&h<=90&&(o=`${String.fromCharCode(h+32)}:${o.substr(2)}`)}s+=i(o,!0,!1)}return a&&(s+="?",s+=i(a,!1,!1)),c&&(s+="#",s+=e?c:xr(c,!1,!1)),s}function Sr(t){try{return decodeURIComponent(t)}catch{return t.length>3?t.substr(0,3)+Sr(t.substr(3)):t}}var Pr=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function di(t){return t.match(Pr)?t.replace(Pr,e=>Sr(e)):t}var Ya=new class{transformIncoming(t){return t}transformOutgoing(t){return t}transformOutgoingURI(t){return t}transformOutgoingScheme(t){return t}},Nu=class Ni{static{this.Undefined=new Ni(void 0)}constructor(e){this.element=e,this.next=Ni.Undefined,this.prev=Ni.Undefined}},Ja=globalThis.performance.now.bind(globalThis.performance),Ka=class K1{static create(e){return new K1(e)}constructor(e){this.c=e===!1?Date.now:Ja,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},$r=!1,Za=!1,Y;(function(t){t.None=()=>V.None;function e(b){if(Za){const{onDidAddListener:m}=b,S=es.create();let C=0;b.onDidAddListener=()=>{++C===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),S.print()),m?.()}}}function i(b,m){return d(b,()=>{},0,void 0,!0,void 0,m)}t.defer=i;function s(b){return(m,S=null,C)=>{let L=!1,M;return M=b(U=>{if(!L)return M?M.dispose():L=!0,m.call(S,U)},null,C),L&&M.dispose(),M}}t.once=s;function r(b,m){return t.once(t.filter(b,m))}t.onceIf=r;function n(b,m,S){return l((C,L=null,M)=>b(U=>C.call(L,m(U)),null,M),S)}t.map=n;function o(b,m,S){return l((C,L=null,M)=>b(U=>{m(U),C.call(L,U)},null,M),S)}t.forEach=o;function a(b,m,S){return l((C,L=null,M)=>b(U=>m(U)&&C.call(L,U),null,M),S)}t.filter=a;function c(b){return b}t.signal=c;function h(...b){return(m,S=null,C)=>{const L=ia(...b.map(M=>M(U=>m.call(S,U))));return f(L,C)}}t.any=h;function u(b,m,S,C){let L=S;return n(b,M=>(L=m(L,M),L),C)}t.reduce=u;function l(b,m){let S;const C={onWillAddFirstListener(){S=b(L.fire,L)},onDidRemoveLastListener(){S?.dispose()}};m||e(C);const L=new x(C);return m?.add(L),L.event}function f(b,m){return m instanceof Array?m.push(b):m&&m.add(b),b}function d(b,m,S=100,C=!1,L=!1,M,U){let et,rt,zt,si=0,be;const qs={leakWarningThreshold:M,onWillAddFirstListener(){et=b(wo=>{si++,rt=m(rt,wo),C&&!zt&&(ri.fire(rt),rt=void 0),be=()=>{const yo=rt;rt=void 0,zt=void 0,(!C||si>1)&&ri.fire(yo),si=0},typeof S=="number"?(zt&&clearTimeout(zt),zt=setTimeout(be,S)):zt===void 0&&(zt=null,queueMicrotask(be))})},onWillRemoveListener(){L&&si>0&&be?.()},onDidRemoveLastListener(){be=void 0,et.dispose()}};U||e(qs);const ri=new x(qs);return U?.add(ri),ri.event}t.debounce=d;function p(b,m=0,S){return t.debounce(b,(C,L)=>C?(C.push(L),C):[L],m,void 0,!0,void 0,S)}t.accumulate=p;function g(b,m=(C,L)=>C===L,S){let C=!0,L;return a(b,M=>{const U=C||!m(M,L);return C=!1,L=M,U},S)}t.latch=g;function w(b,m,S){return[t.filter(b,m,S),t.filter(b,C=>!m(C),S)]}t.split=w;function v(b,m=!1,S=[],C){let L=S.slice(),M=b(rt=>{L?L.push(rt):et.fire(rt)});C&&C.add(M);const U=()=>{L?.forEach(rt=>et.fire(rt)),L=null},et=new x({onWillAddFirstListener(){M||(M=b(rt=>et.fire(rt)),C&&C.add(M))},onDidAddFirstListener(){L&&(m?setTimeout(U):U())},onDidRemoveLastListener(){M&&M.dispose(),M=null}});return C&&C.add(et),et.event}t.buffer=v;function D(b,m){return(C,L,M)=>{const U=m(new ie);return b(function(et){const rt=U.evaluate(et);rt!==T&&C.call(L,rt)},void 0,M)}}t.chain=D;const T=Symbol("HaltChainable");class ie{constructor(){this.f=[]}map(m){return this.f.push(m),this}forEach(m){return this.f.push(S=>(m(S),S)),this}filter(m){return this.f.push(S=>m(S)?S:T),this}reduce(m,S){let C=S;return this.f.push(L=>(C=m(C,L),C)),this}latch(m=(S,C)=>S===C){let S=!0,C;return this.f.push(L=>{const M=S||!m(L,C);return S=!1,C=L,M?L:T}),this}evaluate(m){for(const S of this.f)if(m=S(m),m===T)break;return m}}function jt(b,m,S=C=>C){const C=(...et)=>U.fire(S(...et)),L=()=>b.on(m,C),M=()=>b.removeListener(m,C),U=new x({onWillAddFirstListener:L,onDidRemoveLastListener:M});return U.event}t.fromNodeEventEmitter=jt;function at(b,m,S=C=>C){const C=(...et)=>U.fire(S(...et)),L=()=>b.addEventListener(m,C),M=()=>b.removeEventListener(m,C),U=new x({onWillAddFirstListener:L,onDidRemoveLastListener:M});return U.event}t.fromDOMEventEmitter=at;function P(b,m){let S;const C=new Promise((L,M)=>{const U=s(b)(L,null,m);S=()=>U.dispose()});return C.cancel=S,C}t.toPromise=P;function $(b){const m=new x;return b.then(S=>{m.fire(S)},()=>{m.fire(void 0)}).finally(()=>{m.dispose()}),m.event}t.fromPromise=$;function E(b,m){return b(S=>m.fire(S))}t.forward=E;function N(b,m,S){return m(S),b(C=>m(C))}t.runAndSubscribe=N;class st{constructor(m,S){this._observable=m,this.f=0,this.g=!1;const C={onWillAddFirstListener:()=>{m.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{m.removeObserver(this)}};S||e(C),this.emitter=new x(C),S&&S.add(this.emitter)}beginUpdate(m){this.f++}handlePossibleChange(m){}handleChange(m,S){this.g=!0}endUpdate(m){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function F(b,m){return new st(b,m).emitter.event}t.fromObservable=F;function Wt(b){return(m,S,C)=>{let L=0,M=!1;const U={beginUpdate(){L++},endUpdate(){L--,L===0&&(b.reportChanges(),M&&(M=!1,m.call(S)))},handlePossibleChange(){},handleChange(){M=!0}};b.addObserver(U),b.reportChanges();const et={dispose(){b.removeObserver(U)}};return C instanceof qt?C.add(et):Array.isArray(C)&&C.push(et),et}}t.fromObservableLight=Wt})(Y||(Y={}));var th=class js{static{this.all=new Set}static{this.f=0}constructor(e){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${e}_${js.f++}`,js.all.add(this)}start(e){this.g=new Ka,this.listenerCount=e}stop(){if(this.g){const e=this.g.elapsed();this.durations.push(e),this.elapsedOverall+=e,this.invocationCount+=1,this.g=void 0}}},Lr=-1,eh=class Z1{static{this.f=1}constructor(e,i,s=(Z1.f++).toString(16).padStart(3,"0")){this.j=e,this.threshold=i,this.name=s,this.h=0}dispose(){this.g?.clear()}check(e,i){const s=this.threshold;if(s<=0||i<s)return;this.g||(this.g=new Map);const r=this.g.get(e.value)||0;if(this.g.set(e.value,r+1),this.h-=1,this.h<=0){this.h=s*.5;const[n,o]=this.getMostFrequentStack(),a=`[${this.name}] potential listener LEAK detected, having ${i} listeners already. MOST frequent listener (${o}):`;console.warn(a),console.warn(n);const c=new ih(a,n);this.j(c)}return()=>{const n=this.g.get(e.value)||0;this.g.set(e.value,n-1)}}getMostFrequentStack(){if(!this.g)return;let e,i=0;for(const[s,r]of this.g)(!e||i<r)&&(e=[s,r],i=r);return e}},es=class to{static create(){const e=new Error;return new to(e.stack??"")}constructor(e){this.value=e}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},ih=class extends Error{constructor(t,e){super(t),this.name="ListenerLeakError",this.stack=e}},sh=class extends Error{constructor(t,e){super(t),this.name="ListenerRefusalError",this.stack=e}},rh=0,pi=class{constructor(t){this.value=t,this.id=rh++}},nh=2,oh=(t,e)=>{if(t instanceof pi)e(t);else for(let i=0;i<t.length;i++){const s=t[i];s&&e(s)}},x=class{constructor(t){this.A=0,this.g=t,this.j=Lr>0||this.g?.leakWarningThreshold?new eh(t?.onListenerError??ni,this.g?.leakWarningThreshold??Lr):void 0,this.m=this.g?._profName?new th(this.g._profName):void 0,this.z=this.g?.deliveryQueue}dispose(){if(!this.q){if(this.q=!0,this.z?.current===this&&this.z.reset(),this.w){if($r){const t=this.w;queueMicrotask(()=>{oh(t,e=>e.stack?.print())})}this.w=void 0,this.A=0}this.g?.onDidRemoveLastListener?.(),this.j?.dispose()}}get event(){return this.u??=(t,e,i)=>{if(this.j&&this.A>this.j.threshold**2){const a=`[${this.j.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.A} vs ${this.j.threshold})`;console.warn(a);const c=this.j.getMostFrequentStack()??["UNKNOWN stack",-1],h=new sh(`${a}. HINT: Stack shows most frequent listener (${c[1]}-times)`,c[0]);return(this.g?.onListenerError||ni)(h),V.None}if(this.q)return V.None;e&&(t=t.bind(e));const s=new pi(t);let r,n;this.j&&this.A>=Math.ceil(this.j.threshold*.2)&&(s.stack=es.create(),r=this.j.check(s.stack,this.A+1)),$r&&(s.stack=n??es.create()),this.w?this.w instanceof pi?(this.z??=new ah,this.w=[this.w,s]):this.w.push(s):(this.g?.onWillAddFirstListener?.(this),this.w=s,this.g?.onDidAddFirstListener?.(this)),this.g?.onDidAddListener?.(this),this.A++;const o=wt(()=>{r?.(),this.B(s)});return i instanceof qt?i.add(o):Array.isArray(i)&&i.push(o),o},this.u}B(t){if(this.g?.onWillRemoveListener?.(this),!this.w)return;if(this.A===1){this.w=void 0,this.g?.onDidRemoveLastListener?.(this),this.A=0;return}const e=this.w,i=e.indexOf(t);if(i===-1)throw console.log("disposed?",this.q),console.log("size?",this.A),console.log("arr?",JSON.stringify(this.w)),new Error("Attempted to dispose unknown listener");this.A--,e[i]=void 0;const s=this.z.current===this;if(this.A*nh<=e.length){let r=0;for(let n=0;n<e.length;n++)e[n]?e[r++]=e[n]:s&&r<this.z.end&&(this.z.end--,r<this.z.i&&this.z.i--);e.length=r}}C(t,e){if(!t)return;const i=this.g?.onListenerError||ni;if(!i){t.value(e);return}try{t.value(e)}catch(s){i(s)}}D(t){const e=t.current.w;for(;t.i<t.end;)this.C(e[t.i++],t.value);t.reset()}fire(t){if(this.z?.current&&(this.D(this.z),this.m?.stop()),this.m?.start(this.A),this.w)if(this.w instanceof pi)this.C(this.w,t);else{const e=this.z;e.enqueue(this,t,this.w.length),this.D(e)}this.m?.stop()}hasListeners(){return this.A>0}},ah=class{constructor(){this.i=-1,this.end=0}enqueue(t,e,i){this.i=0,this.end=i,this.current=t,this.value=e}reset(){this.i=this.end,this.current=void 0,this.value=void 0}},hh=class{constructor(){this.g=!1,this.h=[],this.f=new x({onWillAddFirstListener:()=>this.j(),onDidRemoveLastListener:()=>this.k()})}get event(){return this.f.event}add(t){const e={event:t,listener:null};return this.h.push(e),this.g&&this.m(e),wt(Hi(()=>{this.g&&this.o(e);const s=this.h.indexOf(e);this.h.splice(s,1)}))}j(){this.g=!0,this.h.forEach(t=>this.m(t))}k(){this.g=!1,this.h.forEach(t=>this.o(t))}m(t){t.listener=t.event(e=>this.f.fire(e))}o(t){t.listener?.dispose(),t.listener=null}dispose(){this.f.dispose();for(const t of this.h)t.listener?.dispose();this.h=[]}},ch=class{constructor(){this.f=!1,this.g=Y.None,this.h=V.None,this.j=new x({onDidAddFirstListener:()=>{this.f=!0,this.h=this.g(this.j.fire,this.j)},onDidRemoveLastListener:()=>{this.f=!1,this.h.dispose()}}),this.event=this.j.event}set input(t){this.g=t,this.f&&(this.h.dispose(),this.h=t(this.j.fire,this.j))}dispose(){this.h.dispose(),this.j.dispose()}},kr=Object.freeze(function(t,e){const i=setTimeout(t.bind(e),0);return{dispose(){clearTimeout(i)}}}),Me;(function(t){function e(i){return i===t.None||i===t.Cancelled||i instanceof gi?!0:!i||typeof i!="object"?!1:typeof i.isCancellationRequested=="boolean"&&typeof i.onCancellationRequested=="function"}t.isCancellationToken=e,t.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Y.None}),t.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:kr})})(Me||(Me={}));var gi=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?kr:(this.b||(this.b=new x),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},is=class{constructor(t){this.f=void 0,this.g=void 0,this.g=t&&t.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new gi),this.f}cancel(){this.f?this.f instanceof gi&&this.f.cancel():this.f=Me.Cancelled}dispose(t=!1){t&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof gi&&this.f.dispose():this.f=Me.None}};function lh(t){return t}var uh=class{constructor(t,e){this.a=void 0,this.b=void 0,typeof t=="function"?(this.c=t,this.d=lh):(this.c=e,this.d=t.getCacheKey)}get(t){const e=this.d(t);return this.b!==e&&(this.b=e,this.a=this.c(t)),this.a}};function fh(t){return!t||typeof t!="string"?!0:t.trim().length===0}var dh=/{(\d+)}/g;function le(t,...e){return e.length===0?t:t.replace(dh,function(i,s){const r=parseInt(s,10);return isNaN(r)||r<0||r>=e.length?i:e[r]})}function ph(t,e){if(!t||!e)return t;const i=e.length,s=t.length;if(i===0||s===0)return t;let r=s,n=-1;for(;n=t.lastIndexOf(e,r-1),!(n===-1||n+i!==r);){if(n===0)return"";r=n}return t.substring(0,r)}function ss(t,e){return t<e?-1:t>e?1:0}function rs(t,e,i=0,s=t.length,r=0,n=e.length){for(;i<s&&r<n;i++,r++){const c=t.charCodeAt(i),h=e.charCodeAt(r);if(c<h)return-1;if(c>h)return 1}const o=s-i,a=n-r;return o<a?-1:o>a?1:0}function Dr(t,e){return Ie(t,e,0,t.length,0,e.length)}function Ie(t,e,i=0,s=t.length,r=0,n=e.length){for(;i<s&&r<n;i++,r++){let c=t.charCodeAt(i),h=e.charCodeAt(r);if(c===h)continue;if(c>=128||h>=128)return rs(t.toLowerCase(),e.toLowerCase(),i,s,r,n);Ar(c)&&(c-=32),Ar(h)&&(h-=32);const u=c-h;if(u!==0)return u}const o=s-i,a=n-r;return o<a?-1:o>a?1:0}function Ar(t){return t>=97&&t<=122}function Mr(t){return t>=65&&t<=90}function gh(t,e){return t.length===e.length&&Ie(t,e)===0}function mh(t,e){const i=e.length;return e.length>t.length?!1:Ie(t,e,0,i)===0}function vh(t){return 55296<=t&&t<=56319}function Ir(t){return 56320<=t&&t<=57343}function wh(t,e){return(t-55296<<10)+(e-56320)+65536}var yh=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,bh=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,Ch=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,Eh=new RegExp("(?:"+[yh.source,bh.source,Ch.source].join("|")+")","g");function xh(t){return t&&(t=t.replace(Eh,"")),t}var Sh=/\\\[.*?\\\]/g;function Ph(t){return xh(t).replace(Sh,"")}var Tu="\uFEFF",Or;(function(t){t[t.Other=0]="Other",t[t.Prepend=1]="Prepend",t[t.CR=2]="CR",t[t.LF=3]="LF",t[t.Control=4]="Control",t[t.Extend=5]="Extend",t[t.Regional_Indicator=6]="Regional_Indicator",t[t.SpacingMark=7]="SpacingMark",t[t.L=8]="L",t[t.V=9]="V",t[t.T=10]="T",t[t.LV=11]="LV",t[t.LVT=12]="LVT",t[t.ZWJ=13]="ZWJ",t[t.Extended_Pictographic=14]="Extended_Pictographic"})(Or||(Or={}));var Bu=class Ke{static{this.c=null}static getInstance(){return Ke.c||(Ke.c=new Ke),Ke.c}constructor(){this.d=$h()}getGraphemeBreakType(e){if(e<32)return e===10?3:e===13?2:4;if(e<127)return 0;const i=this.d,s=i.length/3;let r=1;for(;r<=s;)if(e<i[3*r])r=2*r;else if(e>i[3*r+1])r=2*r+1;else return i[3*r+2];return 0}};function $h(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var _r;(function(t){t[t.zwj=8205]="zwj",t[t.emojiVariantSelector=65039]="emojiVariantSelector",t[t.enclosingKeyCap=8419]="enclosingKeyCap",t[t.space=32]="space"})(_r||(_r={}));var Fu=class Ze{static{this.c=new se(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new uh({getCacheKey:JSON.stringify},e=>{function i(u){const l=new Map;for(let f=0;f<u.length;f+=2)l.set(u[f],u[f+1]);return l}function s(u,l){const f=new Map(u);for(const[d,p]of l)f.set(d,p);return f}function r(u,l){if(!u)return l;const f=new Map;for(const[d,p]of u)l.has(d)&&f.set(d,p);return f}const n=this.c.value;let o=e.filter(u=>!u.startsWith("_")&&u in n);o.length===0&&(o=["_default"]);let a;for(const u of o){const l=i(n[u]);a=r(a,l)}const c=i(n._common),h=s(c,a);return new Ze(h)})}static getInstance(e){return Ze.d.get(Array.from(e))}static{this.e=new se(()=>Object.keys(Ze.c.value).filter(e=>!e.startsWith("_")))}static getLocales(){return Ze.e.value}constructor(e){this.f=e}isAmbiguous(e){return this.f.has(e)}containsAmbiguousCharacter(e){for(let i=0;i<e.length;i++){const s=e.codePointAt(i);if(typeof s=="number"&&this.isAmbiguous(s))return!0}return!1}getPrimaryConfusable(e){return this.f.get(e)}getConfusableCodePoints(){return new Set(this.f.keys())}},Uu=class ti{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(ti.c())].flat())),this.d}static isInvisibleCharacter(e){return ti.e().has(e)}static containsInvisibleCharacter(e){for(let i=0;i<e.length;i++){const s=e.codePointAt(i);if(typeof s=="number"&&(ti.isInvisibleCharacter(s)||s===32))return!0}return!1}static get codePoints(){return ti.e()}};function Bt(t){return t===47||t===92}function Rr(t){return t.replace(/[\\/]/g,H.sep)}function Lh(t){return t.indexOf("/")===-1&&(t=Rr(t)),/^[a-zA-Z]:(\/|$)/.test(t)&&(t="/"+t),t}function Nr(t,e=H.sep){if(!t)return"";const i=t.length,s=t.charCodeAt(0);if(Bt(s)){if(Bt(t.charCodeAt(1))&&!Bt(t.charCodeAt(2))){let n=3;const o=n;for(;n<i&&!Bt(t.charCodeAt(n));n++);if(o!==n&&!Bt(t.charCodeAt(n+1))){for(n+=1;n<i;n++)if(Bt(t.charCodeAt(n)))return t.slice(0,n+1).replace(/[\\/]/g,e)}}return e}else if(Tr(s)&&t.charCodeAt(1)===58)return Bt(t.charCodeAt(2))?t.slice(0,2)+e:t.slice(0,2);let r=t.indexOf("://");if(r!==-1){for(r+=3;r<i;r++)if(Bt(t.charCodeAt(r)))return t.slice(0,r+1)}return""}function ns(t,e,i,s=Ae){if(t===e)return!0;if(!t||!e||e.length>t.length)return!1;if(i){if(!mh(t,e))return!1;if(e.length===t.length)return!0;let n=e.length;return e.charAt(e.length-1)===s&&n--,t.charAt(n)===s}return e.charAt(e.length-1)!==s&&(e+=s),t.indexOf(e)===0}function Tr(t){return t>=65&&t<=90||t>=97&&t<=122}function Br(t){const e=he(t);return R?t.length>3?!1:kh(e)&&(t.length===2||e.charCodeAt(2)===92):e===H.sep}function kh(t,e=R){return e?Tr(t.charCodeAt(0))&&t.charCodeAt(1)===58:!1}var Dh="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",Ah="BDEFGHIJKMOQRSTUVWXYZbdefghijkmoqrstuvwxyz0123456789";function Mh(t,e,i=8){let s="";for(let n=0;n<i;n++){let o;n===0&&R&&!e&&(i===3||i===4)?o=Ah:o=Dh,s+=o.charAt(Math.floor(Math.random()*o.length))}let r;return e?r=`${e}-${s}`:r=s,t?O(t,r):r}var Z;(function(t){t.inMemory="inmemory",t.vscode="vscode",t.internal="private",t.walkThrough="walkThrough",t.walkThroughSnippet="walkThroughSnippet",t.http="http",t.https="https",t.file="file",t.mailto="mailto",t.untitled="untitled",t.data="data",t.command="command",t.vscodeRemote="vscode-remote",t.vscodeRemoteResource="vscode-remote-resource",t.vscodeManagedRemoteResource="vscode-managed-remote-resource",t.vscodeUserData="vscode-userdata",t.vscodeCustomEditor="vscode-custom-editor",t.vscodeNotebookCell="vscode-notebook-cell",t.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",t.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",t.vscodeNotebookCellOutput="vscode-notebook-cell-output",t.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",t.vscodeNotebookMetadata="vscode-notebook-metadata",t.vscodeInteractiveInput="vscode-interactive-input",t.vscodeSettings="vscode-settings",t.vscodeWorkspaceTrust="vscode-workspace-trust",t.vscodeTerminal="vscode-terminal",t.vscodeChatCodeBlock="vscode-chat-code-block",t.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",t.vscodeChatEditor="vscode-chat-editor",t.vscodeChatInput="chatSessionInput",t.vscodeChatSession="vscode-chat-session",t.webviewPanel="webview-panel",t.vscodeWebview="vscode-webview",t.extension="extension",t.vscodeFileResource="vscode-file",t.tmp="tmp",t.vsls="vsls",t.vscodeSourceControl="vscode-scm",t.commentsInput="comment",t.codeSetting="code-setting",t.outputChannel="output",t.accessibleView="accessible-view"})(Z||(Z={}));var Ih="tkn",Oh=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(t){this.d=t}setDelegate(t){this.e=t}setServerRootPath(t,e){this.f=H.join(e??"/",Rh(t))}getServerRootPath(){return this.f}get g(){return H.join(this.f,Z.vscodeRemoteResource)}set(t,e,i){this.a[t]=e,this.b[t]=i}setConnectionToken(t,e){this.c[t]=e}getPreferredWebSchema(){return this.d}rewrite(t){if(this.e)try{return this.e(t)}catch(o){return ni(o),t}const e=t.authority;let i=this.a[e];i&&i.indexOf(":")!==-1&&i.indexOf("[")===-1&&(i=`[${i}]`);const s=this.b[e],r=this.c[e];let n=`path=${encodeURIComponent(t.path)}`;return typeof r=="string"&&(n+=`&${Ih}=${encodeURIComponent(r)}`),j.from({scheme:Ji?this.d:Z.vscodeRemoteResource,authority:`${i}:${s}`,path:this.g,query:n})}},_h=new Oh;function Rh(t){return`${t.quality??"oss"}-${t.commit??"dev"}`}var Nh="vscode-app",Th=class Ti{static{this.a=Nh}asBrowserUri(e){const i=this.b(e);return this.uriToBrowserUri(i)}uriToBrowserUri(e){return e.scheme===Z.vscodeRemote?_h.rewrite(e):e.scheme===Z.file&&(xa||Pa===`${Z.vscodeFileResource}://${Ti.a}`)?e.with({scheme:Z.vscodeFileResource,authority:e.authority||Ti.a,query:null,fragment:null}):e}asFileUri(e){const i=this.b(e);return this.uriToFileUri(i)}uriToFileUri(e){return e.scheme===Z.vscodeFileResource?e.with({scheme:Z.file,authority:e.authority!==Ti.a?e.authority:null,query:null,fragment:null}):e}b(e){if(j.isUri(e))return e;if(globalThis._VSCODE_FILE_ROOT){const i=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(i))return j.joinPath(j.parse(i,!0),e);const s=O(i,e);return j.file(s)}throw new Error("Cannot determine URI for module id!")}},Oe=new Th,ju=Object.freeze({"Cache-Control":"no-cache, no-store"}),Wu=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),Fr;(function(t){const e=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);t.CoopAndCoep=Object.freeze(e.get("3"));const i="vscode-coi";function s(n){let o;typeof n=="string"?o=new URL(n).searchParams:n instanceof URL?o=n.searchParams:j.isUri(n)&&(o=new URL(n.toString(!0)).searchParams);const a=o?.get(i);if(a)return e.get(a)}t.getHeadersFromQuery=s;function r(n,o,a){if(!globalThis.crossOriginIsolated)return;const c=o&&a?"3":a?"2":"1";n instanceof URLSearchParams?n.set(i,c):n[i]=c}t.addSearchParam=r})(Fr||(Fr={}));function kt(t){return fi(t,!0)}var os=class{constructor(t){this.a=t}compare(t,e,i=!1){return t===e?0:ss(this.getComparisonKey(t,i),this.getComparisonKey(e,i))}isEqual(t,e,i=!1){return t===e?!0:!t||!e?!1:this.getComparisonKey(t,i)===this.getComparisonKey(e,i)}getComparisonKey(t,e=!1){return t.with({path:this.a(t)?t.path.toLowerCase():void 0,fragment:e?null:void 0}).toString()}ignorePathCasing(t){return this.a(t)}isEqualOrParent(t,e,i=!1){if(t.scheme===e.scheme){if(t.scheme===Z.file)return ns(kt(t),kt(e),this.a(t))&&t.query===e.query&&(i||t.fragment===e.fragment);if(Ur(t.authority,e.authority))return ns(t.path,e.path,this.a(t),"/")&&t.query===e.query&&(i||t.fragment===e.fragment)}return!1}joinPath(t,...e){return j.joinPath(t,...e)}basenameOrAuthority(t){return Fh(t)||t.authority}basename(t){return H.basename(t.path)}extname(t){return H.extname(t.path)}dirname(t){if(t.path.length===0)return t;let e;return t.scheme===Z.file?e=j.file(De(kt(t))).path:(e=H.dirname(t.path),t.authority&&e.length&&e.charCodeAt(0)!==47&&(console.error(`dirname("${t.toString})) resulted in a relative path`),e="/")),t.with({path:e})}normalizePath(t){if(!t.path.length)return t;let e;return t.scheme===Z.file?e=j.file(he(kt(t))).path:e=H.normalize(t.path),t.with({path:e})}relativePath(t,e){if(t.scheme!==e.scheme||!Ur(t.authority,e.authority))return;if(t.scheme===Z.file){const r=Ua(kt(t),kt(e));return R?Rr(r):r}let i=t.path||"/";const s=e.path||"/";if(this.a(t)){let r=0;for(const n=Math.min(i.length,s.length);r<n&&!(i.charCodeAt(r)!==s.charCodeAt(r)&&i.charAt(r).toLowerCase()!==s.charAt(r).toLowerCase());r++);i=s.substr(0,r)+i.substr(r)}return H.relative(i,s)}resolvePath(t,e){if(t.scheme===Z.file){const i=j.file(li(kt(t),e));return t.with({authority:i.authority,path:i.path})}return e=Lh(e),t.with({path:H.resolve(t.path,e)})}isAbsolutePath(t){return!!t.path&&t.path[0]==="/"}isEqualAuthority(t,e){return t===e||t!==void 0&&e!==void 0&&gh(t,e)}hasTrailingPathSeparator(t,e=Ae){if(t.scheme===Z.file){const i=kt(t);return i.length>Nr(i).length&&i[i.length-1]===e}else{const i=t.path;return i.length>1&&i.charCodeAt(i.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(t.fsPath)}}removeTrailingPathSeparator(t,e=Ae){return jr(t,e)?t.with({path:t.path.substr(0,t.path.length-1)}):t}addTrailingPathSeparator(t,e=Ae){let i=!1;if(t.scheme===Z.file){const s=kt(t);i=s!==void 0&&s.length===Nr(s).length&&s[s.length-1]===e}else{e="/";const s=t.path;i=s.length===1&&s.charCodeAt(s.length-1)===47}return!i&&!jr(t,e)?t.with({path:t.path+"/"}):t}},B=new os(()=>!1),Bh=new os(t=>t.scheme===Z.file?!Le:!0),zu=new os(t=>!0),Hu=B.isEqual.bind(B),qu=B.isEqualOrParent.bind(B),Vu=B.getComparisonKey.bind(B),Xu=B.basenameOrAuthority.bind(B),Fh=B.basename.bind(B),Gu=B.extname.bind(B),Qu=B.dirname.bind(B),Ct=B.joinPath.bind(B),Yu=B.normalizePath.bind(B),Ju=B.relativePath.bind(B),Ku=B.resolvePath.bind(B),Zu=B.isAbsolutePath.bind(B),Ur=B.isEqualAuthority.bind(B),jr=B.hasTrailingPathSeparator.bind(B),t0=B.removeTrailingPathSeparator.bind(B),e0=B.addTrailingPathSeparator.bind(B),Wr;(function(t){t.META_DATA_LABEL="label",t.META_DATA_DESCRIPTION="description",t.META_DATA_SIZE="size",t.META_DATA_MIME="mime";function e(i){const s=new Map;i.path.substring(i.path.indexOf(";")+1,i.path.lastIndexOf(";")).split(";").forEach(o=>{const[a,c]=o.split(":");a&&c&&s.set(a,c)});const n=i.path.substring(0,i.path.indexOf(";"));return n&&s.set(t.META_DATA_MIME,n),s}t.parseMetaData=e})(Wr||(Wr={}));var i0=Symbol("MicrotaskDelay");function as(t){const e=new is,i=t(e.token);let s=!1;const r=new Promise((n,o)=>{const a=e.token.onCancellationRequested(()=>{s=!0,a.dispose(),o(new _t)});Promise.resolve(i).then(c=>{a.dispose(),e.dispose(),s?ea(c)&&c.dispose():n(c)},c=>{a.dispose(),e.dispose(),o(c)})});return new class{cancel(){e.cancel(),e.dispose()}then(n,o){return r.then(n,o)}catch(n){return this.then(void 0,n)}finally(n){return r.finally(n)}}}var Uh=class{constructor(){this.a=!1,this.b=new Promise((t,e)=>{this.d=t})}isOpen(){return this.a}open(){this.a=!0,this.d(!0)}wait(){return this.b}},jh=class extends Uh{constructor(t){super(),this.f=setTimeout(()=>this.open(),t)}open(){clearTimeout(this.f),super.open()}};function Ft(t,e){return e?new Promise((i,s)=>{const r=setTimeout(()=>{n.dispose(),i()},t),n=e.onCancellationRequested(()=>{clearTimeout(r),n.dispose(),s(new _t)})}):as(i=>Ft(t,i))}var Wh=class{constructor(t){this.a=0,this.b=!1,this.f=t,this.g=[],this.d=0,this.h=new x}whenIdle(){return this.size>0?Y.toPromise(this.onDrained):Promise.resolve()}get onDrained(){return this.h.event}get size(){return this.a}queue(t){if(this.b)throw new Error("Object has been disposed");return this.a++,new Promise((e,i)=>{this.g.push({factory:t,c:e,e:i}),this.j()})}j(){for(;this.g.length&&this.d<this.f;){const t=this.g.shift();this.d++;const e=t.factory();e.then(t.c,t.e),e.then(()=>this.k(),()=>this.k())}}k(){this.b||(this.d--,--this.a===0&&this.h.fire(),this.g.length>0&&this.j())}clear(){if(this.b)throw new Error("Object has been disposed");this.g.length=0,this.a=this.d}dispose(){this.b=!0,this.g.length=0,this.a=0,this.h.dispose()}},zr=class extends Wh{constructor(){super(1)}},zh=class{constructor(){this.a=new Map,this.b=new Set,this.d=void 0,this.f=0}async whenDrained(){if(this.g())return;const t=new Vh;return this.b.add(t),t.p}g(){for(const[,t]of this.a)if(t.size>0)return!1;return!0}queueSize(t,e=B){const i=e.getComparisonKey(t);return this.a.get(i)?.size??0}queueFor(t,e,i=B){const s=i.getComparisonKey(t);let r=this.a.get(s);if(!r){r=new zr;const n=this.f++,o=Y.once(r.onDrained)(()=>{r?.dispose(),this.a.delete(s),this.h(),this.d?.deleteAndDispose(n),this.d?.size===0&&(this.d.dispose(),this.d=void 0)});this.d||(this.d=new ra),this.d.set(n,o),this.a.set(s,r)}return r.queue(e)}h(){this.g()&&this.j()}j(){for(const t of this.b)t.complete();this.b.clear()}dispose(){for(const[,t]of this.a)t.dispose();this.a.clear(),this.j(),this.d?.dispose()}},Hh=class{constructor(t,e){this.b=void 0,this.a=t,this.d=e,this.f=this.g.bind(this)}dispose(){this.cancel(),this.a=null}cancel(){this.isScheduled()&&(clearTimeout(this.b),this.b=void 0)}schedule(t=this.d){this.cancel(),this.b=setTimeout(this.f,t)}get delay(){return this.d}set delay(t){this.d=t}isScheduled(){return this.b!==void 0}flush(){this.isScheduled()&&(this.cancel(),this.h())}g(){this.b=void 0,this.a&&this.h()}h(){this.a?.()}},Hr=class{constructor(t,e){e%1e3!==0&&console.warn(`ProcessTimeRunOnceScheduler resolution is 1s, ${e}ms is not a multiple of 1000ms.`),this.a=t,this.b=e,this.d=0,this.f=void 0,this.g=this.h.bind(this)}dispose(){this.cancel(),this.a=null}cancel(){this.isScheduled()&&(clearInterval(this.f),this.f=void 0)}schedule(t=this.b){t%1e3!==0&&console.warn(`ProcessTimeRunOnceScheduler resolution is 1s, ${t}ms is not a multiple of 1000ms.`),this.cancel(),this.d=Math.ceil(t/1e3),this.f=setInterval(this.g,1e3)}isScheduled(){return this.f!==void 0}h(){this.d--,!(this.d>0)&&(clearInterval(this.f),this.f=void 0,this.a?.())}},qh,hs;(function(){const t=globalThis;typeof t.requestIdleCallback!="function"||typeof t.cancelIdleCallback!="function"?hs=(e,i,s)=>{ka(()=>{if(r)return;const n=Date.now()+15;i(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,n-Date.now())}}))});let r=!1;return{dispose(){r||(r=!0)}}}:hs=(e,i,s)=>{const r=e.requestIdleCallback(i,typeof s=="number"?{timeout:s}:void 0);let n=!1;return{dispose(){n||(n=!0,e.cancelIdleCallback(r))}}},qh=(e,i)=>hs(globalThis,e,i)})();var qr;(function(t){t[t.Resolved=0]="Resolved",t[t.Rejected=1]="Rejected"})(qr||(qr={}));var Vh=class{get isRejected(){return this.d?.outcome===1}get isResolved(){return this.d?.outcome===0}get isSettled(){return!!this.d}get value(){return this.d?.outcome===0?this.d?.value:void 0}constructor(){this.p=new Promise((t,e)=>{this.a=t,this.b=e})}complete(t){return new Promise(e=>{this.a(t),this.d={outcome:0,value:t},e()})}error(t){return new Promise(e=>{this.b(t),this.d={outcome:1,value:t},e()})}settleWith(t){return t.then(e=>this.complete(e),e=>this.error(e))}cancel(){return this.error(new _t)}},cs;(function(t){async function e(s){let r;const n=await Promise.all(s.map(o=>o.then(a=>a,a=>{r||(r=a)})));if(typeof r<"u")throw r;return n}t.settled=e;function i(s){return new Promise(async(r,n)=>{try{await s(r,n)}catch(o){n(o)}})}t.withAsyncBody=i})(cs||(cs={}));var Vr;(function(t){t[t.Initial=0]="Initial",t[t.DoneOK=1]="DoneOK",t[t.DoneError=2]="DoneError"})(Vr||(Vr={}));var s0=class mt{static fromArray(e){return new mt(i=>{i.emitMany(e)})}static fromPromise(e){return new mt(async i=>{i.emitMany(await e)})}static fromPromisesResolveOrder(e){return new mt(async i=>{await Promise.all(e.map(async s=>i.emitOne(await s)))})}static merge(e){return new mt(async i=>{await Promise.all(e.map(async s=>{for await(const r of s)i.emitOne(r)}))})}static{this.EMPTY=mt.fromArray([])}constructor(e,i){this.a=0,this.b=[],this.d=null,this.f=i,this.g=new x,queueMicrotask(async()=>{const s={emitOne:r=>this.h(r),emitMany:r=>this.j(r),reject:r=>this.l(r)};try{await Promise.resolve(e(s)),this.k()}catch(r){this.l(r)}finally{s.emitOne=void 0,s.emitMany=void 0,s.reject=void 0}})}[Symbol.asyncIterator](){let e=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(e<this.b.length)return{done:!1,value:this.b[e++]};if(this.a===1)return{done:!0,value:void 0};await Y.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(e,i){return new mt(async s=>{for await(const r of e)s.emitOne(i(r))})}map(e){return mt.map(this,e)}static filter(e,i){return new mt(async s=>{for await(const r of e)i(r)&&s.emitOne(r)})}filter(e){return mt.filter(this,e)}static coalesce(e){return mt.filter(e,i=>!!i)}coalesce(){return mt.coalesce(this)}static async toPromise(e){const i=[];for await(const s of e)i.push(s);return i}toPromise(){return mt.toPromise(this)}h(e){this.a===0&&(this.b.push(e),this.g.fire())}j(e){this.a===0&&(this.b=this.b.concat(e),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(e){this.a===0&&(this.a=2,this.d=e,this.g.fire())}},r0=Symbol("AsyncReaderEndOfStream");function Xr(t){return(e,i,s)=>{let r=null,n=null;if(typeof s.value=="function"?(r="value",n=s.value):typeof s.get=="function"&&(r="get",n=s.get),!n||typeof i=="symbol")throw new Error("not supported");s[r]=t(n,i)}}function W(t,e,i){let s=null,r=null;if(typeof i.value=="function"?(s="value",r=i.value,r.length!==0&&console.warn("Memoize should only be used in functions with zero parameters")):typeof i.get=="function"&&(s="get",r=i.get),!r)throw new Error("not supported");const n=`$memoize$${e}`;i[s]=function(...o){return this.hasOwnProperty(n)||Object.defineProperty(this,n,{configurable:!1,enumerable:!1,writable:!1,value:r.apply(this,o)}),this[n]}}function ls(t,e,i){return Xr((s,r)=>{const n=`$debounce$${r}`,o=`$debounce$result$${r}`;return function(...a){this[o]||(this[o]=i?i():void 0),clearTimeout(this[n]),e&&(this[o]=e(this[o],...a),a=[this[o]]),this[n]=setTimeout(()=>{s.apply(this,a),this[o]=i?i():void 0},t)}})}function Gr(t,e,i){return Xr((s,r)=>{const n=`$throttle$timer$${r}`,o=`$throttle$result$${r}`,a=`$throttle$lastRun$${r}`,c=`$throttle$pending$${r}`;return function(...h){if(this[o]||(this[o]=i?i():void 0),(this[a]===null||this[a]===void 0)&&(this[a]=-Number.MAX_VALUE),e&&(this[o]=e(this[o],...h)),this[c])return;const u=this[a]+t;u<=Date.now()?(this[a]=Date.now(),s.apply(this,[this[o]]),this[o]=i?i():void 0):(this[c]=!0,this[n]=setTimeout(()=>{this[c]=!1,this[a]=Date.now(),s.apply(this,[this[o]]),this[o]=i?i():void 0},u-Date.now()))}})}function mi(t,e=0){if(!t||e>200)return t;if(typeof t=="object"){switch(t.$mid){case 1:return j.revive(t);case 2:return new RegExp(t.source,t.flags);case 17:return new Date(t.source)}if(t instanceof gt||t instanceof Uint8Array)return t;if(Array.isArray(t))for(let i=0;i<t.length;++i)t[i]=mi(t[i],e+1);else for(const i in t)Object.hasOwnProperty.call(t,i)&&(t[i]=mi(t[i],e+1))}return t}var Qr;(function(t){t[t.Promise=100]="Promise",t[t.PromiseCancel=101]="PromiseCancel",t[t.EventListen=102]="EventListen",t[t.EventDispose=103]="EventDispose"})(Qr||(Qr={}));function ue(t){switch(t){case 100:return"req";case 101:return"cancel";case 102:return"subscribe";case 103:return"unsubscribe"}}var Yr;(function(t){t[t.Initialize=200]="Initialize",t[t.PromiseSuccess=201]="PromiseSuccess",t[t.PromiseError=202]="PromiseError",t[t.PromiseErrorObj=203]="PromiseErrorObj",t[t.EventFire=204]="EventFire"})(Yr||(Yr={}));function vi(t){switch(t){case 200:return"init";case 201:return"reply:";case 202:case 203:return"replyErr:";case 204:return"event:"}}var Gt;(function(t){t[t.Uninitialized=0]="Uninitialized",t[t.Idle=1]="Idle"})(Gt||(Gt={}));function fe(t){let e=0;for(let i=0;;i+=7){const s=t.read(1);if(e|=(s.buffer[0]&127)<<i,!(s.buffer[0]&128))return e}}var Xh=Ut(0);function de(t,e){if(e===0){t.write(Xh);return}let i=0;for(let r=e;r!==0;r=r>>>7)i++;const s=gt.alloc(i);for(let r=0;e!==0;r++)s.buffer[r]=e&127,e=e>>>7,e>0&&(s.buffer[r]|=128);t.write(s)}var us=class{constructor(t){this.b=t,this.a=0}read(t){const e=this.b.slice(this.a,this.a+t);return this.a+=e.byteLength,e}},Jr=class{constructor(){this.a=[]}get buffer(){return gt.concat(this.a)}write(t){this.a.push(t)}},nt;(function(t){t[t.Undefined=0]="Undefined",t[t.String=1]="String",t[t.Buffer=2]="Buffer",t[t.VSBuffer=3]="VSBuffer",t[t.Array=4]="Array",t[t.Object=5]="Object",t[t.Int=6]="Int"})(nt||(nt={}));function Ut(t){const e=gt.alloc(1);return e.writeUInt8(t,0),e}var Qt={Undefined:Ut(nt.Undefined),String:Ut(nt.String),Buffer:Ut(nt.Buffer),VSBuffer:Ut(nt.VSBuffer),Array:Ut(nt.Array),Object:Ut(nt.Object),Uint:Ut(nt.Int)};function _e(t,e){if(typeof e>"u")t.write(Qt.Undefined);else if(typeof e=="string"){const i=gt.fromString(e);t.write(Qt.String),de(t,i.byteLength),t.write(i)}else if(gt.isNativeBuffer(e)){const i=gt.wrap(e);t.write(Qt.Buffer),de(t,i.byteLength),t.write(i)}else if(e instanceof gt)t.write(Qt.VSBuffer),de(t,e.byteLength),t.write(e);else if(Array.isArray(e)){t.write(Qt.Array),de(t,e.length);for(const i of e)_e(t,i)}else if(typeof e=="number"&&(e|0)===e)t.write(Qt.Uint),de(t,e);else{const i=gt.fromString(JSON.stringify(e));t.write(Qt.Object),de(t,i.byteLength),t.write(i)}}function pe(t){switch(t.read(1).readUInt8(0)){case nt.Undefined:return;case nt.String:return t.read(fe(t)).toString();case nt.Buffer:return t.read(fe(t)).buffer;case nt.VSBuffer:return t.read(fe(t));case nt.Array:{const i=fe(t),s=[];for(let r=0;r<i;r++)s.push(pe(t));return s}case nt.Object:return JSON.parse(t.read(fe(t)).toString());case nt.Int:return fe(t)}}var Kr=class{constructor(t,e,i=null,s=1e3){this.h=t,this.j=e,this.k=i,this.l=s,this.b=new Map,this.d=new Map,this.g=new Map,this.f=this.h.onMessage(r=>this.q(r)),this.m({type:200})}registerChannel(t,e){this.b.set(t,e),setTimeout(()=>this.w(t),0)}m(t){switch(t.type){case 200:{const e=this.o([t.type]);this.k?.logOutgoing(e,0,1,vi(t.type));return}case 201:case 202:case 204:case 203:{const e=this.o([t.type,t.id],t.data);this.k?.logOutgoing(e,t.id,1,vi(t.type),t.data);return}}}o(t,e=void 0){const i=new Jr;return _e(i,t),_e(i,e),this.p(i.buffer)}p(t){try{return this.h.send(t),t.byteLength}catch{return 0}}q(t){const e=new us(t),i=pe(e),s=pe(e),r=i[0];switch(r){case 100:return this.k?.logIncoming(t.byteLength,i[1],1,`${ue(r)}: ${i[2]}.${i[3]}`,s),this.s({type:r,id:i[1],channelName:i[2],name:i[3],arg:s});case 102:return this.k?.logIncoming(t.byteLength,i[1],1,`${ue(r)}: ${i[2]}.${i[3]}`,s),this.t({type:r,id:i[1],channelName:i[2],name:i[3],arg:s});case 101:return this.k?.logIncoming(t.byteLength,i[1],1,`${ue(r)}`),this.u({type:r,id:i[1]});case 103:return this.k?.logIncoming(t.byteLength,i[1],1,`${ue(r)}`),this.u({type:r,id:i[1]})}}s(t){const e=this.b.get(t.channelName);if(!e){this.v(t);return}const i=new is;let s;try{s=e.call(this.j,t.name,t.arg,i.token)}catch(o){s=Promise.reject(o)}const r=t.id;s.then(o=>{this.m({id:r,data:o,type:201})},o=>{o instanceof Error?this.m({id:r,data:{message:o.message,name:o.name,stack:o.stack?o.stack.split(`
`):void 0},type:202}):this.m({id:r,data:o,type:203})}).finally(()=>{n.dispose(),this.d.delete(t.id)});const n=wt(()=>i.cancel());this.d.set(t.id,n)}t(t){const e=this.b.get(t.channelName);if(!e){this.v(t);return}const i=t.id,r=e.listen(this.j,t.name,t.arg)(n=>this.m({id:i,data:n,type:204}));this.d.set(t.id,r)}u(t){const e=this.d.get(t.id);e&&(e.dispose(),this.d.delete(t.id))}v(t){let e=this.g.get(t.channelName);e||(e=[],this.g.set(t.channelName,e));const i=setTimeout(()=>{console.error(`Unknown channel: ${t.channelName}`),t.type===100&&this.m({id:t.id,data:{name:"Unknown channel",message:`Channel name '${t.channelName}' timed out after ${this.l}ms`,stack:void 0},type:202})},this.l);e.push({request:t,timeoutTimer:i})}w(t){const e=this.g.get(t);if(e){for(const i of e)switch(clearTimeout(i.timeoutTimer),i.request.type){case 100:this.s(i.request);break;case 102:this.t(i.request);break}this.g.delete(t)}}dispose(){this.f&&(this.f.dispose(),this.f=null),Rt(this.d.values()),this.d.clear()}},Zr;(function(t){t[t.LocalSide=0]="LocalSide",t[t.OtherSide=1]="OtherSide"})(Zr||(Zr={}));var tn=class{constructor(t,e=null){this.l=t,this.a=!1,this.b=Gt.Uninitialized,this.d=new Set,this.f=new Map,this.g=0,this.k=new x,this.onDidInitialize=this.k.event,this.h=this.l.onMessage(i=>this.s(i)),this.j=e}getChannel(t){const e=this;return{call(i,s,r){return e.a?Promise.reject(new _t):e.m(t,i,s,r)},listen(i,s){return e.a?Y.None:e.o(t,i,s)}}}m(t,e,i,s=Me.None){const r=this.g++,o={id:r,type:100,channelName:t,name:e,arg:i};if(s.isCancellationRequested)return Promise.reject(new _t);let a,c;return new Promise((u,l)=>{if(s.isCancellationRequested)return l(new _t);const f=()=>{const g=w=>{switch(w.type){case 201:this.f.delete(r),u(w.data);break;case 202:{this.f.delete(r);const v=new Error(w.data.message);v.stack=Array.isArray(w.data.stack)?w.data.stack.join(`
`):w.data.stack,v.name=w.data.name,l(v);break}case 203:this.f.delete(r),l(w.data);break}};this.f.set(r,g),this.p(o)};let d=null;this.b===Gt.Idle?f():(d=as(g=>this.u()),d.then(()=>{d=null,f()}));const p=()=>{d?(d.cancel(),d=null):this.p({id:r,type:101}),l(new _t)};a=s.onCancellationRequested(p),c={dispose:Hi(()=>{p(),a.dispose()})},this.d.add(c)}).finally(()=>{a?.dispose(),this.d.delete(c)})}o(t,e,i){const s=this.g++,n={id:s,type:102,channelName:t,name:e,arg:i};let o=null;const a=new x({onWillAddFirstListener:()=>{const h=()=>{this.d.add(a),this.p(n)};this.b===Gt.Idle?h():(o=as(u=>this.u()),o.then(()=>{o=null,h()}))},onDidRemoveLastListener:()=>{o?(o.cancel(),o=null):(this.d.delete(a),this.p({id:s,type:103}))}}),c=h=>a.fire(h.data);return this.f.set(s,c),a.event}p(t){switch(t.type){case 100:case 102:{const e=this.q([t.type,t.id,t.channelName,t.name],t.arg);this.j?.logOutgoing(e,t.id,0,`${ue(t.type)}: ${t.channelName}.${t.name}`,t.arg);return}case 101:case 103:{const e=this.q([t.type,t.id]);this.j?.logOutgoing(e,t.id,0,ue(t.type));return}}}q(t,e=void 0){const i=new Jr;return _e(i,t),_e(i,e),this.r(i.buffer)}r(t){try{return this.l.send(t),t.byteLength}catch{return 0}}s(t){const e=new us(t),i=pe(e),s=pe(e),r=i[0];switch(r){case 200:return this.j?.logIncoming(t.byteLength,0,0,vi(r)),this.t({type:i[0]});case 201:case 202:case 204:case 203:return this.j?.logIncoming(t.byteLength,i[1],0,vi(r),s),this.t({type:i[0],id:i[1],data:s})}}t(t){if(t.type===200){this.b=Gt.Idle,this.k.fire();return}this.f.get(t.id)?.(t)}get onDidInitializePromise(){return Y.toPromise(this.onDidInitialize)}u(){return this.b===Gt.Idle?Promise.resolve():this.onDidInitializePromise}dispose(){this.a=!0,this.h&&(this.h.dispose(),this.h=null),Rt(this.d.values()),this.d.clear()}};__decorate([W],tn.prototype,"onDidInitializePromise",null);var Gh=class{get connections(){const t=[];return this.f.forEach(e=>t.push(e)),t}constructor(t,e,i){this.a=new Map,this.f=new Set,this.g=new x,this.onDidAddConnection=this.g.event,this.h=new x,this.onDidRemoveConnection=this.h.event,this.j=new qt,this.j.add(t(({protocol:s,onDidClientDisconnect:r})=>{const n=Y.once(s.onMessage);this.j.add(n(o=>{const a=new us(o),c=pe(a),h=new Kr(s,c,e,i),u=new tn(s,e);this.a.forEach((f,d)=>h.registerChannel(d,f));const l={channelServer:h,channelClient:u,ctx:c};this.f.add(l),this.g.fire(l),this.j.add(r(()=>{h.dispose(),u.dispose(),this.f.delete(l),this.h.fire(l)}))}))}))}getChannel(t,e){const i=this;return{call(s,r,n){let o;if(Vi(e)){const c=To(i.connections.filter(e));o=c?Promise.resolve(c):Y.toPromise(Y.filter(i.onDidAddConnection,e))}else o=e.routeCall(i,s,r);const a=o.then(c=>c.channelClient.getChannel(t));return en(a).call(s,r,n)},listen(s,r){if(Vi(e))return i.k(t,e,s,r);const n=e.routeEvent(i,s,r).then(o=>o.channelClient.getChannel(t));return en(n).listen(s,r)}}}k(t,e,i,s){const r=this;let n;const o=new x({onWillAddFirstListener:()=>{n=new qt;const a=new hh,c=new Map,h=l=>{const d=l.channelClient.getChannel(t).listen(i,s),p=a.add(d);c.set(l,p)},u=l=>{const f=c.get(l);f&&(f.dispose(),c.delete(l))};r.connections.filter(e).forEach(h),Y.filter(r.onDidAddConnection,e)(h,void 0,n),r.onDidRemoveConnection(u,void 0,n),a.event(o.fire,o,n),n.add(a)},onDidRemoveLastListener:()=>{n?.dispose(),n=void 0}});return r.j.add(o),o.event}registerChannel(t,e){this.a.set(t,e);for(const i of this.f)i.channelServer.registerChannel(t,e)}dispose(){this.j.dispose();for(const t of this.f)t.channelClient.dispose(),t.channelServer.dispose();this.f.clear(),this.a.clear(),this.g.dispose(),this.h.dispose()}};function en(t){return{call(e,i,s){return t.then(r=>r.call(e,i,s))},listen(e,i){const s=new ch;return t.then(r=>s.input=r.listen(e,i)),s.event}}}var wi;(function(t){function e(n,o,a){const c=n,h=a&&a.disableMarshalling,u=new Map;for(const l in c)s(l)&&u.set(l,Y.buffer(c[l],!0,void 0,o));return new class{listen(l,f,d){const p=u.get(f);if(p)return p;const g=c[f];if(typeof g=="function"){if(r(f))return g.call(c,d);if(s(f))return u.set(f,Y.buffer(c[f],!0,void 0,o)),u.get(f)}throw new re(`Event not found: ${f}`)}call(l,f,d){const p=c[f];if(typeof p=="function"){if(!h&&Array.isArray(d))for(let w=0;w<d.length;w++)d[w]=mi(d[w]);let g=p.apply(c,d);return g instanceof Promise||(g=Promise.resolve(g)),g}throw new re(`Method not found: ${f}`)}}}t.fromService=e;function i(n,o){const a=o&&o.disableMarshalling;return new Proxy({},{get(c,h){if(typeof h=="string")return o?.properties?.has(h)?o.properties.get(h):r(h)?function(u){return n.listen(h,u)}:s(h)?n.listen(h):async function(...u){let l;o&&!Yo(o.context)?l=[o.context,...u]:l=u;const f=await n.call(h,l);return a?f:mi(f)};throw new re(`Property not found: ${String(h)}`)}})}t.toService=i;function s(n){return n[0]==="o"&&n[1]==="n"&&Mr(n.charCodeAt(2))}function r(n){return/^onDynamic/.test(n)&&Mr(n.charCodeAt(9))}})(wi||(wi={}));import"child_process";function yi(t,e){if(t===e)return!0;if(t==null||e===null||e===void 0||typeof t!=typeof e||typeof t!="object"||Array.isArray(t)!==Array.isArray(e))return!1;let i,s;if(Array.isArray(t)){if(t.length!==e.length)return!1;for(i=0;i<t.length;i++)if(!yi(t[i],e[i]))return!1}else{const r=[];for(s in t)r.push(s);r.sort();const n=[];for(s in e)n.push(s);if(n.sort(),!yi(r,n))return!1;for(i=0;i<r.length;i++)if(!yi(t[r[i]],e[r[i]]))return!1}return!0}function sn(t,e){const i=e.toLowerCase(),s=Object.keys(t).find(r=>r.toLowerCase()===i);return s?t[s]:t[e]}import"child_process";import{promises as rn}from"fs";var nn;(function(t){t[t.stdout=0]="stdout",t[t.stderr=1]="stderr"})(nn||(nn={}));var on;(function(t){t[t.Success=0]="Success",t[t.Unknown=1]="Unknown",t[t.AccessDenied=2]="AccessDenied",t[t.ProcessNotFound=3]="ProcessNotFound"})(on||(on={}));import*as z from"fs";import{tmpdir as Qh}from"os";import{promisify as Re}from"util";var Yh=new tr(1e4);function an(t){return Kh(t,"NFC",Yh)}var u0=new tr(1e4),Jh=/[^\u0000-\u0080]/;function Kh(t,e,i){if(!t)return t;const s=i.get(t);if(s)return s;let r;return Jh.test(t)?r=t.normalize(e):r=t,i.set(t,r),r}var Ne;(function(t){t[t.UNLINK=0]="UNLINK",t[t.MOVE=1]="MOVE"})(Ne||(Ne={}));async function hn(t,e=Ne.UNLINK,i){if(Br(t))throw new Error("rimraf - will refuse to recursively delete root");return e===Ne.UNLINK?fs(t):Zh(t,i)}async function Zh(t,e=Mh(Qh())){try{try{await z.promises.rename(t,e)}catch(i){return i.code==="ENOENT"?void 0:fs(t)}fs(e).catch(i=>{})}catch(i){if(i.code!=="ENOENT")throw i}}async function fs(t){return z.promises.rm(t,{recursive:!0,force:!0,maxRetries:3})}async function bi(t,e){try{return await cn(t,e)}catch(i){if(i.code==="ENOENT"&&R&&Br(t))try{return await cn(t.slice(0,-1),e)}catch{}throw i}}async function cn(t,e){return ec(await(e?tc(t):z.promises.readdir(t)))}async function tc(t){try{return await z.promises.readdir(t,{withFileTypes:!0})}catch(s){console.warn("[node.js fs] readdir with filetypes failed with error: ",s)}const e=[],i=await bi(t);for(const s of i){let r=!1,n=!1,o=!1;try{const a=await z.promises.lstat(O(t,s));r=a.isFile(),n=a.isDirectory(),o=a.isSymbolicLink()}catch(a){console.warn("[node.js fs] unexpected error from lstat after readdir: ",a)}e.push({name:s,isFile:()=>r,isDirectory:()=>n,isSymbolicLink:()=>o})}return e}function ec(t){return t.map(e=>typeof e=="string"?Nt?an(e):e:(e.name=Nt?an(e.name):e.name,e))}async function ic(t){const e=await bi(t),i=[];for(const s of e)await Dt.existsDirectory(O(t,s))&&i.push(s);return i}var Dt;(function(t){async function e(r){let n;try{if(n=await z.promises.lstat(r),!n.isSymbolicLink())return{stat:n}}catch{}try{return{stat:await z.promises.stat(r),symbolicLink:n?.isSymbolicLink()?{dangling:!1}:void 0}}catch(o){if(o.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};if(R&&o.code==="EACCES")try{return{stat:await z.promises.stat(await z.promises.readlink(r)),symbolicLink:{dangling:!1}}}catch(a){if(a.code==="ENOENT"&&n)return{stat:n,symbolicLink:{dangling:!0}};throw a}throw o}}t.stat=e;async function i(r){try{const{stat:n,symbolicLink:o}=await t.stat(r);return n.isFile()&&o?.dangling!==!0}catch{}return!1}t.existsFile=i;async function s(r){try{const{stat:n,symbolicLink:o}=await t.stat(r);return n.isDirectory()&&o?.dangling!==!0}catch{}return!1}t.existsDirectory=s})(Dt||(Dt={}));var sc=new zh;function rc(t,e,i){return sc.queueFor(j.file(t),()=>{const s=ac(i);return new Promise((r,n)=>oc(t,e,s,o=>o?n(o):r()))},Bh)}var ln=!0;function nc(t){ln=t}function oc(t,e,i,s){if(!ln)return z.writeFile(t,e,{mode:i.mode,flag:i.flag},s);z.open(t,i.flag,i.mode,(r,n)=>{if(r)return s(r);z.writeFile(n,e,o=>{if(o)return z.close(n,()=>s(o));z.fdatasync(n,a=>(a&&(console.warn("[node.js fs] fdatasync is now disabled for this session because it failed: ",a),nc(!1)),z.close(n,c=>s(c))))})})}function ac(t){return t?{mode:typeof t.mode=="number"?t.mode:438,flag:typeof t.flag=="string"?t.flag:"w"}:{mode:438,flag:"w"}}async function hc(t,e,i=6e4){if(t!==e)try{R&&typeof i=="number"?await un(t,e,Date.now(),i):await z.promises.rename(t,e)}catch(s){if(t.toLowerCase()!==e.toLowerCase()&&s.code==="EXDEV"||t.endsWith("."))await fn(t,e,{preserveSymlinks:!1}),await hn(t,Ne.MOVE);else throw s}}async function un(t,e,i,s,r=0){try{return await z.promises.rename(t,e)}catch(n){if(n.code!=="EACCES"&&n.code!=="EPERM"&&n.code!=="EBUSY")throw n;if(Date.now()-i>=s)throw console.error(`[node.js fs] rename failed after ${r} retries with error: ${n}`),n;if(r===0){let o=!1;try{const{stat:a}=await Dt.stat(e);a.isFile()||(o=!0)}catch{}if(o)throw n}return await Ft(Math.min(100,r*10)),un(t,e,i,s,r+1)}}async function fn(t,e,i){return pn(t,e,{root:{source:t,target:e},options:i,handledSourcePaths:new Set})}var dn=511;async function pn(t,e,i){if(i.handledSourcePaths.has(t))return;i.handledSourcePaths.add(t);const{stat:s,symbolicLink:r}=await Dt.stat(t);if(r){if(i.options.preserveSymlinks)try{return await uc(t,e,i)}catch{}if(r.dangling)return}return s.isDirectory()?cc(t,e,s.mode&dn,i):lc(t,e,s.mode&dn)}async function cc(t,e,i,s){await z.promises.mkdir(e,{recursive:!0,mode:i});const r=await bi(t);for(const n of r)await pn(O(t,n),O(e,n),s)}async function lc(t,e,i){await z.promises.copyFile(t,e),await z.promises.chmod(e,i)}async function uc(t,e,i){let s=await z.promises.readlink(t);ns(s,i.root.source,!Le)&&(s=O(i.root.target,s.substr(i.root.source.length+1))),await z.promises.symlink(s,e)}async function fc(t){try{return await Re(z.realpath)(t)}catch{const i=dc(t);return await z.promises.access(i,z.constants.R_OK),i}}function dc(t){return ph(he(t),Ae)}var ds=new class{get read(){return(t,e,i,s,r)=>new Promise((n,o)=>{z.read(t,e,i,s,r,(a,c,h)=>a?o(a):n({bytesRead:c,buffer:h}))})}get write(){return(t,e,i,s,r)=>new Promise((n,o)=>{z.write(t,e,i,s,r,(a,c,h)=>a?o(a):n({bytesWritten:c,buffer:h}))})}get fdatasync(){return Re(z.fdatasync)}get open(){return Re(z.open)}get close(){return Re(z.close)}get ftruncate(){return Re(z.ftruncate)}async exists(t){try{return await z.promises.access(t),!0}catch{return!1}}get readdir(){return bi}get readDirsInDir(){return ic}get writeFile(){return rc}get rm(){return hn}get rename(){return hc}get copy(){return fn}get realpath(){return fc}};function pc(t=Pt){return t.comspec||"cmd.exe"}async function gc(t){if(await ds.exists(t)){let e;try{e=await rn.stat(t)}catch(i){i.message.startsWith("EACCES")&&(e=await rn.lstat(t))}return e?!e.isDirectory():!1}return!1}async function mc(t,e,i,s=Pt,r=gc){if(yr(t))return await r(t)?t:void 0;if(e===void 0&&(e=ke()),De(t)!=="."){const c=O(e,t);return await r(c)?c:void 0}const o=sn(s,"PATH");if(i===void 0&&ne(o)&&(i=o.split(br)),i===void 0||i.length===0){const c=O(e,t);return await r(c)?c:void 0}for(const c of i){let h;if(yr(c)?h=O(c,t):h=O(e,c,t),R){const l=(sn(s,"PATHEXT")||".COM;.EXE;.BAT;.CMD").split(";").map(async f=>{const d=h+f;return await r(d)?d:void 0});for(const f of l){const d=await f;if(d)return d}}if(await r(h))return h}const a=O(e,t);return await r(a)?a:void 0}var vc=class extends Kr{constructor(t){super({send:e=>{try{process.send?.(e.buffer.toString("base64"))}catch{}},onMessage:Y.fromNodeEventEmitter(process,"message",e=>gt.wrap(Buffer.from(e,"base64")))},t),process.once("disconnect",()=>this.dispose())}};function gn(t){return!!t.parentPort}var wc=class{constructor(t){this.a=t,this.onMessage=Y.fromNodeEventEmitter(this.a,"message",e=>e.data?gt.wrap(e.data):gt.alloc(0)),t.start()}send(t){this.a.postMessage(t.buffer)}disconnect(){this.a.close()}},yc=class eo extends Gh{static b(e){Jo(gn(process),"Electron Utility Process");const i=new x;return process.parentPort.on("message",s=>{if(e?.handledClientConnection(s))return;const r=s.ports.at(0);r&&i.fire(r)}),Y.map(i.event,s=>({protocol:new wc(s),onDidClientDisconnect:Y.fromNodeEventEmitter(s,"close")}))}constructor(e){super(eo.b(e))}},bc=ko(Do(),1),f0={o:y(1861,null),e:y(1862,null),t:y(1863,null),m:y(1864,null)},Cc={chat:{type:"subcommand",description:"Pass in a prompt to run in a chat session in the current working directory.",options:{_:{type:"string[]",description:y(1865,null)},mode:{type:"string",cat:"o",alias:"m",args:"mode",description:y(1866,null)},"add-file":{type:"string[]",cat:"o",alias:"a",args:"path",description:y(1867,null)},maximize:{type:"boolean",cat:"o",description:y(1868,null)},"reuse-window":{type:"boolean",cat:"o",alias:"r",description:y(1869,null)},"new-window":{type:"boolean",cat:"o",alias:"n",description:y(1870,null)},help:{type:"boolean",alias:"h",description:y(1871,null)}}},"serve-web":{type:"subcommand",description:"Run a server that displays the editor UI in browsers.",options:{"cli-data-dir":{type:"string",args:"dir",description:y(1872,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"}}},tunnel:{type:"subcommand",description:"Make the current machine accessible from vscode.dev or other machines through a secure tunnel.",options:{"cli-data-dir":{type:"string",args:"dir",description:y(1873,null)},"disable-telemetry":{type:"boolean"},"telemetry-level":{type:"string"},user:{type:"subcommand",options:{login:{type:"subcommand",options:{provider:{type:"string"},"access-token":{type:"string"}}}}}}},diff:{type:"boolean",cat:"o",alias:"d",args:["file","file"],description:y(1874,null)},merge:{type:"boolean",cat:"o",alias:"m",args:["path1","path2","base","result"],description:y(1875,null)},add:{type:"boolean",cat:"o",alias:"a",args:"folder",description:y(1876,null)},remove:{type:"boolean",cat:"o",args:"folder",description:y(1877,null)},goto:{type:"boolean",cat:"o",alias:"g",args:"file:line[:character]",description:y(1878,null)},"new-window":{type:"boolean",cat:"o",alias:"n",description:y(1879,null)},"reuse-window":{type:"boolean",cat:"o",alias:"r",description:y(1880,null)},wait:{type:"boolean",cat:"o",alias:"w",description:y(1881,null)},waitMarkerFilePath:{type:"string"},locale:{type:"string",cat:"o",args:"locale",description:y(1882,null)},"user-data-dir":{type:"string",cat:"o",args:"dir",description:y(1883,null)},profile:{type:"string",cat:"o",args:"profileName",description:y(1884,null)},help:{type:"boolean",cat:"o",alias:"h",description:y(1885,null)},"extensions-dir":{type:"string",deprecates:["extensionHomePath"],cat:"e",args:"dir",description:y(1886,null)},"extensions-download-dir":{type:"string"},"builtin-extensions-dir":{type:"string"},"list-extensions":{type:"boolean",cat:"e",description:y(1887,null)},"show-versions":{type:"boolean",cat:"e",description:y(1888,null)},category:{type:"string",allowEmptyValue:!0,cat:"e",description:y(1889,null),args:"category"},"install-extension":{type:"string[]",cat:"e",args:"ext-id | path",description:y(1890,null)},"pre-release":{type:"boolean",cat:"e",description:y(1891,null)},"uninstall-extension":{type:"string[]",cat:"e",args:"ext-id",description:y(1892,null)},"update-extensions":{type:"boolean",cat:"e",description:y(1893,null)},"enable-proposed-api":{type:"string[]",allowEmptyValue:!0,cat:"e",args:"ext-id",description:y(1894,null)},"add-mcp":{type:"string[]",cat:"m",args:"json",description:y(1895,null)},version:{type:"boolean",cat:"t",alias:"v",description:y(1896,null)},verbose:{type:"boolean",cat:"t",global:!0,description:y(1897,null)},log:{type:"string[]",cat:"t",args:"level",global:!0,description:y(1898,null)},status:{type:"boolean",alias:"s",cat:"t",description:y(1899,null)},"prof-startup":{type:"boolean",cat:"t",description:y(1900,null)},"prof-append-timers":{type:"string"},"prof-duration-markers":{type:"string[]"},"prof-duration-markers-file":{type:"string"},"no-cached-data":{type:"boolean"},"prof-startup-prefix":{type:"string"},"prof-v8-extensions":{type:"boolean"},"disable-extensions":{type:"boolean",deprecates:["disableExtensions"],cat:"t",description:y(1901,null)},"disable-extension":{type:"string[]",cat:"t",args:"ext-id",description:y(1902,null)},sync:{type:"string",cat:"t",description:y(1903,null),args:["on | off"]},"inspect-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugPluginHost"],args:"port",cat:"t",description:y(1904,null)},"inspect-brk-extensions":{type:"string",allowEmptyValue:!0,deprecates:["debugBrkPluginHost"],args:"port",cat:"t",description:y(1905,null)},"disable-lcd-text":{type:"boolean",cat:"t",description:y(1906,null)},"disable-gpu":{type:"boolean",cat:"t",description:y(1907,null)},"disable-chromium-sandbox":{type:"boolean",cat:"t",description:y(1908,null)},sandbox:{type:"boolean"},"locate-shell-integration-path":{type:"string",cat:"t",args:["shell"],description:y(1909,null)},telemetry:{type:"boolean",cat:"t",description:y(1910,null)},remote:{type:"string",allowEmptyValue:!0},"folder-uri":{type:"string[]",cat:"o",args:"uri"},"file-uri":{type:"string[]",cat:"o",args:"uri"},"locate-extension":{type:"string[]"},extensionDevelopmentPath:{type:"string[]"},extensionDevelopmentKind:{type:"string[]"},extensionTestsPath:{type:"string"},extensionEnvironment:{type:"string"},debugId:{type:"string"},debugRenderer:{type:"boolean"},"inspect-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-brk-ptyhost":{type:"string",allowEmptyValue:!0},"inspect-search":{type:"string",deprecates:["debugSearch"],allowEmptyValue:!0},"inspect-brk-search":{type:"string",deprecates:["debugBrkSearch"],allowEmptyValue:!0},"inspect-sharedprocess":{type:"string",allowEmptyValue:!0},"inspect-brk-sharedprocess":{type:"string",allowEmptyValue:!0},"export-default-configuration":{type:"string"},"install-source":{type:"string"},"enable-smoke-test-driver":{type:"boolean"},logExtensionHostCommunication:{type:"boolean"},"skip-release-notes":{type:"boolean"},"skip-welcome":{type:"boolean"},"disable-telemetry":{type:"boolean"},"disable-updates":{type:"boolean"},transient:{type:"boolean"},"use-inmemory-secretstorage":{type:"boolean",deprecates:["disable-keytar"]},"password-store":{type:"string"},"disable-workspace-trust":{type:"boolean"},"disable-crash-reporter":{type:"boolean"},"crash-reporter-directory":{type:"string"},"crash-reporter-id":{type:"string"},"skip-add-to-recently-opened":{type:"boolean"},"open-url":{type:"boolean"},"file-write":{type:"boolean"},"file-chmod":{type:"boolean"},"install-builtin-extension":{type:"string[]"},force:{type:"boolean"},"do-not-sync":{type:"boolean"},"do-not-include-pack-dependencies":{type:"boolean"},trace:{type:"boolean"},"trace-memory-infra":{type:"boolean"},"trace-category-filter":{type:"string"},"trace-options":{type:"string"},"preserve-env":{type:"boolean"},"force-user-env":{type:"boolean"},"force-disable-user-env":{type:"boolean"},"open-devtools":{type:"boolean"},"disable-gpu-sandbox":{type:"boolean"},logsPath:{type:"string"},"__enable-file-policy":{type:"boolean"},editSessionId:{type:"string"},continueOn:{type:"string"},"enable-coi":{type:"boolean"},"unresponsive-sample-interval":{type:"string"},"unresponsive-sample-period":{type:"string"},"enable-rdp-display-tracking":{type:"boolean"},"disable-layout-restore":{type:"boolean"},"disable-experiments":{type:"boolean"},"startup-experiment-group":{type:"string",cat:"t",args:"control|maximizedChat|splitEmptyEditorChat|splitWelcomeChat",description:y(1911,null)},"no-proxy-server":{type:"boolean"},"no-sandbox":{type:"boolean",alias:"sandbox"},"proxy-server":{type:"string"},"proxy-bypass-list":{type:"string"},"proxy-pac-url":{type:"string"},"js-flags":{type:"string"},inspect:{type:"string",allowEmptyValue:!0},"inspect-brk":{type:"string",allowEmptyValue:!0},nolazy:{type:"boolean"},"force-device-scale-factor":{type:"string"},"force-renderer-accessibility":{type:"boolean"},"ignore-certificate-errors":{type:"boolean"},"allow-insecure-localhost":{type:"boolean"},"log-net-log":{type:"string"},vmodule:{type:"string"},_urls:{type:"string[]"},"disable-dev-shm-usage":{type:"boolean"},"profile-temp":{type:"boolean"},"ozone-platform":{type:"string"},"enable-tracing":{type:"string"},"trace-startup-format":{type:"string"},"trace-startup-file":{type:"string"},"trace-startup-duration":{type:"string"},"xdg-portal-required-version":{type:"string"},_:{type:"string[]"}},Ec={onUnknownOption:()=>{},onMultipleValues:()=>{},onEmptyValue:()=>{},onDeprecatedOption:()=>{}};function mn(t,e,i=Ec){const s=t.find((f,d)=>f.length>0&&f[0]!=="-"&&e.hasOwnProperty(f)&&e[f].type==="subcommand"),r={},n=["_"],o=[],a={};let c;for(const f in e){const d=e[f];d.type==="subcommand"?f===s&&(c=d):(d.alias&&(r[f]=d.alias),d.type==="string"||d.type==="string[]"?(n.push(f),d.deprecates&&n.push(...d.deprecates)):d.type==="boolean"&&(o.push(f),d.deprecates&&o.push(...d.deprecates)),d.global&&(a[f]=d))}if(c&&s){const f=a;for(const w in c.options)f[w]=c.options[w];const d=t.filter(w=>w!==s),p=i.getSubcommandReporter?i.getSubcommandReporter(s):void 0,g=mn(d,f,p);return{[s]:g,_:[]}}const h=(0,bc.default)(t,{string:n,boolean:o,alias:r}),u={},l=h;u._=h._.map(f=>String(f)).filter(f=>f.length>0),delete l._;for(const f in e){const d=e[f];if(d.type==="subcommand")continue;d.alias&&delete l[d.alias];let p=l[f];if(d.deprecates)for(const g of d.deprecates)l.hasOwnProperty(g)&&(p||(p=l[g],p&&i.onDeprecatedOption(g,d.deprecationMessage||y(1912,null,f))),delete l[g]);if(typeof p<"u"){if(d.type==="string[]"){if(Array.isArray(p)||(p=[p]),!d.allowEmptyValue){const g=p.filter(w=>w.length>0);g.length!==p.length&&(i.onEmptyValue(f),p=g.length>0?g:void 0)}}else d.type==="string"&&(Array.isArray(p)?(p=p.pop(),i.onMultipleValues(f,p)):!p&&!d.allowEmptyValue&&(i.onEmptyValue(f),p=void 0));u[f]=p,d.deprecationMessage&&i.onDeprecatedOption(f,d.deprecationMessage)}delete l[f]}for(const f in l)i.onUnknownOption(f);return u}import{homedir as xc,tmpdir as Sc}from"os";var Pc=60,$c=Pc*60,ps=$c*24,p0=ps*7,g0=ps*30,m0=ps*365;function Lc(t){return t.getFullYear()+"-"+String(t.getMonth()+1).padStart(2,"0")+"-"+String(t.getDate()).padStart(2,"0")+"T"+String(t.getHours()).padStart(2,"0")+":"+String(t.getMinutes()).padStart(2,"0")+":"+String(t.getSeconds()).padStart(2,"0")+"."+(t.getMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"}var vn=/^([^.]+\..+)[:=](.+)$/,q=class{get appRoot(){return De(Oe.asFileUri("").fsPath)}get userHome(){return j.file(this.b.homeDir)}get userDataPath(){return this.b.userDataDir}get appSettingsHome(){return j.file(O(this.userDataPath,"User"))}get tmpDir(){return j.file(this.b.tmpDir)}get cacheHome(){return j.file(this.userDataPath)}get stateResource(){return Ct(this.appSettingsHome,"globalStorage","storage.json")}get userRoamingDataHome(){return this.appSettingsHome.with({scheme:Z.vscodeUserData})}get userDataSyncHome(){return Ct(this.appSettingsHome,"sync")}get logsHome(){if(!this.args.logsPath){const t=Lc(new Date).replace(/-|:|\.\d+Z$/g,"");this.args.logsPath=O(this.userDataPath,"logs",t)}return j.file(this.args.logsPath)}get sync(){return this.args.sync}get workspaceStorageHome(){return Ct(this.appSettingsHome,"workspaceStorage")}get localHistoryHome(){return Ct(this.appSettingsHome,"History")}get keyboardLayoutResource(){return Ct(this.userRoamingDataHome,"keyboardLayout.json")}get argvResource(){const t=Pt.VSCODE_PORTABLE;return t?j.file(O(t,"argv.json")):Ct(this.userHome,this.c.dataFolderName,"argv.json")}get isExtensionDevelopment(){return!!this.args.extensionDevelopmentPath}get untitledWorkspacesHome(){return j.file(O(this.userDataPath,"Workspaces"))}get builtinExtensionsPath(){const t=this.args["builtin-extensions-dir"];return t?li(t):he(O(Oe.asFileUri("").fsPath,"..","extensions"))}get extensionsDownloadLocation(){const t=this.args["extensions-download-dir"];return t?j.file(li(t)):j.file(O(this.userDataPath,"CachedExtensionVSIXs"))}get extensionsPath(){const t=this.args["extensions-dir"];if(t)return li(t);const e=Pt.VSCODE_EXTENSIONS;if(e)return e;const i=Pt.VSCODE_PORTABLE;return i?O(i,"extensions"):Ct(this.userHome,this.c.dataFolderName,"extensions").fsPath}get extensionDevelopmentLocationURI(){const t=this.args.extensionDevelopmentPath;if(Array.isArray(t))return t.map(e=>/^[^:/?#]+?:\/\//.test(e)?j.parse(e):j.file(he(e)))}get extensionDevelopmentKind(){return this.args.extensionDevelopmentKind?.map(t=>t==="ui"||t==="workspace"||t==="web"?t:"workspace")}get extensionTestsLocationURI(){const t=this.args.extensionTestsPath;if(t)return/^[^:/?#]+?:\/\//.test(t)?j.parse(t):j.file(he(t))}get disableExtensions(){if(this.args["disable-extensions"])return!0;const t=this.args["disable-extension"];if(t){if(typeof t=="string")return[t];if(Array.isArray(t)&&t.length>0)return t}return!1}get debugExtensionHost(){return kc(this.args,this.isBuilt)}get debugRenderer(){return!!this.args.debugRenderer}get isBuilt(){return!Pt.VSCODE_DEV}get verbose(){return!!this.args.verbose}get logLevel(){return this.args.log?.find(t=>!vn.test(t))}get extensionLogLevel(){const t=[];for(const e of this.args.log||[]){const i=vn.exec(e);i&&i[1]&&i[2]&&t.push([i[1],i[2]])}return t.length?t:void 0}get serviceMachineIdResource(){return Ct(j.file(this.userDataPath),"machineid")}get crashReporterId(){return this.args["crash-reporter-id"]}get crashReporterDirectory(){return this.args["crash-reporter-directory"]}get disableTelemetry(){return!!this.args["disable-telemetry"]}get disableExperiments(){return!!this.args["disable-experiments"]}get disableWorkspaceTrust(){return!!this.args["disable-workspace-trust"]}get useInMemorySecretStorage(){return!!this.args["use-inmemory-secretstorage"]}get policyFile(){if(this.args["__enable-file-policy"]){const t=Pt.VSCODE_PORTABLE;return t?j.file(O(t,"policy.json")):Ct(this.userHome,this.c.dataFolderName,"policy.json")}}get editSessionId(){return this.args.editSessionId}get continueOn(){return this.args.continueOn}set continueOn(t){this.args.continueOn=t}get args(){return this.a}constructor(t,e,i){this.a=t,this.b=e,this.c=i}};__decorate([W],q.prototype,"appRoot",null),__decorate([W],q.prototype,"userHome",null),__decorate([W],q.prototype,"userDataPath",null),__decorate([W],q.prototype,"appSettingsHome",null),__decorate([W],q.prototype,"tmpDir",null),__decorate([W],q.prototype,"cacheHome",null),__decorate([W],q.prototype,"stateResource",null),__decorate([W],q.prototype,"userRoamingDataHome",null),__decorate([W],q.prototype,"userDataSyncHome",null),__decorate([W],q.prototype,"sync",null),__decorate([W],q.prototype,"workspaceStorageHome",null),__decorate([W],q.prototype,"localHistoryHome",null),__decorate([W],q.prototype,"keyboardLayoutResource",null),__decorate([W],q.prototype,"argvResource",null),__decorate([W],q.prototype,"isExtensionDevelopment",null),__decorate([W],q.prototype,"untitledWorkspacesHome",null),__decorate([W],q.prototype,"builtinExtensionsPath",null),__decorate([W],q.prototype,"extensionsPath",null),__decorate([W],q.prototype,"extensionDevelopmentLocationURI",null),__decorate([W],q.prototype,"extensionDevelopmentKind",null),__decorate([W],q.prototype,"extensionTestsLocationURI",null),__decorate([W],q.prototype,"debugExtensionHost",null),__decorate([W],q.prototype,"logLevel",null),__decorate([W],q.prototype,"extensionLogLevel",null),__decorate([W],q.prototype,"serviceMachineIdResource",null),__decorate([W],q.prototype,"disableTelemetry",null),__decorate([W],q.prototype,"disableExperiments",null),__decorate([W],q.prototype,"disableWorkspaceTrust",null),__decorate([W],q.prototype,"useInMemorySecretStorage",null),__decorate([W],q.prototype,"policyFile",null);function kc(t,e){return Dc(t["inspect-extensions"],t["inspect-brk-extensions"],5870,e,t.debugId,t.extensionEnvironment)}function Dc(t,e,i,s,r,n){const a=Number(e||t)||(s?null:i),c=a?!!e:!1;let h;if(n)try{h=JSON.parse(n)}catch{}return{port:a,break:c,debugId:r,env:h}}import{homedir as wn}from"os";import{resolve as Ac,isAbsolute as Mc,join as ge}from"path";var Ic=process.env.VSCODE_CWD||process.cwd();function Oc(t,e){const i=_c(t,e),s=[i];return Mc(i)||s.unshift(Ic),Ac(...s)}function _c(t,e){process.env.VSCODE_DEV&&(e="code-oss-dev");const i=process.env.VSCODE_PORTABLE;if(i)return ge(i,"user-data");let s=process.env.VSCODE_APPDATA;if(s)return ge(s,e);const r=t["user-data-dir"];if(r)return r;switch(process.platform){case"win32":if(s=process.env.APPDATA,!s){const n=process.env.USERPROFILE;if(typeof n!="string")throw new Error("Windows: Unexpected undefined %USERPROFILE% environment variable");s=ge(n,"AppData","Roaming")}break;case"darwin":s=ge(wn(),"Library","Application Support");break;case"linux":s=process.env.XDG_CONFIG_HOME||ge(wn(),".config");break;default:throw new Error("Platform not supported")}return ge(s,e)}var Rc=class extends q{constructor(t,e){super(t,{homeDir:xc(),tmpDir:Sc(),userDataDir:Oc(t,e.nameShort)},e)}};function gs(t,e){return e&&(t.stack||t.stacktrace)?y(110,null,bn(t),yn(t.stack)||yn(t.stacktrace)):bn(t)}function yn(t){return Array.isArray(t)?t.join(`
`):t}function bn(t){return t.code==="ERR_UNC_HOST_NOT_ALLOWED"?`${t.message}. Please update the 'security.allowedUNCHosts' setting if you want to allow this host.`:typeof t.code=="string"&&typeof t.errno=="number"&&typeof t.syscall=="string"?y(111,null,t.message):t.message||y(112,null)}function Cn(t=null,e=!1){if(!t)return y(113,null);if(Array.isArray(t)){const i=No(t),s=Cn(i[0],e);return i.length>1?y(114,null,s,i.length):s}if(ne(t))return t;if(t.detail){const i=t.detail;if(i.error)return gs(i.error,e);if(i.exception)return gs(i.exception,e)}return t.stack?gs(t,e):t.message?t.message:y(115,null)}function Nc(t){return ms(t,0)}function ms(t,e){switch(typeof t){case"object":return t===null?At(349,e):Array.isArray(t)?Bc(t,e):Fc(t,e);case"string":return En(t,e);case"boolean":return Tc(t,e);case"number":return At(t,e);case"undefined":return At(937,e);default:return At(617,e)}}function At(t,e){return(e<<5)-e+t|0}function Tc(t,e){return At(t?433:863,e)}function En(t,e){e=At(149417,e);for(let i=0,s=t.length;i<s;i++)e=At(t.charCodeAt(i),e);return e}function Bc(t,e){return e=At(104579,e),t.reduce((i,s)=>ms(s,i),e)}function Fc(t,e){return e=At(181387,e),Object.keys(t).sort().reduce((i,s)=>(i=En(s,i),ms(t[s],i)),e)}var xn;(function(t){t[t.BLOCK_SIZE=64]="BLOCK_SIZE",t[t.UNICODE_REPLACEMENT=65533]="UNICODE_REPLACEMENT"})(xn||(xn={}));function vs(t,e,i=32){const s=i-e,r=~((1<<s)-1);return(t<<e|(r&t)>>>s)>>>0}function Te(t,e=32){return t instanceof ArrayBuffer?da(gt.wrap(new Uint8Array(t))):(t>>>0).toString(16).padStart(e/4,"0")}var y0=class io{static{this.g=new DataView(new ArrayBuffer(320))}constructor(){this.h=1732584193,this.l=4023233417,this.m=2562383102,this.n=271733878,this.o=3285377520,this.p=new Uint8Array(67),this.q=new DataView(this.p.buffer),this.r=0,this.t=0,this.u=0,this.v=!1}update(e){const i=e.length;if(i===0)return;const s=this.p;let r=this.r,n=this.u,o,a;for(n!==0?(o=n,a=-1,n=0):(o=e.charCodeAt(0),a=0);;){let c=o;if(vh(o))if(a+1<i){const h=e.charCodeAt(a+1);Ir(h)?(a++,c=wh(o,h)):c=65533}else{n=o;break}else Ir(o)&&(c=65533);if(r=this.w(s,r,c),a++,a<i)o=e.charCodeAt(a);else break}this.r=r,this.u=n}w(e,i,s){return s<128?e[i++]=s:s<2048?(e[i++]=192|(s&1984)>>>6,e[i++]=128|(s&63)>>>0):s<65536?(e[i++]=224|(s&61440)>>>12,e[i++]=128|(s&4032)>>>6,e[i++]=128|(s&63)>>>0):(e[i++]=240|(s&1835008)>>>18,e[i++]=128|(s&258048)>>>12,e[i++]=128|(s&4032)>>>6,e[i++]=128|(s&63)>>>0),i>=64&&(this.y(),i-=64,this.t+=64,e[0]=e[64],e[1]=e[65],e[2]=e[66]),i}digest(){return this.v||(this.v=!0,this.u&&(this.u=0,this.r=this.w(this.p,this.r,65533)),this.t+=this.r,this.x()),Te(this.h)+Te(this.l)+Te(this.m)+Te(this.n)+Te(this.o)}x(){this.p[this.r++]=128,this.p.subarray(this.r).fill(0),this.r>56&&(this.y(),this.p.fill(0));const e=8*this.t;this.q.setUint32(56,Math.floor(e/4294967296),!1),this.q.setUint32(60,e%4294967296,!1),this.y()}y(){const e=io.g,i=this.q;for(let l=0;l<64;l+=4)e.setUint32(l,i.getUint32(l,!1),!1);for(let l=64;l<320;l+=4)e.setUint32(l,vs(e.getUint32(l-12,!1)^e.getUint32(l-32,!1)^e.getUint32(l-56,!1)^e.getUint32(l-64,!1),1),!1);let s=this.h,r=this.l,n=this.m,o=this.n,a=this.o,c,h,u;for(let l=0;l<80;l++)l<20?(c=r&n|~r&o,h=1518500249):l<40?(c=r^n^o,h=1859775393):l<60?(c=r&n|r&o|n&o,h=2400959708):(c=r^n^o,h=3395469782),u=vs(s,5)+c+a+h+e.getUint32(l*4,!1)&4294967295,a=o,o=n,n=vs(r,30),r=s,s=u;this.h=this.h+s&4294967295,this.l=this.l+r&4294967295,this.m=this.m+n&4294967295,this.n=this.n+o&4294967295,this.o=this.o+a&4294967295}},Sn;(function(t){t[t.LParen=0]="LParen",t[t.RParen=1]="RParen",t[t.Neg=2]="Neg",t[t.Eq=3]="Eq",t[t.NotEq=4]="NotEq",t[t.Lt=5]="Lt",t[t.LtEq=6]="LtEq",t[t.Gt=7]="Gt",t[t.GtEq=8]="GtEq",t[t.RegexOp=9]="RegexOp",t[t.RegexStr=10]="RegexStr",t[t.True=11]="True",t[t.False=12]="False",t[t.In=13]="In",t[t.Not=14]="Not",t[t.And=15]="And",t[t.Or=16]="Or",t[t.Str=17]="Str",t[t.QuotedStr=18]="QuotedStr",t[t.Error=19]="Error",t[t.EOF=20]="EOF"})(Sn||(Sn={}));function ws(...t){switch(t.length){case 1:return y(1841,null,t[0]);case 2:return y(1842,null,t[0],t[1]);case 3:return y(1843,null,t[0],t[1],t[2]);default:return}}var Uc=y(1844,null),jc=y(1845,null),Be=class Ws{constructor(){this.c="",this.d=0,this.e=0,this.f=[],this.g=[],this.m=/[a-zA-Z0-9_<>\-\./\\:\*\?\+\[\]\^,#@;"%\$\p{L}-]+/uy}static getLexeme(e){switch(e.type){case 0:return"(";case 1:return")";case 2:return"!";case 3:return e.isTripleEq?"===":"==";case 4:return e.isTripleEq?"!==":"!=";case 5:return"<";case 6:return"<=";case 7:return">=";case 8:return">=";case 9:return"=~";case 10:return e.lexeme;case 11:return"true";case 12:return"false";case 13:return"in";case 14:return"not";case 15:return"&&";case 16:return"||";case 17:return e.lexeme;case 18:return e.lexeme;case 19:return e.lexeme;case 20:return"EOF";default:throw Oo(`unhandled token type: ${JSON.stringify(e)}; have you forgotten to add a case?`)}}static{this.a=new Set(["i","g","s","m","y","u"].map(e=>e.charCodeAt(0)))}static{this.b=new Map([["not",14],["in",13],["false",12],["true",11]])}get errors(){return this.g}reset(e){return this.c=e,this.d=0,this.e=0,this.f=[],this.g=[],this}scan(){for(;!this.r();)switch(this.d=this.e,this.i()){case 40:this.k(0);break;case 41:this.k(1);break;case 33:if(this.h(61)){const i=this.h(61);this.f.push({type:4,offset:this.d,isTripleEq:i})}else this.k(2);break;case 39:this.o();break;case 47:this.q();break;case 61:if(this.h(61)){const i=this.h(61);this.f.push({type:3,offset:this.d,isTripleEq:i})}else this.h(126)?this.k(9):this.l(ws("==","=~"));break;case 60:this.k(this.h(61)?6:5);break;case 62:this.k(this.h(61)?8:7);break;case 38:this.h(38)?this.k(15):this.l(ws("&&"));break;case 124:this.h(124)?this.k(16):this.l(ws("||"));break;case 32:case 13:case 9:case 10:case 160:break;default:this.n()}return this.d=this.e,this.k(20),Array.from(this.f)}h(e){return this.r()||this.c.charCodeAt(this.e)!==e?!1:(this.e++,!0)}i(){return this.c.charCodeAt(this.e++)}j(){return this.r()?0:this.c.charCodeAt(this.e)}k(e){this.f.push({type:e,offset:this.d})}l(e){const i=this.d,s=this.c.substring(this.d,this.e),r={type:19,offset:this.d,lexeme:s};this.g.push({offset:i,lexeme:s,additionalInfo:e}),this.f.push(r)}n(){this.m.lastIndex=this.d;const e=this.m.exec(this.c);if(e){this.e=this.d+e[0].length;const i=this.c.substring(this.d,this.e),s=Ws.b.get(i);s?this.k(s):this.f.push({type:17,lexeme:i,offset:this.d})}}o(){for(;this.j()!==39&&!this.r();)this.i();if(this.r()){this.l(Uc);return}this.i(),this.f.push({type:18,lexeme:this.c.substring(this.d+1,this.e-1),offset:this.d+1})}q(){let e=this.e,i=!1,s=!1;for(;;){if(e>=this.c.length){this.e=e,this.l(jc);return}const n=this.c.charCodeAt(e);if(i)i=!1;else if(n===47&&!s){e++;break}else n===91?s=!0:n===92?i=!0:n===93&&(s=!1);e++}for(;e<this.c.length&&Ws.a.has(this.c.charCodeAt(e));)e++;this.e=e;const r=this.c.substring(this.d,this.e);this.f.push({type:10,lexeme:r,offset:this.d})}r(){return this.e>=this.c.length}},Mt;(function(t){t.serviceIds=new Map,t.DI_TARGET="$di$target",t.DI_DEPENDENCIES="$di$dependencies";function e(i){return i[t.DI_DEPENDENCIES]||[]}t.getServiceDependencies=e})(Mt||(Mt={}));var b0=It("instantiationService");function Wc(t,e,i){e[Mt.DI_TARGET]===e?e[Mt.DI_DEPENDENCIES].push({id:t,index:i}):(e[Mt.DI_DEPENDENCIES]=[{id:t,index:i}],e[Mt.DI_TARGET]=e)}function It(t){if(Mt.serviceIds.has(t))return Mt.serviceIds.get(t);const e=function(i,s,r){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");Wc(e,i,r)};return e.toString=()=>t,Mt.serviceIds.set(t,e),e}var tt=new Map;tt.set("false",!1),tt.set("true",!0),tt.set("isMac",Nt),tt.set("isLinux",Le),tt.set("isWindows",R),tt.set("isWeb",Ji),tt.set("isMacNative",Nt&&!Ji),tt.set("isEdge",Ma),tt.set("isFirefox",Da),tt.set("isChrome",gr),tt.set("isSafari",Aa);var zc=Object.prototype.hasOwnProperty,Pn;(function(t){t[t.False=0]="False",t[t.True=1]="True",t[t.Defined=2]="Defined",t[t.Not=3]="Not",t[t.Equals=4]="Equals",t[t.NotEquals=5]="NotEquals",t[t.And=6]="And",t[t.Regex=7]="Regex",t[t.NotRegex=8]="NotRegex",t[t.Or=9]="Or",t[t.In=10]="In",t[t.NotIn=11]="NotIn",t[t.Greater=12]="Greater",t[t.GreaterEquals=13]="GreaterEquals",t[t.Smaller=14]="Smaller",t[t.SmallerEquals=15]="SmallerEquals"})(Pn||(Pn={}));var Hc={regexParsingWithErrorRecovery:!0},qc=y(1821,null),Vc=y(1822,null),Xc=y(1823,null),$n=y(1824,null),Gc=y(1825,null),Qc=y(1826,null),Yc=y(1827,null),Jc=y(1828,null),Kc=class ei{static{this.c=new Error}get lexingErrors(){return this.d.errors}get parsingErrors(){return this.h}constructor(e=Hc){this.k=e,this.d=new Be,this.f=[],this.g=0,this.h=[],this.v=/g|y/g}parse(e){if(e===""){this.h.push({message:qc,offset:0,lexeme:"",additionalInfo:Vc});return}this.f=this.d.reset(e).scan(),this.g=0,this.h=[];try{const i=this.l();if(!this.E()){const s=this.D(),r=s.type===17?Qc:void 0;throw this.h.push({message:Gc,offset:s.offset,lexeme:Be.getLexeme(s),additionalInfo:r}),ei.c}return i}catch(i){if(i!==ei.c)throw i;return}}l(){return this.m()}m(){const e=[this.o()];for(;this.y(16);){const i=this.o();e.push(i)}return e.length===1?e[0]:ot.or(...e)}o(){const e=[this.s()];for(;this.y(15);){const i=this.s();e.push(i)}return e.length===1?e[0]:ot.and(...e)}s(){if(this.y(2)){const e=this.D();switch(e.type){case 11:return this.z(),ht.INSTANCE;case 12:return this.z(),dt.INSTANCE;case 0:{this.z();const i=this.l();return this.A(1,$n),i?.negate()}case 17:return this.z(),je.create(e.lexeme);default:throw this.B("KEY | true | false | '(' expression ')'",e)}}return this.t()}t(){const e=this.D();switch(e.type){case 11:return this.z(),ot.true();case 12:return this.z(),ot.false();case 0:{this.z();const i=this.l();return this.A(1,$n),i}case 17:{const i=e.lexeme;if(this.z(),this.y(9)){const r=this.D();if(!this.k.regexParsingWithErrorRecovery){if(this.z(),r.type!==10)throw this.B("REGEX",r);const n=r.lexeme,o=n.lastIndexOf("/"),a=o===n.length-1?void 0:this.w(n.substring(o+1));let c;try{c=new RegExp(n.substring(1,o),a)}catch{throw this.B("REGEX",r)}return Ss.create(i,c)}switch(r.type){case 10:case 19:{const n=[r.lexeme];this.z();let o=this.D(),a=0;for(let f=0;f<r.lexeme.length;f++)r.lexeme.charCodeAt(f)===40?a++:r.lexeme.charCodeAt(f)===41&&a--;for(;!this.E()&&o.type!==15&&o.type!==16;){switch(o.type){case 0:a++;break;case 1:a--;break;case 10:case 18:for(let f=0;f<o.lexeme.length;f++)o.lexeme.charCodeAt(f)===40?a++:r.lexeme.charCodeAt(f)===41&&a--}if(a<0)break;n.push(Be.getLexeme(o)),this.z(),o=this.D()}const c=n.join(""),h=c.lastIndexOf("/"),u=h===c.length-1?void 0:this.w(c.substring(h+1));let l;try{l=new RegExp(c.substring(1,h),u)}catch{throw this.B("REGEX",r)}return ot.regex(i,l)}case 18:{const n=r.lexeme;this.z();let o=null;if(!fh(n)){const a=n.indexOf("/"),c=n.lastIndexOf("/");if(a!==c&&a>=0){const h=n.slice(a+1,c),u=n[c+1]==="i"?"i":"";try{o=new RegExp(h,u)}catch{throw this.B("REGEX",r)}}}if(o===null)throw this.B("REGEX",r);return Ss.create(i,o)}default:throw this.B("REGEX",this.D())}}if(this.y(14)){this.A(13,Xc);const r=this.u();return ot.notIn(i,r)}switch(this.D().type){case 3:{this.z();const r=this.u();if(this.x().type===18)return ot.equals(i,r);switch(r){case"true":return ot.has(i);case"false":return ot.not(i);default:return ot.equals(i,r)}}case 4:{this.z();const r=this.u();if(this.x().type===18)return ot.notEquals(i,r);switch(r){case"true":return ot.not(i);case"false":return ot.has(i);default:return ot.notEquals(i,r)}}case 5:return this.z(),Es.create(i,this.u());case 6:return this.z(),xs.create(i,this.u());case 7:return this.z(),Ei.create(i,this.u());case 8:return this.z(),Cs.create(i,this.u());case 13:return this.z(),ot.in(i,this.u());default:return ot.has(i)}}case 20:throw this.h.push({message:Yc,offset:e.offset,lexeme:"",additionalInfo:Jc}),ei.c;default:throw this.B(`true | false | KEY 
	| KEY '=~' REGEX 
	| KEY ('==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not' 'in') value`,this.D())}}u(){const e=this.D();switch(e.type){case 17:case 18:return this.z(),e.lexeme;case 11:return this.z(),"true";case 12:return this.z(),"false";case 13:return this.z(),"in";default:return""}}w(e){return e.replaceAll(this.v,"")}x(){return this.f[this.g-1]}y(e){return this.C(e)?(this.z(),!0):!1}z(){return this.E()||this.g++,this.x()}A(e,i){if(this.C(e))return this.z();throw this.B(i,this.D())}B(e,i,s){const r=y(1829,null,e,Be.getLexeme(i)),n=i.offset,o=Be.getLexeme(i);return this.h.push({message:r,offset:n,lexeme:o,additionalInfo:s}),ei.c}C(e){return this.D().type===e}D(){return this.f[this.g]}E(){return this.D().type===20}},ot=class{static false(){return ht.INSTANCE}static true(){return dt.INSTANCE}static has(t){return Ue.create(t)}static equals(t,e){return ys.create(t,e)}static notEquals(t,e){return bs.create(t,e)}static regex(t,e){return Ss.create(t,e)}static in(t,e){return Ln.create(t,e)}static notIn(t,e){return kn.create(t,e)}static not(t){return je.create(t)}static and(...t){return An.create(t,null,!0)}static or(...t){return Ps.create(t,null,!0)}static greater(t,e){return Ei.create(t,e)}static greaterEquals(t,e){return Cs.create(t,e)}static smaller(t,e){return Es.create(t,e)}static smallerEquals(t,e){return xs.create(t,e)}static{this.c=new Kc({regexParsingWithErrorRecovery:!1})}static deserialize(t){return t==null?void 0:this.c.parse(t)}};function Fe(t,e){return t.cmp(e)}var ht=class so{static{this.INSTANCE=new so}constructor(){this.type=0}cmp(e){return this.type-e.type}equals(e){return e.type===this.type}substituteConstants(){return this}evaluate(e){return!1}serialize(){return"false"}keys(){return[]}map(e){return this}negate(){return dt.INSTANCE}},dt=class ro{static{this.INSTANCE=new ro}constructor(){this.type=1}cmp(e){return this.type-e.type}equals(e){return e.type===this.type}substituteConstants(){return this}evaluate(e){return!0}serialize(){return"true"}keys(){return[]}map(e){return this}negate(){return ht.INSTANCE}},Ue=class no{static create(e,i=null){const s=tt.get(e);return typeof s=="boolean"?s?dt.INSTANCE:ht.INSTANCE:new no(e,i)}constructor(e,i){this.key=e,this.c=i,this.type=2}cmp(e){return e.type!==this.type?this.type-e.type:In(this.key,e.key)}equals(e){return e.type===this.type?this.key===e.key:!1}substituteConstants(){const e=tt.get(this.key);return typeof e=="boolean"?e?dt.INSTANCE:ht.INSTANCE:this}evaluate(e){return!!e.getValue(this.key)}serialize(){return this.key}keys(){return[this.key]}map(e){return e.mapDefined(this.key)}negate(){return this.c||(this.c=je.create(this.key,this)),this.c}},ys=class oo{static create(e,i,s=null){if(typeof i=="boolean")return i?Ue.create(e,s):je.create(e,s);const r=tt.get(e);return typeof r=="boolean"?i===(r?"true":"false")?dt.INSTANCE:ht.INSTANCE:new oo(e,i,s)}constructor(e,i,s){this.c=e,this.d=i,this.f=s,this.type=4}cmp(e){return e.type!==this.type?this.type-e.type:Yt(this.c,this.d,e.c,e.d)}equals(e){return e.type===this.type?this.c===e.c&&this.d===e.d:!1}substituteConstants(){const e=tt.get(this.c);if(typeof e=="boolean"){const i=e?"true":"false";return this.d===i?dt.INSTANCE:ht.INSTANCE}return this}evaluate(e){return e.getValue(this.c)==this.d}serialize(){return`${this.c} == '${this.d}'`}keys(){return[this.c]}map(e){return e.mapEquals(this.c,this.d)}negate(){return this.f||(this.f=bs.create(this.c,this.d,this)),this.f}},Ln=class ao{static create(e,i){return new ao(e,i)}constructor(e,i){this.d=e,this.f=i,this.type=10,this.c=null}cmp(e){return e.type!==this.type?this.type-e.type:Yt(this.d,this.f,e.d,e.f)}equals(e){return e.type===this.type?this.d===e.d&&this.f===e.f:!1}substituteConstants(){return this}evaluate(e){const i=e.getValue(this.f),s=e.getValue(this.d);return Array.isArray(i)?i.includes(s):typeof s=="string"&&typeof i=="object"&&i!==null?zc.call(i,s):!1}serialize(){return`${this.d} in '${this.f}'`}keys(){return[this.d,this.f]}map(e){return e.mapIn(this.d,this.f)}negate(){return this.c||(this.c=kn.create(this.d,this.f)),this.c}},kn=class ho{static create(e,i){return new ho(e,i)}constructor(e,i){this.d=e,this.f=i,this.type=11,this.c=Ln.create(e,i)}cmp(e){return e.type!==this.type?this.type-e.type:this.c.cmp(e.c)}equals(e){return e.type===this.type?this.c.equals(e.c):!1}substituteConstants(){return this}evaluate(e){return!this.c.evaluate(e)}serialize(){return`${this.d} not in '${this.f}'`}keys(){return this.c.keys()}map(e){return e.mapNotIn(this.d,this.f)}negate(){return this.c}},bs=class co{static create(e,i,s=null){if(typeof i=="boolean")return i?je.create(e,s):Ue.create(e,s);const r=tt.get(e);return typeof r=="boolean"?i===(r?"true":"false")?ht.INSTANCE:dt.INSTANCE:new co(e,i,s)}constructor(e,i,s){this.c=e,this.d=i,this.f=s,this.type=5}cmp(e){return e.type!==this.type?this.type-e.type:Yt(this.c,this.d,e.c,e.d)}equals(e){return e.type===this.type?this.c===e.c&&this.d===e.d:!1}substituteConstants(){const e=tt.get(this.c);if(typeof e=="boolean"){const i=e?"true":"false";return this.d===i?ht.INSTANCE:dt.INSTANCE}return this}evaluate(e){return e.getValue(this.c)!=this.d}serialize(){return`${this.c} != '${this.d}'`}keys(){return[this.c]}map(e){return e.mapNotEquals(this.c,this.d)}negate(){return this.f||(this.f=ys.create(this.c,this.d,this)),this.f}},je=class lo{static create(e,i=null){const s=tt.get(e);return typeof s=="boolean"?s?ht.INSTANCE:dt.INSTANCE:new lo(e,i)}constructor(e,i){this.c=e,this.d=i,this.type=3}cmp(e){return e.type!==this.type?this.type-e.type:In(this.c,e.c)}equals(e){return e.type===this.type?this.c===e.c:!1}substituteConstants(){const e=tt.get(this.c);return typeof e=="boolean"?e?ht.INSTANCE:dt.INSTANCE:this}evaluate(e){return!e.getValue(this.c)}serialize(){return`!${this.c}`}keys(){return[this.c]}map(e){return e.mapNot(this.c)}negate(){return this.d||(this.d=Ue.create(this.c,this)),this.d}};function Ci(t,e){if(typeof t=="string"){const i=parseFloat(t);isNaN(i)||(t=i)}return typeof t=="string"||typeof t=="number"?e(t):ht.INSTANCE}var Ei=class uo{static create(e,i,s=null){return Ci(i,r=>new uo(e,r,s))}constructor(e,i,s){this.c=e,this.d=i,this.f=s,this.type=12}cmp(e){return e.type!==this.type?this.type-e.type:Yt(this.c,this.d,e.c,e.d)}equals(e){return e.type===this.type?this.c===e.c&&this.d===e.d:!1}substituteConstants(){return this}evaluate(e){return typeof this.d=="string"?!1:parseFloat(e.getValue(this.c))>this.d}serialize(){return`${this.c} > ${this.d}`}keys(){return[this.c]}map(e){return e.mapGreater(this.c,this.d)}negate(){return this.f||(this.f=xs.create(this.c,this.d,this)),this.f}},Cs=class fo{static create(e,i,s=null){return Ci(i,r=>new fo(e,r,s))}constructor(e,i,s){this.c=e,this.d=i,this.f=s,this.type=13}cmp(e){return e.type!==this.type?this.type-e.type:Yt(this.c,this.d,e.c,e.d)}equals(e){return e.type===this.type?this.c===e.c&&this.d===e.d:!1}substituteConstants(){return this}evaluate(e){return typeof this.d=="string"?!1:parseFloat(e.getValue(this.c))>=this.d}serialize(){return`${this.c} >= ${this.d}`}keys(){return[this.c]}map(e){return e.mapGreaterEquals(this.c,this.d)}negate(){return this.f||(this.f=Es.create(this.c,this.d,this)),this.f}},Es=class po{static create(e,i,s=null){return Ci(i,r=>new po(e,r,s))}constructor(e,i,s){this.c=e,this.d=i,this.f=s,this.type=14}cmp(e){return e.type!==this.type?this.type-e.type:Yt(this.c,this.d,e.c,e.d)}equals(e){return e.type===this.type?this.c===e.c&&this.d===e.d:!1}substituteConstants(){return this}evaluate(e){return typeof this.d=="string"?!1:parseFloat(e.getValue(this.c))<this.d}serialize(){return`${this.c} < ${this.d}`}keys(){return[this.c]}map(e){return e.mapSmaller(this.c,this.d)}negate(){return this.f||(this.f=Cs.create(this.c,this.d,this)),this.f}},xs=class go{static create(e,i,s=null){return Ci(i,r=>new go(e,r,s))}constructor(e,i,s){this.c=e,this.d=i,this.f=s,this.type=15}cmp(e){return e.type!==this.type?this.type-e.type:Yt(this.c,this.d,e.c,e.d)}equals(e){return e.type===this.type?this.c===e.c&&this.d===e.d:!1}substituteConstants(){return this}evaluate(e){return typeof this.d=="string"?!1:parseFloat(e.getValue(this.c))<=this.d}serialize(){return`${this.c} <= ${this.d}`}keys(){return[this.c]}map(e){return e.mapSmallerEquals(this.c,this.d)}negate(){return this.f||(this.f=Ei.create(this.c,this.d,this)),this.f}},Ss=class mo{static create(e,i){return new mo(e,i)}constructor(e,i){this.d=e,this.f=i,this.type=7,this.c=null}cmp(e){if(e.type!==this.type)return this.type-e.type;if(this.d<e.d)return-1;if(this.d>e.d)return 1;const i=this.f?this.f.source:"",s=e.f?e.f.source:"";return i<s?-1:i>s?1:0}equals(e){if(e.type===this.type){const i=this.f?this.f.source:"",s=e.f?e.f.source:"";return this.d===e.d&&i===s}return!1}substituteConstants(){return this}evaluate(e){const i=e.getValue(this.d);return this.f?this.f.test(i):!1}serialize(){const e=this.f?`/${this.f.source}/${this.f.flags}`:"/invalid/";return`${this.d} =~ ${e}`}keys(){return[this.d]}map(e){return e.mapRegex(this.d,this.f)}negate(){return this.c||(this.c=Zc.create(this)),this.c}},Zc=class zs{static create(e){return new zs(e)}constructor(e){this.c=e,this.type=8}cmp(e){return e.type!==this.type?this.type-e.type:this.c.cmp(e.c)}equals(e){return e.type===this.type?this.c.equals(e.c):!1}substituteConstants(){return this}evaluate(e){return!this.c.evaluate(e)}serialize(){return`!(${this.c.serialize()})`}keys(){return this.c.keys()}map(e){return new zs(this.c.map(e))}negate(){return this.c}};function Dn(t){let e=null;for(let i=0,s=t.length;i<s;i++){const r=t[i].substituteConstants();if(t[i]!==r&&e===null){e=[];for(let n=0;n<i;n++)e[n]=t[n]}e!==null&&(e[i]=r)}return e===null?t:e}var An=class ye{static create(e,i,s){return ye.d(e,i,s)}constructor(e,i){this.expr=e,this.c=i,this.type=6}cmp(e){if(e.type!==this.type)return this.type-e.type;if(this.expr.length<e.expr.length)return-1;if(this.expr.length>e.expr.length)return 1;for(let i=0,s=this.expr.length;i<s;i++){const r=Fe(this.expr[i],e.expr[i]);if(r!==0)return r}return 0}equals(e){if(e.type===this.type){if(this.expr.length!==e.expr.length)return!1;for(let i=0,s=this.expr.length;i<s;i++)if(!this.expr[i].equals(e.expr[i]))return!1;return!0}return!1}substituteConstants(){const e=Dn(this.expr);return e===this.expr?this:ye.create(e,this.c,!1)}evaluate(e){for(let i=0,s=this.expr.length;i<s;i++)if(!this.expr[i].evaluate(e))return!1;return!0}static d(e,i,s){const r=[];let n=!1;for(const o of e)if(o){if(o.type===1){n=!0;continue}if(o.type===0)return ht.INSTANCE;if(o.type===6){r.push(...o.expr);continue}r.push(o)}if(r.length===0&&n)return dt.INSTANCE;if(r.length!==0){if(r.length===1)return r[0];r.sort(Fe);for(let o=1;o<r.length;o++)r[o-1].equals(r[o])&&(r.splice(o,1),o--);if(r.length===1)return r[0];for(;r.length>1;){const o=r[r.length-1];if(o.type!==9)break;r.pop();const a=r.pop(),c=r.length===0,h=Ps.create(o.expr.map(u=>ye.create([u,a],null,s)),null,c);h&&(r.push(h),r.sort(Fe))}if(r.length===1)return r[0];if(s){for(let o=0;o<r.length;o++)for(let a=o+1;a<r.length;a++)if(r[o].negate().equals(r[a]))return ht.INSTANCE;if(r.length===1)return r[0]}return new ye(r,i)}}serialize(){return this.expr.map(e=>e.serialize()).join(" && ")}keys(){const e=[];for(const i of this.expr)e.push(...i.keys());return e}map(e){return new ye(this.expr.map(i=>i.map(e)),null)}negate(){if(!this.c){const e=[];for(const i of this.expr)e.push(i.negate());this.c=Ps.create(e,this,!0)}return this.c}},Ps=class ee{static create(e,i,s){return ee.d(e,i,s)}constructor(e,i){this.expr=e,this.c=i,this.type=9}cmp(e){if(e.type!==this.type)return this.type-e.type;if(this.expr.length<e.expr.length)return-1;if(this.expr.length>e.expr.length)return 1;for(let i=0,s=this.expr.length;i<s;i++){const r=Fe(this.expr[i],e.expr[i]);if(r!==0)return r}return 0}equals(e){if(e.type===this.type){if(this.expr.length!==e.expr.length)return!1;for(let i=0,s=this.expr.length;i<s;i++)if(!this.expr[i].equals(e.expr[i]))return!1;return!0}return!1}substituteConstants(){const e=Dn(this.expr);return e===this.expr?this:ee.create(e,this.c,!1)}evaluate(e){for(let i=0,s=this.expr.length;i<s;i++)if(this.expr[i].evaluate(e))return!0;return!1}static d(e,i,s){let r=[],n=!1;if(e){for(let o=0,a=e.length;o<a;o++){const c=e[o];if(c){if(c.type===0){n=!0;continue}if(c.type===1)return dt.INSTANCE;if(c.type===9){r=r.concat(c.expr);continue}r.push(c)}}if(r.length===0&&n)return ht.INSTANCE;r.sort(Fe)}if(r.length!==0){if(r.length===1)return r[0];for(let o=1;o<r.length;o++)r[o-1].equals(r[o])&&(r.splice(o,1),o--);if(r.length===1)return r[0];if(s){for(let o=0;o<r.length;o++)for(let a=o+1;a<r.length;a++)if(r[o].negate().equals(r[a]))return dt.INSTANCE;if(r.length===1)return r[0]}return new ee(r,i)}}serialize(){return this.expr.map(e=>e.serialize()).join(" || ")}keys(){const e=[];for(const i of this.expr)e.push(...i.keys());return e}map(e){return new ee(this.expr.map(i=>i.map(e)),null)}negate(){if(!this.c){const e=[];for(const i of this.expr)e.push(i.negate());for(;e.length>1;){const i=e.shift(),s=e.shift(),r=[];for(const n of On(i))for(const o of On(s))r.push(An.create([n,o],null,!1));e.unshift(ee.create(r,null,!1))}this.c=ee.create(e,this,!0)}return this.c}},Mn=class Bi extends Ue{static{this.d=[]}static all(){return Bi.d.values()}constructor(e,i,s){super(e,null),this.f=i,typeof s=="object"?Bi.d.push({...s,key:e}):s!==!0&&Bi.d.push({key:e,description:s,type:i!=null?typeof i:void 0})}bindTo(e){return e.createKey(this.key,this.f)}getValue(e){return e.getContextKeyValue(this.key)}toNegated(){return this.negate()}isEqualTo(e){return ys.create(this.key,e)}notEqualsTo(e){return bs.create(this.key,e)}greater(e){return Ei.create(this.key,e)}},C0=It("contextKeyService");function In(t,e){return t<e?-1:t>e?1:0}function Yt(t,e,i,s){return t<i?-1:t>i?1:e<s?-1:e>s?1:0}function On(t){return t.type===9?t.expr:[t]}var Jt=It("logService"),E0=It("loggerService");function _n(t){return ir(t)}var k;(function(t){t[t.Off=0]="Off",t[t.Trace=1]="Trace",t[t.Debug=2]="Debug",t[t.Info=3]="Info",t[t.Warning=4]="Warning",t[t.Error=5]="Error"})(k||(k={}));var Rn=k.Info;function tl(t,e){return t!==k.Off&&t<=e}function me(t,e=!1){let i="";for(let s=0;s<t.length;s++){let r=t[s];if(r instanceof Error&&(r=Cn(r,e)),typeof r=="object")try{r=JSON.stringify(r)}catch{}i+=(s>0?" ":"")+r}return i}var Nn=class extends V{constructor(){super(...arguments),this.b=Rn,this.c=this.B(new x),this.onDidChangeLogLevel=this.c.event}setLevel(t){this.b!==t&&(this.b=t,this.c.fire(this.b))}getLevel(){return this.b}f(t){return tl(this.b,t)}g(t){return this.q.isDisposed?!1:this.f(t)}},el=class extends Nn{constructor(t){super(),this.h=t}f(t){return this.h||super.f(t)}trace(t,...e){this.g(k.Trace)&&this.m(k.Trace,me([t,...e],!0))}debug(t,...e){this.g(k.Debug)&&this.m(k.Debug,me([t,...e]))}info(t,...e){this.g(k.Info)&&this.m(k.Info,me([t,...e]))}warn(t,...e){this.g(k.Warning)&&this.m(k.Warning,me([t,...e]))}error(t,...e){if(this.g(k.Error))if(t instanceof Error){const i=Array.prototype.slice.call(arguments);i[0]=t.stack,this.m(k.Error,me(i))}else this.m(k.Error,me([t,...e]))}flush(){}},il=class extends Nn{constructor(t){super(),this.h=t,t.length&&this.setLevel(t[0].getLevel())}setLevel(t){for(const e of this.h)e.setLevel(t);super.setLevel(t)}trace(t,...e){for(const i of this.h)i.trace(t,...e)}debug(t,...e){for(const i of this.h)i.debug(t,...e)}info(t,...e){for(const i of this.h)i.info(t,...e)}warn(t,...e){for(const i of this.h)i.warn(t,...e)}error(t,...e){for(const i of this.h)i.error(t,...e)}flush(){for(const t of this.h)t.flush()}dispose(){for(const t of this.h)t.dispose();super.dispose()}},sl=class extends V{constructor(t,e,i){if(super(),this.j=t,this.m=e,this.b=new zi,this.f=this.B(new x),this.onDidChangeLoggers=this.f.event,this.g=this.B(new x),this.onDidChangeLogLevel=this.g.event,this.h=this.B(new x),this.onDidChangeVisibility=this.h.event,i)for(const s of i)this.b.set(s.resource,{logger:void 0,info:s})}n(t){return ne(t)?[...this.b.values()].find(e=>e.info.id===t):this.b.get(t)}getLogger(t){return this.n(t)?.logger}createLogger(t,e){const i=this.s(t),s=ne(t)?t:e?.id??Nc(i.toString()).toString(16);let r=this.b.get(i)?.logger;const n=e?.logLevel==="always"?k.Trace:e?.logLevel;r||(r=this.t(i,n??this.getLogLevel(i)??this.j,{...e,id:s}));const o={logger:r,info:{resource:i,id:s,logLevel:n,name:e?.name,hidden:e?.hidden,group:e?.group,extensionId:e?.extensionId,when:e?.when}};return this.registerLogger(o.info),this.b.set(i,o),r}s(t){return ne(t)?Ct(this.m,`${t}.log`):t}setLogLevel(t,e){if(j.isUri(t)){const i=t,s=e,r=this.b.get(i);r&&s!==r.info.logLevel&&(r.info.logLevel=s===this.j?void 0:s,r.logger?.setLevel(s),this.b.set(r.info.resource,r),this.g.fire([i,s]))}else{this.j=t;for(const[i,s]of this.b.entries())this.b.get(i)?.info.logLevel===void 0&&s.logger?.setLevel(this.j);this.g.fire(this.j)}}setVisibility(t,e){const i=this.n(t);i&&e!==!i.info.hidden&&(i.info.hidden=!e,this.b.set(i.info.resource,i),this.h.fire([i.info.resource,e]))}getLogLevel(t){let e;return t&&(e=this.b.get(t)?.info.logLevel),e??this.j}registerLogger(t){const e=this.b.get(t.resource);e?e.info.hidden!==t.hidden&&this.setVisibility(t.resource,!t.hidden):(this.b.set(t.resource,{info:t,logger:void 0}),this.f.fire({added:[t],removed:[]}))}deregisterLogger(t){const e=this.s(t),i=this.b.get(e);i&&(i.logger&&i.logger.dispose(),this.b.delete(e),this.f.fire({added:[],removed:[i.info]}))}*getRegisteredLoggers(){for(const t of this.b.values())yield t.info}getRegisteredLogger(t){return this.b.get(t)?.info}dispose(){this.b.forEach(t=>t.logger?.dispose()),this.b.clear(),super.dispose()}};function rl(t){if(t.verbose)return k.Trace;if(typeof t.logLevel=="string"){const e=ol(t.logLevel.toLowerCase());if(e!==void 0)return e}return Rn}function nl(t){switch(t){case k.Trace:return"trace";case k.Debug:return"debug";case k.Info:return"info";case k.Warning:return"warn";case k.Error:return"error";case k.Off:return"off"}}function ol(t){switch(t){case"trace":return k.Trace;case"debug":return k.Debug;case"info":return k.Info;case"warn":return k.Warning;case"error":return k.Error;case"critical":return k.Error;case"off":return k.Off}}var x0=new Mn("logLevel",nl(k.Info)),al=class{constructor(t,e){this.a=t,this.b=e}listen(t,e){const i=this.b(t);switch(e){case"onDidChangeLoggers":return Y.map(this.a.onDidChangeLoggers,s=>({added:[...s.added].map(r=>this.c(r,i)),removed:[...s.removed].map(r=>this.c(r,i))}));case"onDidChangeVisibility":return Y.map(this.a.onDidChangeVisibility,s=>[i.transformOutgoingURI(s[0]),s[1]]);case"onDidChangeLogLevel":return Y.map(this.a.onDidChangeLogLevel,s=>_n(s)?s:[i.transformOutgoingURI(s[0]),s[1]])}throw new Error(`Event not found: ${e}`)}async call(t,e,i){const s=this.b(t);switch(e){case"setLogLevel":return _n(i[0])?this.a.setLogLevel(i[0]):this.a.setLogLevel(j.revive(s.transformIncoming(i[0][0])),i[0][1]);case"getRegisteredLoggers":return Promise.resolve([...this.a.getRegisteredLoggers()].map(r=>this.c(r,s)))}throw new Error(`Call not found: ${e}`)}c(t,e){return{...t,resource:e.transformOutgoingURI(t.resource)}}},hl=class extends V{constructor(t,e=[]){super(),this.a=new il([t,...e]),this.B(t.onDidChangeLogLevel(i=>this.setLevel(i)))}get onDidChangeLogLevel(){return this.a.onDidChangeLogLevel}setLevel(t){this.a.setLevel(t)}getLevel(){return this.a.getLevel()}trace(t,...e){this.a.trace(t,...e)}debug(t,...e){this.a.debug(t,...e)}info(t,...e){this.a.info(t,...e)}warn(t,...e){this.a.warn(t,...e)}error(t,...e){this.a.error(t,...e)}flush(){this.a.flush()}},cl=function(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID.bind(crypto);const t=new Uint8Array(16),e=[];for(let i=0;i<256;i++)e.push(i.toString(16).padStart(2,"0"));return function(){crypto.getRandomValues(t),t[6]=t[6]&15|64,t[8]=t[8]&63|128;let s=0,r="";return r+=e[t[s++]],r+=e[t[s++]],r+=e[t[s++]],r+=e[t[s++]],r+="-",r+=e[t[s++]],r+=e[t[s++]],r+="-",r+=e[t[s++]],r+=e[t[s++]],r+="-",r+=e[t[s++]],r+=e[t[s++]],r+="-",r+=e[t[s++]],r+=e[t[s++]],r+=e[t[s++]],r+=e[t[s++]],r+=e[t[s++]],r+=e[t[s++]],r}}(),ll=class{constructor(){this.b="",this.c=0}reset(t){return this.b=t,this.c=0,this}next(){return this.c+=1,this}hasNext(){return this.c<this.b.length-1}cmp(t){const e=t.charCodeAt(0),i=this.b.charCodeAt(this.c);return e-i}value(){return this.b[this.c]}},ul=class{constructor(t=!0){this.e=t}reset(t){return this.b=t,this.c=0,this.d=0,this.next()}hasNext(){return this.d<this.b.length}next(){this.c=this.d;let t=!0;for(;this.d<this.b.length;this.d++)if(this.b.charCodeAt(this.d)===46)if(t)this.c++;else break;else t=!1;return this}cmp(t){return this.e?rs(t,this.b,0,t.length,this.c,this.d):Ie(t,this.b,0,t.length,this.c,this.d)}value(){return this.b.substring(this.c,this.d)}},Tn=class{constructor(t=!0,e=!0){this.f=t,this.g=e}reset(t){this.d=0,this.e=0,this.b=t,this.c=t.length;for(let e=t.length-1;e>=0;e--,this.c--){const i=this.b.charCodeAt(e);if(!(i===47||this.f&&i===92))break}return this.next()}hasNext(){return this.e<this.c}next(){this.d=this.e;let t=!0;for(;this.e<this.c;this.e++){const e=this.b.charCodeAt(this.e);if(e===47||this.f&&e===92)if(t)this.d++;else break;else t=!1}return this}cmp(t){return this.g?rs(t,this.b,0,t.length,this.d,this.e):Ie(t,this.b,0,t.length,this.d,this.e)}value(){return this.b.substring(this.d,this.e)}},Bn;(function(t){t[t.Scheme=1]="Scheme",t[t.Authority=2]="Authority",t[t.Path=3]="Path",t[t.Query=4]="Query",t[t.Fragment=5]="Fragment"})(Bn||(Bn={}));var fl=class{constructor(t,e){this.f=t,this.g=e,this.d=[],this.e=0}reset(t){return this.c=t,this.d=[],this.c.scheme&&this.d.push(1),this.c.authority&&this.d.push(2),this.c.path&&(this.b=new Tn(!1,!this.f(t)),this.b.reset(t.path),this.b.value()&&this.d.push(3)),this.g(t)||(this.c.query&&this.d.push(4),this.c.fragment&&this.d.push(5)),this.e=0,this}next(){return this.d[this.e]===3&&this.b.hasNext()?this.b.next():this.e+=1,this}hasNext(){return this.d[this.e]===3&&this.b.hasNext()||this.e<this.d.length-1}cmp(t){if(this.d[this.e]===1)return Dr(t,this.c.scheme);if(this.d[this.e]===2)return Dr(t,this.c.authority);if(this.d[this.e]===3)return this.b.cmp(t);if(this.d[this.e]===4)return ss(t,this.c.query);if(this.d[this.e]===5)return ss(t,this.c.fragment);throw new Error}value(){if(this.d[this.e]===1)return this.c.scheme;if(this.d[this.e]===2)return this.c.authority;if(this.d[this.e]===3)return this.b.value();if(this.d[this.e]===4)return this.c.query;if(this.d[this.e]===5)return this.c.fragment;throw new Error}},Kt=class Hs{static{this.Val=Symbol("undefined_placeholder")}static wrap(e){return e===void 0?Hs.Val:e}static unwrap(e){return e===Hs.Val?void 0:e}},xi=class{constructor(){this.height=1,this.value=void 0,this.key=void 0,this.left=void 0,this.mid=void 0,this.right=void 0}isEmpty(){return!this.left&&!this.mid&&!this.right&&this.value===void 0}rotateLeft(){const t=this.right;return this.right=t.left,t.left=this,this.updateHeight(),t.updateHeight(),t}rotateRight(){const t=this.left;return this.left=t.right,t.right=this,this.updateHeight(),t.updateHeight(),t}updateHeight(){this.height=1+Math.max(this.heightLeft,this.heightRight)}balanceFactor(){return this.heightRight-this.heightLeft}get heightLeft(){return this.left?.height??0}get heightRight(){return this.right?.height??0}},Fn;(function(t){t[t.Left=-1]="Left",t[t.Mid=0]="Mid",t[t.Right=1]="Right"})(Fn||(Fn={}));var $s=class ii{static forUris(e=()=>!1,i=()=>!1){return new ii(new fl(e,i))}static forPaths(e=!1){return new ii(new Tn(void 0,!e))}static forStrings(){return new ii(new ll)}static forConfigKeys(){return new ii(new ul)}constructor(e){this.b=e}clear(){this.c=void 0}fill(e,i){if(i){const s=i.slice(0);Gs(s);for(const r of s)this.set(r,e)}else{const s=e.slice(0);Gs(s);for(const r of s)this.set(r[0],r[1])}}set(e,i){const s=this.b.reset(e);let r;this.c||(this.c=new xi,this.c.segment=s.value());const n=[];for(r=this.c;;){const a=s.cmp(r.segment);if(a>0)r.left||(r.left=new xi,r.left.segment=s.value()),n.push([-1,r]),r=r.left;else if(a<0)r.right||(r.right=new xi,r.right.segment=s.value()),n.push([1,r]),r=r.right;else if(s.hasNext())s.next(),r.mid||(r.mid=new xi,r.mid.segment=s.value()),n.push([0,r]),r=r.mid;else break}const o=Kt.unwrap(r.value);r.value=Kt.wrap(i),r.key=e;for(let a=n.length-1;a>=0;a--){const c=n[a][1];c.updateHeight();const h=c.balanceFactor();if(h<-1||h>1){const u=n[a][0],l=n[a+1][0];if(u===1&&l===1)n[a][1]=c.rotateLeft();else if(u===-1&&l===-1)n[a][1]=c.rotateRight();else if(u===1&&l===-1)c.right=n[a+1][1]=n[a+1][1].rotateRight(),n[a][1]=c.rotateLeft();else if(u===-1&&l===1)c.left=n[a+1][1]=n[a+1][1].rotateLeft(),n[a][1]=c.rotateRight();else throw new Error;if(a>0)switch(n[a-1][0]){case-1:n[a-1][1].left=n[a][1];break;case 1:n[a-1][1].right=n[a][1];break;case 0:n[a-1][1].mid=n[a][1];break}else this.c=n[0][1]}}return o}get(e){return Kt.unwrap(this.d(e)?.value)}d(e){const i=this.b.reset(e);let s=this.c;for(;s;){const r=i.cmp(s.segment);if(r>0)s=s.left;else if(r<0)s=s.right;else if(i.hasNext())i.next(),s=s.mid;else break}return s}has(e){const i=this.d(e);return!(i?.value===void 0&&i?.mid===void 0)}delete(e){return this.e(e,!1)}deleteSuperstr(e){return this.e(e,!0)}e(e,i){const s=this.b.reset(e),r=[];let n=this.c;for(;n;){const o=s.cmp(n.segment);if(o>0)r.push([-1,n]),n=n.left;else if(o<0)r.push([1,n]),n=n.right;else if(s.hasNext())s.next(),r.push([0,n]),n=n.mid;else break}if(n){if(i?(n.left=void 0,n.mid=void 0,n.right=void 0,n.height=1):(n.key=void 0,n.value=void 0),!n.mid&&!n.value)if(n.left&&n.right){const o=[[1,n]],a=this.f(n.right,o);if(a.key){n.key=a.key,n.value=a.value,n.segment=a.segment;const c=a.right;if(o.length>1){const[u,l]=o[o.length-1];switch(u){case-1:l.left=c;break;case 0:er(!1);case 1:er(!1)}}else n.right=c;const h=this.g(o);if(r.length>0){const[u,l]=r[r.length-1];switch(u){case-1:l.left=h;break;case 0:l.mid=h;break;case 1:l.right=h;break}}else this.c=h}}else{const o=n.left??n.right;if(r.length>0){const[a,c]=r[r.length-1];switch(a){case-1:c.left=o;break;case 0:c.mid=o;break;case 1:c.right=o;break}}else this.c=o}this.c=this.g(r)??this.c}}f(e,i){for(;e.left;)i.push([-1,e]),e=e.left;return e}g(e){for(let i=e.length-1;i>=0;i--){const s=e[i][1];s.updateHeight();const r=s.balanceFactor();if(r>1?(s.right.balanceFactor()>=0||(s.right=s.right.rotateRight()),e[i][1]=s.rotateLeft()):r<-1&&(s.left.balanceFactor()<=0||(s.left=s.left.rotateLeft()),e[i][1]=s.rotateRight()),i>0)switch(e[i-1][0]){case-1:e[i-1][1].left=e[i][1];break;case 1:e[i-1][1].right=e[i][1];break;case 0:e[i-1][1].mid=e[i][1];break}else return e[0][1]}}findSubstr(e){const i=this.b.reset(e);let s=this.c,r;for(;s;){const n=i.cmp(s.segment);if(n>0)s=s.left;else if(n<0)s=s.right;else if(i.hasNext())i.next(),r=Kt.unwrap(s.value)||r,s=s.mid;else break}return s&&Kt.unwrap(s.value)||r}findSuperstr(e){return this.h(e,!1)}h(e,i){const s=this.b.reset(e);let r=this.c;for(;r;){const n=s.cmp(r.segment);if(n>0)r=r.left;else if(n<0)r=r.right;else if(s.hasNext())s.next(),r=r.mid;else return r.mid?this.j(r.mid):i?Kt.unwrap(r.value):void 0}}hasElementOrSubtree(e){return this.h(e,!0)!==void 0}forEach(e){for(const[i,s]of this)e(s,i)}*[Symbol.iterator](){yield*this.j(this.c)}j(e){const i=[];return this.l(e,i),i[Symbol.iterator]()}l(e,i){e&&(e.left&&this.l(e.left,i),e.value!==void 0&&i.push([e.key,Kt.unwrap(e.value)]),e.mid&&this.l(e.mid,i),e.right&&this.l(e.right,i))}_isBalanced(){const e=i=>{if(!i)return!0;const s=i.balanceFactor();return s<-1||s>1?!1:e(i.left)&&e(i.right)};return e(this.c)}},S0=It("fileService"),Un;(function(t){t[t.Unknown=0]="Unknown",t[t.File=1]="File",t[t.Directory=2]="Directory",t[t.SymbolicLink=64]="SymbolicLink"})(Un||(Un={}));var jn;(function(t){t[t.Readonly=1]="Readonly",t[t.Locked=2]="Locked"})(jn||(jn={}));var Wn;(function(t){t[t.UPDATED=2]="UPDATED",t[t.ADDED=4]="ADDED",t[t.DELETED=8]="DELETED"})(Wn||(Wn={}));var zn;(function(t){t[t.None=0]="None",t[t.FileReadWrite=2]="FileReadWrite",t[t.FileOpenReadWriteClose=4]="FileOpenReadWriteClose",t[t.FileReadStream=16]="FileReadStream",t[t.FileFolderCopy=8]="FileFolderCopy",t[t.PathCaseSensitive=1024]="PathCaseSensitive",t[t.Readonly=2048]="Readonly",t[t.Trash=4096]="Trash",t[t.FileWriteUnlock=8192]="FileWriteUnlock",t[t.FileAtomicRead=16384]="FileAtomicRead",t[t.FileAtomicWrite=32768]="FileAtomicWrite",t[t.FileAtomicDelete=65536]="FileAtomicDelete",t[t.FileClone=131072]="FileClone",t[t.FileRealpath=262144]="FileRealpath"})(zn||(zn={}));var Hn;(function(t){t.FileExists="EntryExists",t.FileNotFound="EntryNotFound",t.FileNotADirectory="EntryNotADirectory",t.FileIsADirectory="EntryIsADirectory",t.FileExceedsStorageQuota="EntryExceedsStorageQuota",t.FileTooLarge="EntryTooLarge",t.FileWriteLocked="EntryWriteLocked",t.NoPermissions="NoPermissions",t.Unavailable="Unavailable",t.Unknown="Unknown"})(Hn||(Hn={}));var qn;(function(t){t[t.CREATE=0]="CREATE",t[t.DELETE=1]="DELETE",t[t.MOVE=2]="MOVE",t[t.COPY=3]="COPY",t[t.WRITE=4]="WRITE"})(qn||(qn={}));var Vn;(function(t){t[t.UPDATED=0]="UPDATED",t[t.ADDED=1]="ADDED",t[t.DELETED=2]="DELETED"})(Vn||(Vn={}));var P0=class Fi{static{this.a=null}constructor(e,i){this.c=i,this.b=void 0,this.d=new se(()=>{const s=$s.forUris(()=>this.c);return s.fill(this.rawAdded.map(r=>[r,!0])),s}),this.f=new se(()=>{const s=$s.forUris(()=>this.c);return s.fill(this.rawUpdated.map(r=>[r,!0])),s}),this.g=new se(()=>{const s=$s.forUris(()=>this.c);return s.fill(this.rawDeleted.map(r=>[r,!0])),s}),this.rawAdded=[],this.rawUpdated=[],this.rawDeleted=[];for(const s of e){switch(s.type){case 1:this.rawAdded.push(s.resource);break;case 0:this.rawUpdated.push(s.resource);break;case 2:this.rawDeleted.push(s.resource);break}this.b!==Fi.a&&(typeof s.cId=="number"?this.b===void 0?this.b=s.cId:this.b!==s.cId&&(this.b=Fi.a):this.b!==void 0&&(this.b=Fi.a))}}contains(e,...i){return this.h(e,{includeChildren:!1},...i)}affects(e,...i){return this.h(e,{includeChildren:!0},...i)}h(e,i,...s){if(!e)return!1;const r=s.length>0;return!!((!r||s.includes(1))&&(this.d.value.get(e)||i.includeChildren&&this.d.value.findSuperstr(e))||(!r||s.includes(0))&&(this.f.value.get(e)||i.includeChildren&&this.f.value.findSuperstr(e))||(!r||s.includes(2))&&(this.g.value.findSubstr(e)||i.includeChildren&&this.g.value.findSuperstr(e)))}gotAdded(){return this.rawAdded.length>0}gotDeleted(){return this.rawDeleted.length>0}gotUpdated(){return this.rawUpdated.length>0}correlates(e){return this.b===e}hasCorrelation(){return typeof this.b=="number"}},Xn;(function(t){t[t.FILE_IS_DIRECTORY=0]="FILE_IS_DIRECTORY",t[t.FILE_NOT_FOUND=1]="FILE_NOT_FOUND",t[t.FILE_NOT_MODIFIED_SINCE=2]="FILE_NOT_MODIFIED_SINCE",t[t.FILE_MODIFIED_SINCE=3]="FILE_MODIFIED_SINCE",t[t.FILE_MOVE_CONFLICT=4]="FILE_MOVE_CONFLICT",t[t.FILE_WRITE_LOCKED=5]="FILE_WRITE_LOCKED",t[t.FILE_PERMISSION_DENIED=6]="FILE_PERMISSION_DENIED",t[t.FILE_TOO_LARGE=7]="FILE_TOO_LARGE",t[t.FILE_INVALID_PATH=8]="FILE_INVALID_PATH",t[t.FILE_NOT_DIRECTORY=9]="FILE_NOT_DIRECTORY",t[t.FILE_OTHER_ERROR=10]="FILE_OTHER_ERROR"})(Xn||(Xn={}));var Gn;(function(t){t[t.FILE=0]="FILE",t[t.FOLDER=1]="FOLDER",t[t.ROOT_FOLDER=2]="ROOT_FOLDER"})(Gn||(Gn={}));var dl=class ct{static{this.KB=1024}static{this.MB=ct.KB*ct.KB}static{this.GB=ct.MB*ct.KB}static{this.TB=ct.GB*ct.KB}static formatSize(e){return ir(e)||(e=0),e<ct.KB?y(2053,null,e.toFixed(0)):e<ct.MB?y(2054,null,(e/ct.KB).toFixed(2)):e<ct.GB?y(2055,null,(e/ct.MB).toFixed(2)):e<ct.TB?y(2056,null,(e/ct.GB).toFixed(2)):y(2057,null,(e/ct.TB).toFixed(2))}},Ot;(function(t){t[t.Trace=0]="Trace",t[t.Debug=1]="Debug",t[t.Info=2]="Info",t[t.Warning=3]="Warning",t[t.Error=4]="Error",t[t.Critical=5]="Critical",t[t.Off=6]="Off"})(Ot||(Ot={}));async function pl(t,e,i,s,r){try{const n=await import("@vscode/spdlog");n.setFlushOn(Ot.Trace);const o=await n.createAsyncRotatingLogger(t,e,i,s);return r?o.clearFormatters():o.setPattern("%Y-%m-%d %H:%M:%S.%e [%l] %v"),o}catch(n){console.error(n)}return null}function Qn(t,e,i){switch(e){case k.Trace:t.trace(i);break;case k.Debug:t.debug(i);break;case k.Info:t.info(i);break;case k.Warning:t.warn(i);break;case k.Error:t.error(i);break;case k.Off:break;default:throw new Error(`Invalid log level ${e}`)}}function Yn(t,e){switch(e){case k.Trace:t.setLevel(Ot.Trace);break;case k.Debug:t.setLevel(Ot.Debug);break;case k.Info:t.setLevel(Ot.Info);break;case k.Warning:t.setLevel(Ot.Warning);break;case k.Error:t.setLevel(Ot.Error);break;case k.Off:t.setLevel(Ot.Off);break;default:throw new Error(`Invalid log level ${e}`)}}var gl=class extends el{constructor(t,e,i,s,r){super(),this.n=[],this.setLevel(r),this.r=this.t(t,e,i,s),this.B(this.onDidChangeLogLevel(n=>{this.s&&Yn(this.s,n)}))}async t(t,e,i,s){const r=i?6:1,n=30/r*dl.MB,o=await pl(t,e,n,r,s);if(o){this.s=o,Yn(this.s,this.getLevel());for(const{level:a,message:c}of this.n)Qn(this.s,a,c);this.n=[]}}m(t,e){this.s?Qn(this.s,t,e):this.getLevel()<=t&&this.n.push({level:t,message:e})}flush(){this.s?this.w():this.r.then(()=>this.w())}dispose(){this.s?this.y():this.r.then(()=>this.y()),super.dispose()}w(){this.s&&this.s.flush()}y(){this.s&&(this.s.drop(),this.s=void 0)}},ml=class extends sl{t(t,e,i){return new gl(cl(),t.fsPath,!i?.donotRotate,!!i?.donotUseFormatters,e)}},pt,Ls=globalThis.vscode;if(typeof Ls<"u"&&typeof Ls.context<"u"){const t=Ls.context.configuration();if(t)pt=t.product;else throw new Error("Sandbox: unable to resolve product configuration from preload script.")}else if(globalThis._VSCODE_PRODUCT_JSON&&globalThis._VSCODE_PACKAGE_JSON){if(pt=globalThis._VSCODE_PRODUCT_JSON,Pt.VSCODE_DEV&&Object.assign(pt,{nameShort:`${pt.nameShort} Dev`,nameLong:`${pt.nameLong} Dev`,dataFolderName:`${pt.dataFolderName}-dev`,serverDataFolderName:pt.serverDataFolderName?`${pt.serverDataFolderName}-dev`:void 0}),!pt.version){const t=globalThis._VSCODE_PACKAGE_JSON;Object.assign(pt,{version:t.version})}}else pt={},Object.keys(pt).length===0&&Object.assign(pt,{version:"1.102.0-dev",nameShort:"Code - OSS Dev",nameLong:"Code - OSS Dev",applicationName:"code-oss",dataFolderName:".vscode-oss",urlProtocol:"code-oss",reportIssueUrl:"https://github.com/microsoft/vscode/issues/new",licenseName:"MIT",licenseUrl:"https://github.com/microsoft/vscode/blob/main/LICENSE.txt",serverLicenseUrl:"https://github.com/microsoft/vscode/blob/main/LICENSE.txt"});var vl=pt,wl=class{constructor(){this.a=new Map}add(t,e){qi(ne(t)),qi(Xo(e)),qi(!this.a.has(t),"There is already an extension with this id"),this.a.set(t,e)}knows(t){return this.a.has(t)}as(t){return this.a.get(t)||null}dispose(){this.a.forEach(t=>{Vi(t.dispose)&&t.dispose()}),this.a.clear()}},yl=new wl,$0=new Mn("terminalTabFocusMode",!1,!0),Jn;(function(t){t.AutomationProfile="terminal.integrated.automationProfile.",t.DefaultProfile="terminal.integrated.defaultProfile.",t.Profiles="terminal.integrated.profiles."})(Jn||(Jn={}));var Kn;(function(t){t.SendKeybindingsToShell="terminal.integrated.sendKeybindingsToShell",t.AutomationProfileLinux="terminal.integrated.automationProfile.linux",t.AutomationProfileMacOs="terminal.integrated.automationProfile.osx",t.AutomationProfileWindows="terminal.integrated.automationProfile.windows",t.ProfilesWindows="terminal.integrated.profiles.windows",t.ProfilesMacOs="terminal.integrated.profiles.osx",t.ProfilesLinux="terminal.integrated.profiles.linux",t.DefaultProfileLinux="terminal.integrated.defaultProfile.linux",t.DefaultProfileMacOs="terminal.integrated.defaultProfile.osx",t.DefaultProfileWindows="terminal.integrated.defaultProfile.windows",t.UseWslProfiles="terminal.integrated.useWslProfiles",t.TabsDefaultColor="terminal.integrated.tabs.defaultColor",t.TabsDefaultIcon="terminal.integrated.tabs.defaultIcon",t.TabsEnabled="terminal.integrated.tabs.enabled",t.TabsEnableAnimation="terminal.integrated.tabs.enableAnimation",t.TabsHideCondition="terminal.integrated.tabs.hideCondition",t.TabsShowActiveTerminal="terminal.integrated.tabs.showActiveTerminal",t.TabsShowActions="terminal.integrated.tabs.showActions",t.TabsLocation="terminal.integrated.tabs.location",t.TabsFocusMode="terminal.integrated.tabs.focusMode",t.MacOptionIsMeta="terminal.integrated.macOptionIsMeta",t.MacOptionClickForcesSelection="terminal.integrated.macOptionClickForcesSelection",t.AltClickMovesCursor="terminal.integrated.altClickMovesCursor",t.CopyOnSelection="terminal.integrated.copyOnSelection",t.EnableMultiLinePasteWarning="terminal.integrated.enableMultiLinePasteWarning",t.DrawBoldTextInBrightColors="terminal.integrated.drawBoldTextInBrightColors",t.FontFamily="terminal.integrated.fontFamily",t.FontSize="terminal.integrated.fontSize",t.LetterSpacing="terminal.integrated.letterSpacing",t.LineHeight="terminal.integrated.lineHeight",t.MinimumContrastRatio="terminal.integrated.minimumContrastRatio",t.TabStopWidth="terminal.integrated.tabStopWidth",t.FastScrollSensitivity="terminal.integrated.fastScrollSensitivity",t.MouseWheelScrollSensitivity="terminal.integrated.mouseWheelScrollSensitivity",t.BellDuration="terminal.integrated.bellDuration",t.FontWeight="terminal.integrated.fontWeight",t.FontWeightBold="terminal.integrated.fontWeightBold",t.CursorBlinking="terminal.integrated.cursorBlinking",t.CursorStyle="terminal.integrated.cursorStyle",t.CursorStyleInactive="terminal.integrated.cursorStyleInactive",t.CursorWidth="terminal.integrated.cursorWidth",t.Scrollback="terminal.integrated.scrollback",t.DetectLocale="terminal.integrated.detectLocale",t.DefaultLocation="terminal.integrated.defaultLocation",t.GpuAcceleration="terminal.integrated.gpuAcceleration",t.TerminalTitleSeparator="terminal.integrated.tabs.separator",t.TerminalTitle="terminal.integrated.tabs.title",t.TerminalDescription="terminal.integrated.tabs.description",t.RightClickBehavior="terminal.integrated.rightClickBehavior",t.MiddleClickBehavior="terminal.integrated.middleClickBehavior",t.Cwd="terminal.integrated.cwd",t.ConfirmOnExit="terminal.integrated.confirmOnExit",t.ConfirmOnKill="terminal.integrated.confirmOnKill",t.EnableBell="terminal.integrated.enableBell",t.EnableVisualBell="terminal.integrated.enableVisualBell",t.CommandsToSkipShell="terminal.integrated.commandsToSkipShell",t.AllowChords="terminal.integrated.allowChords",t.AllowMnemonics="terminal.integrated.allowMnemonics",t.TabFocusMode="terminal.integrated.tabFocusMode",t.EnvMacOs="terminal.integrated.env.osx",t.EnvLinux="terminal.integrated.env.linux",t.EnvWindows="terminal.integrated.env.windows",t.EnvironmentChangesIndicator="terminal.integrated.environmentChangesIndicator",t.EnvironmentChangesRelaunch="terminal.integrated.environmentChangesRelaunch",t.ShowExitAlert="terminal.integrated.showExitAlert",t.SplitCwd="terminal.integrated.splitCwd",t.WindowsEnableConpty="terminal.integrated.windowsEnableConpty",t.WindowsUseConptyDll="terminal.integrated.windowsUseConptyDll",t.WordSeparators="terminal.integrated.wordSeparators",t.EnableFileLinks="terminal.integrated.enableFileLinks",t.AllowedLinkSchemes="terminal.integrated.allowedLinkSchemes",t.UnicodeVersion="terminal.integrated.unicodeVersion",t.EnablePersistentSessions="terminal.integrated.enablePersistentSessions",t.PersistentSessionReviveProcess="terminal.integrated.persistentSessionReviveProcess",t.HideOnStartup="terminal.integrated.hideOnStartup",t.HideOnLastClosed="terminal.integrated.hideOnLastClosed",t.CustomGlyphs="terminal.integrated.customGlyphs",t.RescaleOverlappingGlyphs="terminal.integrated.rescaleOverlappingGlyphs",t.PersistentSessionScrollback="terminal.integrated.persistentSessionScrollback",t.InheritEnv="terminal.integrated.inheritEnv",t.ShowLinkHover="terminal.integrated.showLinkHover",t.IgnoreProcessNames="terminal.integrated.ignoreProcessNames",t.ShellIntegrationEnabled="terminal.integrated.shellIntegration.enabled",t.ShellIntegrationShowWelcome="terminal.integrated.shellIntegration.showWelcome",t.ShellIntegrationDecorationsEnabled="terminal.integrated.shellIntegration.decorationsEnabled",t.ShellIntegrationEnvironmentReporting="terminal.integrated.shellIntegration.environmentReporting",t.EnableImages="terminal.integrated.enableImages",t.SmoothScrolling="terminal.integrated.smoothScrolling",t.IgnoreBracketedPasteMode="terminal.integrated.ignoreBracketedPasteMode",t.FocusAfterRun="terminal.integrated.focusAfterRun",t.FontLigaturesEnabled="terminal.integrated.fontLigatures.enabled",t.FontLigaturesFeatureSettings="terminal.integrated.fontLigatures.featureSettings",t.FontLigaturesFallbackLigatures="terminal.integrated.fontLigatures.fallbackLigatures",t.DeveloperPtyHostLatency="terminal.integrated.developer.ptyHost.latency",t.DeveloperPtyHostStartupDelay="terminal.integrated.developer.ptyHost.startupDelay",t.DevMode="terminal.integrated.developer.devMode"})(Kn||(Kn={}));var Zn;(function(t){t.Bash="bash",t.Fish="fish",t.Sh="sh",t.Csh="csh",t.Ksh="ksh",t.Zsh="zsh"})(Zn||(Zn={}));var t1;(function(t){t.CommandPrompt="cmd",t.Wsl="wsl",t.GitBash="gitbash"})(t1||(t1={}));var e1;(function(t){t.PowerShell="pwsh",t.Python="python",t.Julia="julia",t.NuShell="nu",t.Node="node"})(e1||(e1={}));var ve;(function(t){t[t.Api=0]="Api",t[t.Process=1]="Process",t[t.Sequence=2]="Sequence",t[t.Config=3]="Config"})(ve||(ve={}));var Zt;(function(t){t.LocalPty="localPty",t.PtyHost="ptyHost",t.PtyHostWindow="ptyHostWindow",t.Logger="logger",t.Heartbeat="heartbeat"})(Zt||(Zt={}));var i1;(function(t){t.Cwd="cwd",t.InitialCwd="initialCwd",t.FixedDimensions="fixedDimensions",t.Title="title",t.ShellType="shellType",t.HasChildProcesses="hasChildProcesses",t.ResolvedShellLaunchConfig="resolvedShellLaunchConfig",t.OverrideDimensions="overrideDimensions",t.FailedShellIntegrationActivation="failedShellIntegrationActivation",t.UsedShellIntegrationInjection="usedShellIntegrationInjection",t.ShellIntegrationInjectionFailureReason="shellIntegrationInjectionFailureReason"})(i1||(i1={}));var L0=It("ptyService"),ks;(function(t){t[t.BeatInterval=5e3]="BeatInterval",t[t.ConnectingBeatInterval=2e4]="ConnectingBeatInterval",t[t.FirstWaitMultiplier=1.2]="FirstWaitMultiplier",t[t.SecondWaitMultiplier=1]="SecondWaitMultiplier",t[t.CreateProcessTimeout=5e3]="CreateProcessTimeout"})(ks||(ks={}));var s1;(function(t){t[t.Panel=1]="Panel",t[t.Editor=2]="Editor"})(s1||(s1={}));var r1;(function(t){t.TerminalView="view",t.Editor="editor"})(r1||(r1={}));var n1;(function(t){t[t.GraceTime=6e4]="GraceTime",t[t.ShortGraceTime=6e3]="ShortGraceTime"})(n1||(n1={}));var o1;(function(t){t[t.HighWatermarkChars=1e5]="HighWatermarkChars",t[t.LowWatermarkChars=5e3]="LowWatermarkChars",t[t.CharCountAckSize=5e3]="CharCountAckSize"})(o1||(o1={}));var a1;(function(t){t.GitBash="Git Bash",t.Pwsh="PowerShell"})(a1||(a1={}));var h1;(function(t){t[t.Off=0]="Off",t[t.FinalTerm=1]="FinalTerm",t[t.VSCode=2]="VSCode"})(h1||(h1={}));var c1;(function(t){t.InjectionSettingDisabled="injectionSettingDisabled",t.NoExecutable="noExecutable",t.FeatureTerminal="featureTerminal",t.IgnoreShellIntegrationFlag="ignoreShellIntegrationFlag",t.Winpty="winpty",t.UnsupportedArgs="unsupportedArgs",t.UnsupportedShell="unsupportedShell",t.FailedToSetStickyBit="failedToSetStickyBit",t.FailedToCreateTmpDir="failedToCreateTmpDir"})(c1||(c1={}));var l1;(function(t){t[t.Unknown=0]="Unknown",t[t.Shutdown=1]="Shutdown",t[t.Process=2]="Process",t[t.User=3]="User",t[t.Extension=4]="Extension"})(l1||(l1={}));var bl={Backend:"workbench.contributions.terminal.processBackend"},Cl=class{constructor(){this.a=new Map}get backends(){return this.a}registerTerminalBackend(t){const e=this.b(t.remoteAuthority);if(this.a.has(e))throw new Error(`A terminal backend with remote authority '${e}' was already registered.`);this.a.set(e,t)}getTerminalBackend(t){return this.a.get(this.b(t))}b(t){return t?.toLowerCase()??""}};yl.add(bl.Backend,new Cl);var k0=It("localPtyService"),D0=It("terminalLogService"),El=class extends V{constructor(){super(),this.a=this.B(new x),this.onBeat=this.a.event;const t=setInterval(()=>{this.a.fire()},ks.BeatInterval);this.B(wt(()=>clearInterval(t)))}};import{execFile as u1,exec as xl}from"child_process";import{userInfo as Sl}from"os";import*as f1 from"os";var d1=/^\d+$/,Pl=/^Microsoft.PowerShell_.*/,$l=/^Microsoft.PowerShellPreview_.*/,p1;(function(t){t[t.x64=0]="x64",t[t.x86=1]="x86",t[t.ARM=2]="ARM"})(p1||(p1={}));var We;switch(process.arch){case"ia32":We=1;break;case"arm":case"arm64":We=2;break;default:We=0;break}var we;process.env.PROCESSOR_ARCHITEW6432?we=process.env.PROCESSOR_ARCHITEW6432==="ARM64"?2:0:process.env.PROCESSOR_ARCHITECTURE==="ARM64"?we=2:process.env.PROCESSOR_ARCHITECTURE==="X86"?we=1:we=0;var ze=class{constructor(t,e,i){this.exePath=t,this.displayName=e,this.a=i}async exists(){return this.a===void 0&&(this.a=await Dt.existsFile(this.exePath)),this.a}};function Ll({useAlternateBitness:t=!1}={}){return t?We===0?process.env["ProgramFiles(x86)"]||null:we===0&&process.env.ProgramW6432||null:process.env.ProgramFiles||null}async function Si({useAlternateBitness:t=!1,findPreview:e=!1}={}){const i=Ll({useAlternateBitness:t});if(!i)return null;const s=O(i,"PowerShell");if(!await Dt.existsDirectory(s))return null;let r=-1,n=null;for(const c of await ds.readdir(s)){let h=-1;if(e){const l=c.indexOf("-");if(l<0)continue;const f=c.substring(0,l);if(!d1.test(f)||c.substring(l+1)!=="preview")continue;h=parseInt(f,10)}else{if(!d1.test(c))continue;h=parseInt(c,10)}if(h<=r)continue;const u=O(s,c,"pwsh.exe");await Dt.existsFile(u)&&(n=u,r=h)}if(!n)return null;const o=i.includes("x86")?" (x86)":"",a=e?" Preview":"";return new ze(n,`PowerShell${a}${o}`,!0)}async function g1({findPreview:t}={}){if(!process.env.LOCALAPPDATA)return null;const e=O(process.env.LOCALAPPDATA,"Microsoft","WindowsApps");if(!await Dt.existsDirectory(e))return null;const{pwshMsixDirRegex:i,pwshMsixName:s}=t?{pwshMsixDirRegex:$l,pwshMsixName:"PowerShell Preview (Store)"}:{pwshMsixDirRegex:Pl,pwshMsixName:"PowerShell (Store)"};for(const r of await ds.readdir(e))if(i.test(r)){const n=O(e,r,"pwsh.exe");return new ze(n,s)}return null}function kl(){const t=O(f1.homedir(),".dotnet","tools","pwsh.exe");return new ze(t,".NET Core PowerShell Global Tool")}function Dl(){const t=O(f1.homedir(),"scoop","apps"),e=O(t,"pwsh","current","pwsh.exe");return new ze(e,"PowerShell (Scoop)")}function Al(){const t=O(process.env.windir,We===1&&we!==1?"SysNative":"System32","WindowsPowerShell","v1.0","powershell.exe");return new ze(t,"Windows PowerShell",!0)}async function*Ml(){let t=await Si();t&&(yield t),t=await Si({useAlternateBitness:!0}),t&&(yield t),t=await g1(),t&&(yield t),t=kl(),t&&(yield t),t=await Si({findPreview:!0}),t&&(yield t),t=await g1({findPreview:!0}),t&&(yield t),t=await Si({useAlternateBitness:!0,findPreview:!0}),t&&(yield t),t=await Dl(),t&&(yield t),t=Al(),t&&(yield t)}async function*Il(){for await(const t of Ml())await t.exists()&&(yield t)}async function Ol(){for await(const t of Il())return t;return null}async function _l(t,e){return t===1?R?Nl():pc(e):Rl(t,e)}var Ds=null;function Rl(t,e){if(Le&&t===2||Nt&&t===3)return"/bin/bash";if(!Ds){let i;if(R)i="/bin/bash";else{if(i=e.SHELL,!i)try{i=Sl().shell}catch{}i||(i="sh"),i==="/bin/false"&&(i="/bin/bash")}Ds=i}return Ds}var As=null;async function Nl(){return As||(As=(await Ol()).exePath),As}var Ms=class extends V{constructor(e,i){super(),this.h=i,this.a=0,this.c=new Map,this.f=new Map,this.g=this.B(new x),this.onCreateRequest=this.g.event,this.b=e===void 0?15e3:e,this.B(wt(()=>{for(const s of this.f.values())Rt(s)}))}createRequest(e){return new Promise((i,s)=>{const r=++this.a;this.c.set(r,i),this.g.fire({requestId:r,...e});const n=new is;Ft(this.b,n.token).then(()=>s(`Request ${r} timed out (${this.b}ms)`)),this.f.set(r,[wt(()=>n.cancel())])})}acceptReply(e,i){const s=this.c.get(e);s?(this.c.delete(e),Rt(this.f.get(e)||[]),this.f.delete(e),s(i)):this.h.warn(`RequestStore#acceptReply was called without receiving a matching request ${e}`)}};Ms=__decorate([__param(1,Jt)],Ms);var Tl=class{constructor(t){this.b=t,this.a=new Map}dispose(){for(const t of this.a.values())t.dispose()}startBuffering(t,e,i=5){const s=e(r=>{const n=typeof r=="string"?r:r.data;let o=this.a.get(t);if(o){o.data.push(n);return}const a=setTimeout(()=>this.flushBuffer(t),i);o={data:[n],timeoutId:a,dispose:()=>{clearTimeout(a),this.flushBuffer(t),s.dispose()}},this.a.set(t,o)});return s}stopBuffering(t){this.a.get(t)?.dispose()}flushBuffer(t){const e=this.a.get(t);e&&(this.a.delete(t),this.b(t,e.data.join("")))}};function Bl(t,e){let i=t;i.includes("\\")&&(i=i.replace(/\\/g,"\\\\"));let s;switch(e){case"bash":case"sh":case"zsh":case"gitbash":s={bothQuotes:n=>`$'${n.replace(/'/g,"\\'")}'`,singleQuotes:n=>`'${n.replace(/'/g,"\\'")}'`,noSingleQuotes:n=>`'${n}'`};break;case"fish":s={bothQuotes:n=>`"${n.replace(/"/g,'\\"')}"`,singleQuotes:n=>`'${n.replace(/'/g,"\\'")}'`,noSingleQuotes:n=>`'${n}'`};break;case"pwsh":s={bothQuotes:n=>`"${n.replace(/"/g,'`"')}"`,singleQuotes:n=>`'${n.replace(/'/g,"''")}'`,noSingleQuotes:n=>`'${n}'`};break;default:s={bothQuotes:n=>`$'${n.replace(/'/g,"\\'")}'`,singleQuotes:n=>`'${n.replace(/'/g,"\\'")}'`,noSingleQuotes:n=>`'${n}'`};break}const r=/[\`\$\|\&\>\~\#\!\^\*\;\<]/g;return i=i.replace(r,""),i.includes("'")&&i.includes('"')?s.bothQuotes(i):i.includes("'")?s.singleQuotes(i):s.noSingleQuotes(i)}function Fl(t){return t.match(/^['"].*['"]$/)&&(t=t.substring(1,t.length-1)),pr===1&&t&&t[1]===":"?t[0].toUpperCase()+t.substring(1):t}import*as Pi from"os";var Et;(function(t){t[t.Replace=1]="Replace",t[t.Append=2]="Append",t[t.Prepend=3]="Prepend"})(Et||(Et={}));function Ul(t){return new Map(t)}function jl(t){return new Map(t??[])}function Wl(t){return new Map(t.map(e=>[e[0],{map:Ul(e[1]),descriptionMap:jl(e[2])}]))}var zl=new Map([[Et.Append,"APPEND"],[Et.Prepend,"PREPEND"],[Et.Replace,"REPLACE"]]),Hl=class{constructor(t){this.collections=t,this.a=new Map,this.b=new Map,t.forEach((e,i)=>{this.d(e,i);const s=e.map.entries();let r=s.next();for(;!r.done;){const n=r.value[1],o=r.value[0];let a=this.a.get(o);if(a||(a=[],this.a.set(o,a)),a.length>0&&a[0].type===Et.Replace){r=s.next();continue}const c={extensionIdentifier:i,value:n.value,type:n.type,scope:n.scope,variable:n.variable,options:n.options};c.scope||delete c.scope,a.unshift(c),r=s.next()}})}async applyToProcessEnvironment(t,e,i){let s;R&&(s={},Object.keys(t).forEach(r=>s[r.toLowerCase()]=r));for(const[r,n]of this.getVariableMap(e)){const o=R&&s[r.toLowerCase()]||r;for(const a of n){const c=i?await i(a.value):a.value;if(a.options?.applyAtProcessCreation??!0)switch(a.type){case Et.Append:t[o]=(t[o]||"")+c;break;case Et.Prepend:t[o]=c+(t[o]||"");break;case Et.Replace:t[o]=c;break}if(a.options?.applyAtShellIntegration??!1){const h=`VSCODE_ENV_${zl.get(a.type)}`;t[h]=(t[h]?t[h]+":":"")+r+"="+this.c(c)}}}}c(t){return t.replaceAll(":","\\x3a")}diff(t,e){const i=new Map,s=new Map,r=new Map;if(t.getVariableMap(e).forEach((n,o)=>{const a=this.getVariableMap(e).get(o),c=v1(n,a);c&&i.set(o,c)}),this.getVariableMap(e).forEach((n,o)=>{const a=t.getVariableMap(e).get(o),c=v1(n,a);c&&r.set(o,c)}),this.getVariableMap(e).forEach((n,o)=>{const a=t.getVariableMap(e).get(o),c=ql(n,a);c&&s.set(o,c)}),!(i.size===0&&s.size===0&&r.size===0))return{added:i,changed:s,removed:r}}getVariableMap(t){const e=new Map;for(const i of this.a.values()){const s=i.filter(r=>m1(r,t));s.length>0&&e.set(s[0].variable,s)}return e}getDescriptionMap(t){const e=new Map;for(const i of this.b.values()){const s=i.filter(r=>m1(r,t,!0));for(const r of s)e.set(r.extensionIdentifier,r.description)}return e}d(t,e){if(!t.descriptionMap)return;const i=t.descriptionMap.entries();let s=i.next();for(;!s.done;){const r=s.value[1],n=s.value[0];let o=this.b.get(n);o||(o=[],this.b.set(n,o));const a={extensionIdentifier:e,scope:r.scope,description:r.description};a.scope||delete a.scope,o.push(a),s=i.next()}}};function m1(t,e,i=!1){return t.scope?!!(t.scope.workspaceFolder&&e?.workspaceFolder&&t.scope.workspaceFolder.index===e.workspaceFolder.index):i?e===t.scope:!0}function v1(t,e){if(!e)return t;const i=new Set;e.forEach(r=>i.add(r.extensionIdentifier));const s=[];return t.forEach(r=>{i.has(r.extensionIdentifier)||s.push(r)}),s.length===0?void 0:s}function ql(t,e){if(!e)return;const i=new Map;e.forEach(r=>i.set(r.extensionIdentifier,r));const s=[];return t.forEach(r=>{const n=i.get(r.extensionIdentifier);n&&(r.type!==n.type||r.value!==n.value||r.scope?.workspaceFolder?.index!==n.scope?.workspaceFolder?.index)&&s.push(n)}),s.length===0?void 0:s}import{chmod as w1,realpathSync as Vl,mkdirSync as Xl}from"fs";import{promisify as y1}from"util";function te(){const t=/(\d+)\.(\d+)\.(\d+)/g.exec(Pi.release());let e=0;return t&&t.length===4&&(e=parseInt(t[3])),e}async function Gl(t,e,i,s,r,n=!1){if(!e.shellIntegration.enabled)return{type:"failure",reason:"injectionSettingDisabled"};if(!t.executable)return{type:"failure",reason:"noExecutable"};if(t.isFeatureTerminal&&!t.forceShellIntegration)return{type:"failure",reason:"featureTerminal"};if(t.ignoreShellIntegration)return{type:"failure",reason:"ignoreShellIntegrationFlag"};if(R&&(!e.windowsEnableConpty||te()<18309))return{type:"failure",reason:"winpty"};const o=t.args,a=mr==="win32"?ui(t.executable).toLowerCase():ui(t.executable),c=De(Oe.asFileUri("").fsPath),h="injection";let u;const l={VSCODE_INJECTION:"1"};e.shellIntegration.nonce&&(l.VSCODE_NONCE=e.shellIntegration.nonce);const f=["PATH","VIRTUAL_ENV","HOME","SHELL","PWD"];if(t.shellIntegrationEnvironmentReporting&&(R?(e.windowsUseConptyDll||e.windowsEnableConpty&&te()>=22631&&a!=="bash.exe")&&(l.VSCODE_SHELL_ENV_REPORTING=f.join(",")):l.VSCODE_SHELL_ENV_REPORTING=f.join(",")),R)return a==="pwsh.exe"||a==="powershell.exe"?(!o||E1(o)?u=Q.get(G.WindowsPwsh):C1(o)&&(u=Q.get(G.WindowsPwshLogin)),u?(u=[...u],u[u.length-1]=le(u[u.length-1],c,""),l.VSCODE_STABLE=r.quality==="stable"?"1":"0",e.shellIntegration.suggestEnabled&&(l.VSCODE_SUGGEST="1"),{type:h,newArgs:u,envMixin:l}):{type:"failure",reason:"unsupportedArgs"}):a==="bash.exe"?(!o||o.length===0?u=Q.get(G.Bash):Di(o)&&(l.VSCODE_SHELL_LOGIN="1",$i(e,l,a),u=Q.get(G.Bash)),u?(u=[...u],u[u.length-1]=le(u[u.length-1],c),l.VSCODE_STABLE=r.quality==="stable"?"1":"0",{type:h,newArgs:u,envMixin:l}):{type:"failure",reason:"unsupportedArgs"}):(s.warn(`Shell integration cannot be enabled for executable "${t.executable}" and args`,t.args),{type:"failure",reason:"unsupportedShell"});switch(a){case"bash":return!o||o.length===0?u=Q.get(G.Bash):Di(o)&&(l.VSCODE_SHELL_LOGIN="1",$i(e,l,a),u=Q.get(G.Bash)),u?(u=[...u],u[u.length-1]=le(u[u.length-1],c),l.VSCODE_STABLE=r.quality==="stable"?"1":"0",{type:h,newArgs:u,envMixin:l}):{type:"failure",reason:"unsupportedArgs"};case"fish":return!o||o.length===0?u=Q.get(G.Fish):Di(o)?u=Q.get(G.FishLogin):(o===Q.get(G.Fish)||o===Q.get(G.FishLogin))&&(u=o),u?($i(e,l,a),u=[...u],u[u.length-1]=le(u[u.length-1],c),{type:h,newArgs:u,envMixin:l}):{type:"failure",reason:"unsupportedArgs"};case"pwsh":return!o||E1(o)?u=Q.get(G.Pwsh):C1(o)&&(u=Q.get(G.PwshLogin)),u?(e.shellIntegration.suggestEnabled&&(l.VSCODE_SUGGEST="1"),u=[...u],u[u.length-1]=le(u[u.length-1],c,""),l.VSCODE_STABLE=r.quality==="stable"?"1":"0",{type:h,newArgs:u,envMixin:l}):{type:"failure",reason:"unsupportedArgs"};case"zsh":{if(!o||o.length===0?u=Q.get(G.Zsh):Di(o)?(u=Q.get(G.ZshLogin),$i(e,l,a)):(o===Q.get(G.Zsh)||o===Q.get(G.ZshLogin))&&(u=o),!u)return{type:"failure",reason:"unsupportedArgs"};u=[...u],u[u.length-1]=le(u[u.length-1],c);let d;try{d=Pi.userInfo().username}catch{d="unknown"}const p=Vl(Pi.tmpdir()),g=O(p,`${d}-${r.applicationName}-zsh`);if(!n)try{await y1(w1)(g,960)}catch(D){if(D.message.includes("ENOENT")){try{Xl(g)}catch(T){return s.error(`Failed to create zdotdir at ${g}: ${T}`),{type:"failure",reason:"failedToCreateTmpDir"}}try{await y1(w1)(g,960)}catch{return s.error(`Failed to set sticky bit on ${g}: ${D}`),{type:"failure",reason:"failedToSetStickyBit"}}}return s.error(`Failed to set sticky bit on ${g}: ${D}`),{type:"failure",reason:"failedToSetStickyBit"}}l.ZDOTDIR=g;const w=i?.ZDOTDIR??Pi.homedir()??"~";l.USER_ZDOTDIR=w;const v=[];return v.push({source:O(c,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"),dest:O(g,".zshrc")}),v.push({source:O(c,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"),dest:O(g,".zprofile")}),v.push({source:O(c,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"),dest:O(g,".zshenv")}),v.push({source:O(c,"out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"),dest:O(g,".zlogin")}),{type:h,newArgs:u,envMixin:l,filesToCopy:v}}}return s.warn(`Shell integration cannot be enabled for executable "${t.executable}" and args`,t.args),{type:"failure",reason:"unsupportedShell"}}function $i(t,e,i){if((Nt||i==="fish")&&t.environmentVariableCollections){const s=Wl(t.environmentVariableCollections),n=new Hl(s).getVariableMap({workspaceFolder:t.workspaceFolder}).get("PATH"),o=[];if(n)for(const a of n)a.type===Et.Prepend&&o.push(a.value);o.length>0&&(e.VSCODE_PATH_PREFIX=o.join(""))}}var G;(function(t){t.WindowsPwsh="windows-pwsh",t.WindowsPwshLogin="windows-pwsh-login",t.Pwsh="pwsh",t.PwshLogin="pwsh-login",t.Zsh="zsh",t.ZshLogin="zsh-login",t.Bash="bash",t.Fish="fish",t.FishLogin="fish-login"})(G||(G={}));var Q=new Map;Q.set(G.WindowsPwsh,["-noexit","-command",'try { . "{0}\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1" } catch {}{1}']),Q.set(G.WindowsPwshLogin,["-l","-noexit","-command",'try { . "{0}\\out\\vs\\workbench\\contrib\\terminal\\common\\scripts\\shellIntegration.ps1" } catch {}{1}']),Q.set(G.Pwsh,["-noexit","-command",'. "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"{1}']),Q.set(G.PwshLogin,["-l","-noexit","-command",'. "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"']),Q.set(G.Zsh,["-i"]),Q.set(G.ZshLogin,["-il"]),Q.set(G.Bash,["--init-file","{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"]),Q.set(G.Fish,["--init-command",'source "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"']),Q.set(G.FishLogin,["-l","--init-command",'source "{0}/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"']);var Li=["-login","-l"],b1=["--login","-l"],Ql=["-i","--interactive"],ki=["-nol","-nologo"];function C1(t){return typeof t=="string"?Li.includes(t.toLowerCase()):t.length===1&&Li.includes(t[0].toLowerCase())||t.length===2&&(Li.includes(t[0].toLowerCase())||Li.includes(t[1].toLowerCase()))&&(ki.includes(t[0].toLowerCase())||ki.includes(t[1].toLowerCase()))}function E1(t){return typeof t=="string"?ki.includes(t.toLowerCase()):t.length===0||t?.length===1&&ki.includes(t[0].toLowerCase())}function Di(t){return typeof t!="string"&&(t=t.filter(e=>!Ql.includes(e.toLowerCase()))),t==="string"&&b1.includes(t.toLowerCase())||typeof t!="string"&&t.length===1&&b1.includes(t[0].toLowerCase())}import*as He from"fs";import{exec as Yl}from"child_process";var Jl=It("productService");import{exec as Ai}from"child_process";import{totalmem as Kl}from"os";function Zl(t){return new Promise((e,i)=>{let s;const r=new Map,n=Kl();function o(h,u,l,f,d){const p=r.get(u);if(h===t||p){const g={name:a(l),cmd:l,pid:h,ppid:u,load:f,mem:R?d:n*(d/100)};r.set(h,g),h===t&&(s=g),p&&(p.children||(p.children=[]),p.children.push(g),p.children.length>1&&(p.children=p.children.sort((w,v)=>w.pid-v.pid)))}}function a(h){const u=/--utility-sub-type=network/i,l=/--crashes-directory/i,f=/\\pipe\\winpty-control/i,d=/conhost\.exe.+--headless/i,p=/--type=([a-zA-Z-]+)/;if(l.exec(h))return"electron-crash-reporter";if(f.exec(h))return"winpty-agent";if(d.exec(h))return"conpty-agent";let g=p.exec(h);if(g&&g.length===2)return g[1]==="renderer"?"window":g[1]==="utility"?u.exec(h)?"utility-network-service":"utility-process":g[1]==="extensionHost"?"extension-host":g[1];const w=/[a-zA-Z-]+\.js/g;let v="";do g=w.exec(h),g&&(v+=g+" ");while(g);return v&&h.indexOf("node ")<0&&h.indexOf("node.exe")<0?`electron-nodejs (${v})`:h}if(process.platform==="win32"){const h=u=>u.indexOf("\\\\?\\")===0||u.indexOf("\\??\\")===0?u.substring(4):u.indexOf('"\\\\?\\')===0||u.indexOf('"\\??\\')===0?'"'+u.substring(5):u;import("@vscode/windows-process-tree").then(u=>{u.getProcessList(t,l=>{if(!l){i(new Error(`Root process ${t} not found`));return}u.getProcessCpuUsage(l,f=>{const d=new Map;f.forEach(p=>{const g=h(p.commandLine||"");d.set(p.pid,{name:a(g),cmd:g,pid:p.pid,ppid:p.ppid,load:p.cpu||0,mem:p.memory||0})}),s=d.get(t),s?(d.forEach(p=>{const g=d.get(p.ppid);g&&(g.children||(g.children=[]),g.children.push(p))}),d.forEach(p=>{p.children&&(p.children=p.children.sort((g,w)=>g.pid-w.pid))}),e(s)):i(new Error(`Root process ${t} not found`))})},u.ProcessDataFlag.CommandLine|u.ProcessDataFlag.Memory)})}else{let h=function(){let u=[s];const l=[];for(;u.length;){const d=u.shift();d&&(l.push(d.pid),d.children&&(u=u.concat(d.children)))}let f=JSON.stringify(Oe.asFileUri("vs/base/node/cpuUsage.sh").fsPath);f+=" "+l.join(" "),Ai(f,{},(d,p,g)=>{if(d||g)i(d||new Error(g.toString()));else{const w=p.toString().split(`
`);for(let v=0;v<l.length;v++){const D=r.get(l[v]);D.load=parseFloat(w[v])}if(!s){i(new Error(`Root process ${t} not found`));return}e(s)}})};var c=h;Ai("which ps",{},(u,l,f)=>{if(u||f)if(process.platform!=="linux")i(u||new Error(f.toString()));else{const d=JSON.stringify(Oe.asFileUri("vs/base/node/ps.sh").fsPath);Ai(d,{},(p,g,w)=>{p||w?i(p||new Error(w.toString())):(x1(g,o),h())})}else{const d=l.toString().trim();Ai(`${d} -ax -o pid=,ppid=,pcpu=,pmem=,command=`,{maxBuffer:1e3*1024,env:{LC_NUMERIC:"en_US.UTF-8"}},(g,w,v)=>{g||v&&!v.includes("screen size is bogus")?i(g||new Error(v.toString())):(x1(w,o),process.platform==="linux"?h():s?e(s):i(new Error(`Root process ${t} not found`)))})}})}})}function x1(t,e){const i=/^\s*([0-9]+)\s+([0-9]+)\s+([0-9]+\.[0-9]+)\s+([0-9]+\.[0-9]+)\s+(.+)$/,s=t.toString().split(`
`);for(const r of s){const n=i.exec(r.trim());n&&n.length===6&&e(parseInt(n[1]),parseInt(n[2]),n[5],parseFloat(n[3]),parseFloat(n[4]))}}var S1;(function(t){t[t.InactiveThrottleDuration=5e3]="InactiveThrottleDuration",t[t.ActiveDebounceDuration=1e3]="ActiveDebounceDuration"})(S1||(S1={}));var Is=[],qe=class extends V{set hasChildProcesses(e){this.a!==e&&(this.a=e,this.f.debug("ChildProcessMonitor: Has child processes changed",e),this.b.fire(e))}get hasChildProcesses(){return this.a}constructor(e,i){super(),this.c=e,this.f=i,this.a=!1,this.b=this.B(new x),this.onDidChangeHasChildProcesses=this.b.event}handleInput(){this.g()}handleOutput(){this.h()}async g(){if(!this.q.isDisposed)try{const e=await Zl(this.c);this.hasChildProcesses=this.j(e)}catch(e){this.f.debug("ChildProcessMonitor: Fetching process tree failed",e)}}h(){this.g()}j(e){if(!e.children)return!1;if(e.children.length===1){const i=e.children[0];let s;if(i.cmd.startsWith('"'))s=i.cmd.substring(1,i.cmd.indexOf('"',1));else{const r=i.cmd.indexOf(" ");r===-1?s=i.cmd:s=i.cmd.substring(0,r)}return Is.indexOf(ja(s).name)===-1}return e.children.length>0}};__decorate([ls(1e3)],qe.prototype,"g",null),__decorate([Gr(5e3)],qe.prototype,"h",null),qe=__decorate([__param(1,Jt)],qe);var tu=["cmd.exe","powershell.exe","pwsh.exe","bash.exe","git-cmd.exe","wsl.exe","ubuntu.exe","ubuntu1804.exe","kali.exe","debian.exe","opensuse-42.exe","sles-12.exe","julia.exe","nu.exe","node.exe"],eu=[/^python(\d(\.\d{0,2})?)?\.exe$/],Os,P1=class extends V{get shellType(){return this.b}get shellTitle(){return this.c}get onShellNameChanged(){return this.f.event}get onShellTypeChanged(){return this.g.event}constructor(t){if(super(),this.h=t,this.c="",this.f=new x,this.g=new x,!R)throw new Error(`WindowsShellHelper cannot be instantiated on ${$a}`);this.j()}async j(){this.q.isDisposed||this.checkShell()}async checkShell(){R&&(await Ft(300),this.getShellName().then(t=>{const e=this.getShellType(t);e!==this.b&&(this.g.fire(e),this.f.fire(t),this.b=e,this.c=t)}))}m(t){if(!t)return"";if(tu.indexOf(t.name)===-1)return t.name;for(const i of eu)if(t.name.match(i))return t.name;if(!t.children||t.children.length===0)return t.name;let e=0;for(;e<t.children.length;e++){const i=t.children[e];if(!i.children||i.children.length===0||i.children[0].name!=="conhost.exe")break}return e>=t.children.length?t.name:this.m(t.children[e])}async getShellName(){return this.q.isDisposed?Promise.resolve(""):this.a?this.a:(Os||(Os=await import("@vscode/windows-process-tree")),this.a=new Promise(t=>{Os.getProcessTree(this.h,e=>{const i=this.m(e);this.a=void 0,t(i)})}),this.a)}getShellType(t){switch(t.toLowerCase()){case"cmd.exe":return"cmd";case"powershell.exe":case"pwsh.exe":return"pwsh";case"bash.exe":case"git-cmd.exe":return"gitbash";case"julia.exe":return"julia";case"node.exe":return"node";case"nu.exe":return"nu";case"wsl.exe":case"ubuntu.exe":case"ubuntu1804.exe":case"kali.exe":case"debian.exe":case"opensuse-42.exe":case"sles-12.exe":return"wsl";default:return t.match(/python(\d(\.\d{0,2})?)?\.exe/)?"python":void 0}}};__decorate([ls(500)],P1.prototype,"checkShell",null);import{spawn as iu}from"node-pty";var $1;(function(t){t[t.WriteMaxChunkSize=50]="WriteMaxChunkSize"})($1||($1={}));function su(t){const e=[];let i=0;for(let s=0;s<t.length-1;s++)(s-i+1>=50||t[s+1]==="\x1B")&&(e.push(t.substring(i,s+1)),i=s+1,s++);return i!==t.length&&e.push(t.substring(i)),e}var Ve,L1;(function(t){t[t.DataFlushTimeout=250]="DataFlushTimeout",t[t.MaximumShutdownTime=5e3]="MaximumShutdownTime"})(L1||(L1={}));var k1;(function(t){t[t.KillSpawnThrottleInterval=250]="KillSpawnThrottleInterval",t[t.KillSpawnSpacingDuration=50]="KillSpawnSpacingDuration",t[t.WriteInterval=5]="WriteInterval"})(k1||(k1={}));var D1=new Map([["bash","bash"],["csh","csh"],["fish","fish"],["ksh","ksh"],["sh","sh"],["zsh","zsh"]]),A1=new Map([["pwsh","pwsh"],["powershell","pwsh"],["python","python"],["julia","julia"],["nu","nu"],["node","node"]]),_s=class extends V{static{Ve=this}static{this.b=0}get exitMessage(){return this.h}get currentTitle(){return this.t?.shellTitle||this.n}get shellType(){return R?this.t?.shellType:D1.get(this.n)||A1.get(this.n)}get hasChildProcesses(){return this.u?.hasChildProcesses||!1}constructor(e,i,s,r,n,o,a,c,h){super(),this.shellLaunchConfig=e,this.N=o,this.O=a,this.P=c,this.Q=h,this.id=0,this.shouldPersist=!1,this.a={cwd:"",initialCwd:"",fixedDimensions:{cols:void 0,rows:void 0},title:"",shellType:void 0,hasChildProcesses:!0,resolvedShellLaunchConfig:{},overrideDimensions:void 0,failedShellIntegrationActivation:!1,usedShellIntegrationInjection:void 0,shellIntegrationInjectionFailureReason:void 0},this.n="",this.y=[],this.G=!1,this.H=0,this.I=this.B(new x),this.onProcessData=this.I.event,this.J=this.B(new x),this.onProcessReady=this.J.event,this.L=this.B(new x),this.onDidChangeProperty=this.L.event,this.M=this.B(new x),this.onProcessExit=this.M.event;let u;R?u=ui(this.shellLaunchConfig.executable||""):u="xterm-256color",this.D=i,this.a.initialCwd=this.D,this.a.cwd=this.D;const l=this.O.windowsEnableConpty&&process.platform==="win32"&&te()>=18309,f=l&&this.O.windowsUseConptyDll;this.F={name:u,cwd:i,env:n,cols:s,rows:r,useConpty:l,useConptyDll:f,conptyInheritCursor:l&&!!e.initialText},R&&(l&&s===0&&r===0&&this.shellLaunchConfig.executable?.endsWith("Git\\bin\\bash.exe")&&(this.C=new ru,this.B(this.C.onTrigger(d=>{this.C?.dispose(),this.C=void 0,d.cols&&d.rows&&this.resize(d.cols,d.rows)}))),this.onProcessReady(d=>{this.t=this.B(new P1(d.pid)),this.B(this.t.onShellTypeChanged(p=>this.L.fire({type:"shellType",value:p}))),this.B(this.t.onShellNameChanged(p=>this.L.fire({type:"title",value:p})))})),this.B(wt(()=>{this.w&&(clearInterval(this.w),this.w=void 0)}))}async start(){const i=(await Promise.all([this.R(),this.S()])).find(r=>r!==void 0);if(i)return i;const s=await Gl(this.shellLaunchConfig,this.O,this.F.env,this.P,this.Q);if(s.type==="injection"){if(this.L.fire({type:"usedShellIntegrationInjection",value:!0}),s.envMixin)for(const[r,n]of Object.entries(s.envMixin))this.F.env||={},this.F.env[r]=n;if(s.filesToCopy)for(const r of s.filesToCopy)try{await He.promises.mkdir(De(r.dest),{recursive:!0}),await He.promises.copyFile(r.source,r.dest)}catch{}}else this.L.fire({type:"failedShellIntegrationActivation",value:!0}),this.L.fire({type:"shellIntegrationInjectionFailureReason",value:s.reason});try{const r=s.type==="injection"?s:void 0;return await this.U(this.shellLaunchConfig,this.F,r),r?.newArgs?{injectedArgs:r.newArgs}:void 0}catch(r){return this.P.trace("node-pty.node-pty.IPty#spawn native exception",r),{message:`A native exception occurred during launch (${r.message})`}}}async R(){try{if(!(await He.promises.stat(this.D)).isDirectory())return{message:y(2329,null,this.D.toString())}}catch(e){if(e?.code==="ENOENT")return{message:y(2330,null,this.D.toString())}}this.L.fire({type:"initialCwd",value:this.D})}async S(){const e=this.shellLaunchConfig;if(!e.executable)throw new Error("IShellLaunchConfig.executable not set");const i=e.cwd instanceof j?e.cwd.path:e.cwd,s=e.env&&e.env.PATH?e.env.PATH.split(br):void 0,r=await mc(e.executable,i,s,this.N);if(!r)return{message:y(2331,null,e.executable)};try{const n=await He.promises.stat(r);if(!n.isFile()&&!n.isSymbolicLink())return{message:y(2332,null,e.executable)};e.executable=r}catch(n){if(n?.code!=="EACCES")throw n}}async U(e,i,s){const r=s?.newArgs||e.args||[];await this.Z(),this.P.trace("node-pty.IPty#spawn",e.executable,r,i);const n=iu(e.executable,r,i);this.m=n,this.u=this.B(new qe(n.pid,this.P)),this.u.onDidChangeHasChildProcesses(o=>this.L.fire({type:"hasChildProcesses",value:o})),this.s=new Promise(o=>{this.onProcessReady(()=>o())}),n.onData(o=>{this.H+=o.length,!this.G&&this.H>1e5&&(this.P.trace(`Flow control: Pause (${this.H} > 100000)`),this.G=!0,n.pause()),this.P.trace("node-pty.IPty#onData",o),this.I.fire(o),this.j&&this.X(),this.t?.checkShell(),this.u?.handleOutput()}),n.onExit(o=>{this.g=o.exitCode,this.X()}),this.$(n.pid),this.W(n)}W(e){setTimeout(()=>this.ab(e)),R||(this.w=setInterval(()=>{this.n!==e.process&&this.ab(e)},200))}X(){this.P.getLevel()===k.Trace&&this.P.trace("TerminalProcess#_queueProcessExit",new Error().stack?.replace(/^Error/,"")),this.j&&clearTimeout(this.j),this.j=setTimeout(()=>{this.j=void 0,this.Y()},250)}async Y(){if(await this.s,!this.q.isDisposed){try{this.m&&(await this.Z(),this.P.trace("node-pty.IPty#kill"),this.m.kill())}catch{}this.M.fire(this.g||0),this.dispose()}}async Z(){if(!(!R||!("useConpty"in this.F)||!this.F.useConpty)&&!this.F.useConptyDll){for(;Date.now()-Ve.b<250;)this.P.trace("Throttling kill/spawn call"),await Ft(250-(Date.now()-Ve.b)+50);Ve.b=Date.now()}}$(e){this.J.fire({pid:e,cwd:this.D,windowsPty:this.getWindowsPty()})}ab(e){if(this.q.isDisposed)return;this.n=e.process??"",this.L.fire({type:"title",value:this.n});let i=this.currentTitle.replace(/ \(figterm\)$/g,"");if(R||(i=ui(i)),i.toLowerCase().startsWith("python"))this.L.fire({type:"shellType",value:"python"});else if(i.toLowerCase().startsWith("julia"))this.L.fire({type:"shellType",value:"julia"});else{const s=D1.get(i)||A1.get(i);this.L.fire({type:"shellType",value:s})}}shutdown(e){this.P.getLevel()===k.Trace&&this.P.trace("TerminalProcess#shutdown",new Error().stack?.replace(/^Error/,"")),e&&!R?this.Y():!this.j&&!this.q.isDisposed&&(this.X(),setTimeout(()=>{this.j&&!this.q.isDisposed&&(this.j=void 0,this.Y())},5e3))}input(e,i=!1){this.q.isDisposed||!this.m||(this.y.push(...su(e).map(s=>({isBinary:i,data:s}))),this.bb())}sendSignal(e){this.q.isDisposed||!this.m||this.m.kill(e)}async processBinary(e){this.input(e,!0)}async refreshProperty(e){switch(e){case"cwd":{const i=await this.getCwd();return i!==this.a.cwd&&(this.a.cwd=i,this.L.fire({type:"cwd",value:this.a.cwd})),i}case"initialCwd":{const i=await this.getInitialCwd();return i!==this.a.initialCwd&&(this.a.initialCwd=i,this.L.fire({type:"initialCwd",value:this.a.initialCwd})),i}case"title":return this.currentTitle;default:return this.shellType}}async updateProperty(e,i){e==="fixedDimensions"&&(this.a.fixedDimensions=i)}bb(){if(!(this.z!==void 0||this.y.length===0)){if(this.cb(),this.y.length===0){this.z=void 0;return}this.z=setTimeout(()=>{this.z=void 0,this.bb()},5)}}cb(){const e=this.y.shift();this.P.trace("node-pty.IPty#write",e.data),e.isBinary?this.m.write(Buffer.from(e.data,"binary")):this.m.write(e.data),this.u?.handleInput()}resize(e,i){if(!this.q.isDisposed&&!(typeof e!="number"||typeof i!="number"||isNaN(e)||isNaN(i))&&this.m){if(e=Math.max(e,1),i=Math.max(i,1),this.C){this.C.cols=e,this.C.rows=i;return}this.P.trace("node-pty.IPty#resize",e,i);try{this.m.resize(e,i)}catch(s){if(this.P.trace("node-pty.IPty#resize exception "+s.message),this.g!==void 0&&s.message!=="ioctl(2) failed, EBADF"&&s.message!=="Cannot resize a pty that has already exited")throw s}}}clearBuffer(){this.m?.clear()}acknowledgeDataEvent(e){this.H=Math.max(this.H-e,0),this.P.trace(`Flow control: Ack ${e} chars (unacknowledged: ${this.H})`),this.G&&this.H<5e3&&(this.P.trace(`Flow control: Resume (${this.H} < 5000)`),this.m?.resume(),this.G=!1)}clearUnacknowledgedChars(){this.H=0,this.P.trace("Flow control: Cleared all unacknowledged chars, forcing resume"),this.G&&(this.m?.resume(),this.G=!1)}async setUnicodeVersion(e){}getInitialCwd(){return Promise.resolve(this.D)}async getCwd(){if(Nt)return new Promise(e=>{if(!this.m){e(this.D);return}this.P.trace("node-pty.IPty#pid"),Yl("lsof -OPln -p "+this.m.pid+" | grep cwd",{env:{...process.env,LANG:"en_US.UTF-8"}},(i,s,r)=>{!i&&s!==""?e(s.substring(s.indexOf("/"),s.length-1)):(this.P.error("lsof did not run successfully, it may not be on the $PATH?",i,s,r),e(this.D))})});if(Le){if(!this.m)return this.D;this.P.trace("node-pty.IPty#pid");try{return await He.promises.readlink(`/proc/${this.m.pid}/cwd`)}catch{return this.D}}return this.D}getWindowsPty(){return R?{backend:"useConpty"in this.F&&this.F.useConpty?"conpty":"winpty",buildNumber:te()}:void 0}};_s=Ve=__decorate([__param(7,Jt),__param(8,Jl)],_s);var ru=class extends V{get onTrigger(){return this.b.event}constructor(){super(),this.b=this.B(new x),this.a=setTimeout(()=>{this.b.fire({rows:this.rows,cols:this.cols})},1e3),this.B(wt(()=>clearTimeout(this.a)))}},nu=class extends V{constructor(){super(...arguments),this.a=new Map,this.b=this.B(new x),this.onDidRemoveCapabilityType=this.b.event,this.f=this.B(new x),this.onDidAddCapabilityType=this.f.event,this.g=this.B(new x),this.onDidRemoveCapability=this.g.event,this.h=this.B(new x),this.onDidAddCapability=this.h.event}get items(){return this.a.keys()}add(t,e){this.a.set(t,e),this.f.fire(t),this.h.fire({id:t,capability:e})}get(t){return this.a.get(t)}remove(t){const e=this.a.get(t);e&&(this.a.delete(t),this.b.fire(t),this.h.fire({id:t,capability:e}))}has(t){return this.a.has(t)}},M1=class vo{get command(){return this.b.command}get commandLineConfidence(){return this.b.commandLineConfidence}get isTrusted(){return this.b.isTrusted}get timestamp(){return this.b.timestamp}get duration(){return this.b.duration}get promptStartMarker(){return this.b.promptStartMarker}get marker(){return this.b.marker}get endMarker(){return this.b.endMarker}set endMarker(e){this.b.endMarker=e}get executedMarker(){return this.b.executedMarker}get aliases(){return this.b.aliases}get wasReplayed(){return this.b.wasReplayed}get cwd(){return this.b.cwd}get exitCode(){return this.b.exitCode}get commandStartLineContent(){return this.b.commandStartLineContent}get markProperties(){return this.b.markProperties}get executedX(){return this.b.executedX}get startX(){return this.b.startX}constructor(e,i){this.a=e,this.b=i}static deserialize(e,i,s){const r=e.buffer.normal,n=i.startLine!==void 0?e.registerMarker(i.startLine-(r.baseY+r.cursorY)):void 0;if(!n)return;const o=i.promptStartLine!==void 0?e.registerMarker(i.promptStartLine-(r.baseY+r.cursorY)):void 0,a=i.endLine!==void 0?e.registerMarker(i.endLine-(r.baseY+r.cursorY)):void 0,c=i.executedLine!==void 0?e.registerMarker(i.executedLine-(r.baseY+r.cursorY)):void 0;return new vo(e,{command:s?"":i.command,commandLineConfidence:i.commandLineConfidence??"low",isTrusted:i.isTrusted,promptStartMarker:o,marker:n,startX:i.startX,endMarker:a,executedMarker:c,executedX:i.executedX,timestamp:i.timestamp,duration:i.duration,cwd:i.cwd,commandStartLineContent:i.commandStartLineContent,exitCode:i.exitCode,markProperties:i.markProperties,aliases:void 0,wasReplayed:!0})}serialize(e){return{promptStartLine:this.promptStartMarker?.line,startLine:this.marker?.line,startX:void 0,endLine:this.endMarker?.line,executedLine:this.executedMarker?.line,executedX:this.executedX,command:e?"":this.command,commandLineConfidence:e?"low":this.commandLineConfidence,isTrusted:this.isTrusted,cwd:this.cwd,exitCode:this.exitCode,commandStartLineContent:this.commandStartLineContent,timestamp:this.timestamp,duration:this.duration,markProperties:this.markProperties}}extractCommandLine(){return O1(this.a.buffer.active,this.a.cols,this.marker,this.startX,this.executedMarker,this.executedX)}getOutput(){if(!this.executedMarker||!this.endMarker)return;const e=this.executedMarker.line,i=this.endMarker.line;if(e===i)return;let s="",r;for(let n=e;n<i;n++)r=this.a.buffer.active.getLine(n),r&&(s+=r.translateToString(!r.isWrapped)+(r.isWrapped?"":`
`));return s===""?void 0:s}getOutputMatch(e){if(!this.executedMarker||!this.endMarker)return;const i=this.endMarker.line;if(i===-1)return;const s=this.a.buffer.active,r=Math.max(this.executedMarker.line,0),n=e.lineMatcher,o=typeof n=="string"?1:e.length||ou(n),a=[];let c;if(e.anchor==="bottom")for(let h=i-(e.offset||0);h>=r;h--){let u=h;const l=h;for(;u>=r&&s.getLine(u)?.isWrapped;)u--;if(h=u,a.unshift(_1(s,u,l,this.a.cols)),c||(c=a[0].match(n)),a.length>=o)break}else for(let h=r+(e.offset||0);h<i;h++){const u=h;let l=h;for(;l+1<i&&s.getLine(l+1)?.isWrapped;)l++;if(h=l,a.push(_1(s,u,l,this.a.cols)),c||(c=a[a.length-1].match(n)),a.length>=o)break}return c?{regexMatch:c,outputLines:a}:void 0}hasOutput(){return!this.executedMarker?.isDisposed&&!this.endMarker?.isDisposed&&!!(this.executedMarker&&this.endMarker&&this.executedMarker.line<this.endMarker.line)}getPromptRowCount(){return R1(this,this.a.buffer.active)}getCommandRowCount(){return N1(this)}},I1=class{constructor(t){this.c=t}serialize(t){if(this.commandStartMarker)return{promptStartLine:this.promptStartMarker?.line,startLine:this.commandStartMarker.line,startX:this.commandStartX,endLine:void 0,executedLine:void 0,executedX:void 0,command:"",commandLineConfidence:"low",isTrusted:!0,cwd:t,exitCode:void 0,commandStartLineContent:void 0,timestamp:0,duration:0,markProperties:void 0}}promoteToFullCommand(t,e,i,s){if(e===void 0&&this.command===void 0&&(this.command=""),this.command!==void 0&&!this.command.startsWith("\\")||i)return new M1(this.c,{command:i?"":this.command||"",commandLineConfidence:i?"low":this.commandLineConfidence||"low",isTrusted:!!this.isTrusted,promptStartMarker:this.promptStartMarker,marker:this.commandStartMarker,startX:this.commandStartX,endMarker:this.commandFinishedMarker,executedMarker:this.commandExecutedMarker,executedX:this.commandExecutedX,timestamp:Date.now(),duration:this.b||0,cwd:t,exitCode:e,commandStartLineContent:this.commandStartLineContent,markProperties:s})}markExecutedTime(){this.a===void 0&&(this.a=Date.now())}markFinishedTime(){this.b===void 0&&this.a!==void 0&&(this.b=Date.now()-this.a)}extractCommandLine(){return O1(this.c.buffer.active,this.c.cols,this.commandStartMarker,this.commandStartX,this.commandExecutedMarker,this.commandExecutedX)}getPromptRowCount(){return R1(this,this.c.buffer.active)}getCommandRowCount(){return N1(this)}};function O1(t,e,i,s,r,n){if(!i||!r||s===void 0||n===void 0)return"";let o="";for(let a=i.line;a<=r.line;a++){const c=t.getLine(a);c&&(o+=c.translateToString(!0,a===i.line?s:0,a===r.line?n:e))}return o}function _1(t,e,i,s){const r=Math.max(2048/s*2);i=Math.min(i,e+r);let n="";for(let o=e;o<=i;o++){const a=t.getLine(o);a&&(n+=a.translateToString(!0,0,s))}return n}function ou(t){if(!t.multiline)return 1;const e=t.source;let i=1,s=e.indexOf("\\n");for(;s!==-1;)i++,s=e.indexOf("\\n",s+1);return i}function R1(t,e){const i="hasOutput"in t?t.marker:t.commandStartMarker;if(!i||!t.promptStartMarker)return 1;let s=1,r=t.promptStartMarker.line;for(;r<i.line&&(e.getLine(r)?.translateToString(!0)??"").length===0;)r++;return s=i.line-r+1,s}function N1(t){const e="hasOutput"in t?t.marker:t.commandStartMarker,i="hasOutput"in t?t.executedMarker:t.commandExecutedMarker;if(!e||!i)return 1;let r=Math.max(i.line,e.line)-e.line+1;return("hasOutput"in t?t.executedX:t.commandExecutedX)===0&&r--,r}var T1;(function(t){t[t.Unknown=0]="Unknown",t[t.Input=1]="Input",t[t.Execute=2]="Execute"})(T1||(T1={}));var Mi=class extends V{get value(){return this.r}get prefix(){return this.r.substring(0,this.s)}get suffix(){return this.r.substring(this.s,this.t===-1?void 0:this.t)}get cursorIndex(){return this.s}get ghostTextIndex(){return this.t}constructor(e,i,s,r,n){super(),this.D=e,this.F=n,this.c=0,this.g=0,this.n="",this.r="",this.s=0,this.t=-1,this.u=this.B(new x),this.onDidStartInput=this.u.event,this.w=this.B(new x),this.onDidChangeInput=this.w.event,this.z=this.B(new x),this.onDidFinishInput=this.z.event,this.C=this.B(new x),this.onDidInterrupt=this.C.event,this.B(Y.any(this.D.onCursorMove,this.D.onData,this.D.onWriteParsed)(()=>this.L())),this.B(this.D.onData(o=>this.N(o))),this.B(i(o=>this.H(o))),this.B(s(()=>this.I())),this.B(r(()=>this.J())),this.B(this.onDidStartInput(()=>this.G("PromptInputModel#onDidStartInput"))),this.B(this.onDidChangeInput(()=>this.G("PromptInputModel#onDidChangeInput"))),this.B(this.onDidFinishInput(()=>this.G("PromptInputModel#onDidFinishInput"))),this.B(this.onDidInterrupt(()=>this.G("PromptInputModel#onDidInterrupt")))}G(e){this.F.getLevel()===k.Trace&&this.F.trace(e,this.getCombinedString())}setShellType(e){this.m=e}setContinuationPrompt(e){this.j=e,this.L()}setLastPromptLine(e){this.h=e,this.L()}setConfidentCommandLine(e){this.r!==e&&(this.r=e,this.s=-1,this.t=-1,this.w.fire(this.Z()))}getCombinedString(e){const i=this.r.replaceAll(`
`,"\u23CE");if(this.s===-1)return i;let s=`${i.substring(0,this.cursorIndex)}|`;return this.ghostTextIndex!==-1?(s+=`${i.substring(this.cursorIndex,this.ghostTextIndex)}[`,s+=`${i.substring(this.ghostTextIndex)}]`):s+=i.substring(this.cursorIndex),s==="|"&&e?"":s}serialize(){return{modelState:this.Z(),commandStartX:this.g,lastPromptLine:this.h,continuationPrompt:this.j,lastUserInput:this.n}}deserialize(e){this.r=e.modelState.value,this.s=e.modelState.cursorIndex,this.t=e.modelState.ghostTextIndex,this.g=e.commandStartX,this.h=e.lastPromptLine,this.j=e.continuationPrompt,this.n=e.lastUserInput}H(e){this.c!==1&&(this.c=1,this.f=e.marker,this.g=this.D.buffer.active.cursorX,this.r="",this.s=0,this.u.fire(this.Z()),this.w.fire(this.Z()),this.h&&this.g!==this.h.length&&this.D.buffer.active.getLine(this.f.line)?.translateToString(!0).startsWith(this.h)&&(this.g=this.h.length,this.L()))}I(){this.c===1&&(this.g=this.D.buffer.active.cursorX,this.w.fire(this.Z()),this.L())}J(){if(this.c===2)return;this.s=-1,this.t!==-1&&(this.r=this.r.substring(0,this.t),this.t=-1);const e=this.Z();this.n===""&&(this.n="",this.C.fire(e)),this.c=2,this.z.fire(e),this.w.fire(e)}L(){try{this.M()}catch(e){this.F.error("Error while syncing prompt input model",e)}}M(){if(this.c!==1)return;let e=this.f?.line;if(e===void 0)return;const i=this.D.buffer.active;let s=i.getLine(e);const r=i.baseY+i.cursorY;let n,o=s?.translateToString(!0,this.g);if(this.m==="fish"&&(!s||!o)&&(e+=1,s=i.getLine(e),s&&(o=s.translateToString(!0),n=r===e?i.cursorX:o?.trimEnd().length)),s===void 0||o===void 0){this.F.trace("PromptInputModel#_sync: no line");return}let a=o,c=-1;n===void 0&&(r===e?n=this.X(this.g,i,s):n=o.trimEnd().length);for(let h=e+1;h<=r;h++){const u=i.getLine(h),l=u?.translateToString(!0);if(l&&u){if(u.isWrapped||r===h&&this.j&&!this.U(l)){a+=`${l}`;const f=this.X(0,i,u);r===h?n+=f:n+=l.length}else if(this.m==="fish")a.endsWith("\\")?(a=a.substring(0,a.length-1),a+=`${l.trim()}`,n+=l.trim().length-1):/^ {6,}/.test(l)?(a+=`
${l.trim()}`,n+=l.trim().length+1):(a+=l,n+=l.length);else if(this.j===void 0||this.U(l)){const f=this.S(l);if(a+=`
${f}`,r===h){const d=this.W(u,l),p=this.X(d,i,u);n+=p+1}else n+=f.length+1}}}for(let h=r+1;h<i.baseY+this.D.rows;h++){const u=i.getLine(h),l=u?.translateToString(!0);if(l&&u)this.m==="fish"?a+=`${l}`:this.j===void 0||this.U(l)?a+=`
${this.S(l)}`:a+=l;else break}this.F.getLevel()===k.Trace&&this.F.trace(`PromptInputModel#_sync: ${this.getCombinedString()}`);{let h=this.r.length-this.r.trimEnd().length;this.n==="\x7F"&&(this.n="",n===this.s-1&&(this.r.trimEnd().length>a.trimEnd().length&&a.trimEnd().length<=n?h=Math.max(this.r.length-1-a.trimEnd().length,0):h=Math.max(h-1,0))),this.n==="\x1B[3~"&&(this.n="",n===this.s&&(h=Math.max(h-1,0)));const u=a.split(`
`),l=u.length>1,f=a.trimEnd();if(!l){f.length<a.length&&(this.n===" "&&(this.n="",n>f.length&&n>this.s&&h++),h=Math.max(n-f.length,h,0));const d=n===0?"":a[n-1];h>0&&n===this.s+1&&this.n!==""&&d!==" "&&(h=this.r.length-this.s)}if(l){u[u.length-1]=u.at(-1)?.trimEnd()??"";const d=(u.length-1)*(this.j?.length??0);h=Math.max(0,n-a.length-d)}a=u.map(d=>d.trimEnd()).join(`
`)+" ".repeat(h)}c=this.O(i,s,n),(this.r!==a||this.s!==n||this.t!==c)&&(this.r=a,this.s=n,this.t=c,this.w.fire(this.Z()))}N(e){this.n=e}O(e,i,s){if(!this.value.trim().length)return-1;let r=-1,n=!1,o=e.cursorX;for(;o>0;){const a=i.getCell(--o);if(!a)break;if(a.getChars().trim().length>0){n=!this.Y(a);break}}if(n){let a=0,c=e.cursorX;for(;c<i.length;){const h=i.getCell(c++);if(!h||h.getCode()===0)break;if(this.Y(h)){r=s+a;break}a+=h.getChars().length}}return r===-1&&(r=this.P(e,i,s)),r>-1&&this.value.substring(r).endsWith(" ")&&(this.r=this.value.trim(),this.value.substring(r)||(r=-1)),r}P(e,i,s){let r=-1,n=e.cursorX;const o=new Map;let a=i.getCell(n),c=a;for(;c&&n<i.length;){const u=this.Q(c);o.set(u,[...o.get(u)??[],n]),c=i.getCell(++n),c?.getChars().trim().length&&(a=c)}if(!a?.getChars().trim().length||this.R(i.getCell(this.g),a))return-1;const h=o.get(this.Q(a));if(h){for(let u=1;u<h.length;u++)if(h[u]!==h[u-1]+1)return-1;e.baseY+e.cursorY===this.f?.line?r=h[0]-this.g:r=h[0]}if(r!==-1)for(let u=e.cursorX;u>=this.g;u--){const l=i.getCell(u);if(l?.getChars.length&&l&&l.getCode()!==0&&this.R(a,l))return-1}return r>=s?r:-1}Q(e){return`${e.getFgColor()}${e.getBgColor()}${e.isBold()}${e.isItalic()}${e.isDim()}${e.isUnderline()}${e.isBlink()}${e.isInverse()}${e.isInvisible()}${e.isStrikethrough()}${e.isOverline()}${e.getFgColorMode()}${e.getBgColorMode()}`}R(e,i){return!e||!i?!1:e.getFgColor()===i.getFgColor()&&e.getBgColor()===i.getBgColor()&&e.isBold()===i.isBold()&&e.isItalic()===i.isItalic()&&e.isDim()===i.isDim()&&e.isUnderline()===i.isUnderline()&&e.isBlink()===i.isBlink()&&e.isInverse()===i.isInverse()&&e.isInvisible()===i.isInvisible()&&e.isStrikethrough()===i.isStrikethrough()&&e.isOverline()===i.isOverline()&&e?.getBgColorMode()===i?.getBgColorMode()&&e?.getFgColorMode()===i?.getFgColorMode()}S(e){return this.U(e)&&(e=e.substring(this.j.length)),e}U(e){return!!(this.j&&e.startsWith(this.j.trimEnd()))}W(e,i){if(!this.j||!i.startsWith(this.j.trimEnd()))return 0;let s="",r=0,n;for(;s!==this.j&&(n=e.getCell(r++),!!n);)s+=n.getChars();return r}X(e,i,s){return s?.translateToString(!0,e,i.cursorX).length??0}Y(e){return!!(e.isItalic()||e.isDim())}Z(){return Object.freeze({value:this.r,prefix:this.prefix,suffix:this.suffix,cursorIndex:this.s,ghostTextIndex:this.t})}};__decorate([Gr(0)],Mi.prototype,"L",null),Mi=__decorate([__param(4,Jt)],Mi);var Ii=class extends V{get promptInputModel(){return this.c}get hasRichCommandDetection(){return this.w}get promptType(){return this.z}get commands(){return this.f}get executingCommand(){return this.n.command}get executingCommandObject(){if(this.n.commandStartMarker)return this.n.promoteToFullCommand(this.g,void 0,this.u?.ignoreCommandLine??!1,void 0)}get executingCommandConfidence(){const e=this.n;return"commandLineConfidence"in e?e.commandLineConfidence:void 0}get currentCommand(){return this.n}get cwd(){return this.g}get promptTerminator(){return this.h}constructor(e,i){super(),this.P=e,this.Q=i,this.type=2,this.f=[],this.r=[],this.t=!1,this.w=!1,this.F=this.B(new x),this.onCommandStarted=this.F.event,this.G=this.B(new x),this.onCommandStartChanged=this.G.event,this.H=this.B(new x),this.onBeforeCommandFinished=this.H.event,this.I=this.B(new x),this.onCommandFinished=this.I.event,this.J=this.B(new x),this.onCommandExecuted=this.J.event,this.L=this.B(new x),this.onCommandInvalidated=this.L.event,this.M=this.B(new x),this.onCurrentCommandInvalidated=this.M.event,this.N=this.B(new x),this.onPromptTypeChanged=this.N.event,this.O=this.B(new x),this.onSetRichCommandDetection=this.O.event,this.n=new I1(this.P),this.c=this.B(new Mi(this.P,this.onCommandStarted,this.onCommandStartChanged,this.onCommandExecuted,this.Q)),this.B(this.onCommandExecuted(r=>{if(r.commandLineConfidence!=="high"){const n=r;r.command=n.extractCommandLine(),r.commandLineConfidence="low","getOutput"in n?n.promptStartMarker&&n.marker&&n.executedMarker&&r.command.indexOf(`
`)===-1&&n.startX!==void 0&&n.startX>0&&(r.commandLineConfidence="medium"):n.promptStartMarker&&n.commandStartMarker&&n.commandExecutedMarker&&r.command.indexOf(`
`)===-1&&n.commandStartX!==void 0&&n.commandStartX>0&&(r.commandLineConfidence="medium")}}));const s=this;this.C=new class{get onCurrentCommandInvalidatedEmitter(){return s.M}get onCommandStartedEmitter(){return s.F}get onCommandExecutedEmitter(){return s.J}get dimensions(){return s.s}get isCommandStorageDisabled(){return s.t}get commandMarkers(){return s.r}set commandMarkers(r){s.r=r}get clearCommandsInViewport(){return s.U.bind(s)}},this.D=this.B(new sa(new Rs(this.P,this,this.C,this.Q))),this.s={cols:this.P.cols,rows:this.P.rows},this.B(this.P.onResize(r=>this.R(r))),this.B(this.P.onCursorMove(()=>this.S()))}R(e){this.D.value.preHandleResize?.(e),this.s.cols=e.cols,this.s.rows=e.rows}S(){this.q.isDisposed||this.P.buffer.active===this.P.buffer.normal&&this.n.commandStartMarker&&this.P.buffer.active.baseY+this.P.buffer.active.cursorY<this.n.commandStartMarker.line&&(this.U(),this.n.isInvalid=!0,this.M.fire({reason:"windows"}))}U(){let e=0;for(let i=this.f.length-1;i>=0;i--){const s=this.f[i].marker?.line;if(s&&s<this.P.buffer.active.baseY)break;e++}e>0&&this.L.fire(this.f.splice(this.f.length-e,e))}setContinuationPrompt(e){this.c.setContinuationPrompt(e)}setPromptTerminator(e,i){this.Q.debug("CommandDetectionCapability#setPromptTerminator",e),this.h=e,this.c.setLastPromptLine(i)}setCwd(e){this.g=e}setIsWindowsPty(e){if(e&&!(this.D.value instanceof Xe)){const i=this;this.D.value=new Xe(this.P,this,new class{get onCurrentCommandInvalidatedEmitter(){return i.M}get onCommandStartedEmitter(){return i.F}get onCommandExecutedEmitter(){return i.J}get dimensions(){return i.s}get isCommandStorageDisabled(){return i.t}get commandMarkers(){return i.r}set commandMarkers(s){i.r=s}get clearCommandsInViewport(){return i.U.bind(i)}},this.Q)}else!e&&!(this.D.value instanceof Rs)&&(this.D.value=new Rs(this.P,this,this.C,this.Q))}setHasRichCommandDetection(e){this.w=e,this.O.fire(e)}setPromptType(e){this.z=e,this.N.fire(e)}setIsCommandStorageDisabled(){this.t=!0}getCommandForLine(e){if(this.n.promptStartMarker&&e>=this.n.promptStartMarker?.line)return this.n;if(this.f.length!==0&&!((this.f[0].promptStartMarker??this.f[0].marker).line>e)){for(let i=this.commands.length-1;i>=0;i--)if((this.commands[i].promptStartMarker??this.commands[i].marker).line<=e)return this.commands[i]}}getCwdForLine(e){if(this.n.promptStartMarker&&e>=this.n.promptStartMarker?.line)return this.g;const i=this.getCommandForLine(e);if(i&&"cwd"in i)return i.cwd}handlePromptStart(e){const i=this.commands.at(-1);i?.endMarker&&i?.executedMarker&&i.endMarker.line===i.executedMarker.line&&(this.Q.debug("CommandDetectionCapability#handlePromptStart adjusted commandFinished",`${i.endMarker.line} -> ${i.executedMarker.line+1}`),i.endMarker=Ge(this.P,i.executedMarker,1)),this.n.promptStartMarker=e?.marker||(i?.endMarker?Ge(this.P,i.endMarker):this.P.registerMarker(0)),this.Q.debug("CommandDetectionCapability#handlePromptStart",this.P.buffer.active.cursorX,this.n.promptStartMarker?.line)}handleContinuationStart(){this.n.currentContinuationMarker=this.P.registerMarker(0),this.Q.debug("CommandDetectionCapability#handleContinuationStart",this.n.currentContinuationMarker)}handleContinuationEnd(){if(!this.n.currentContinuationMarker){this.Q.warn("CommandDetectionCapability#handleContinuationEnd Received continuation end without start");return}this.n.continuations||(this.n.continuations=[]),this.n.continuations.push({marker:this.n.currentContinuationMarker,end:this.P.buffer.active.cursorX}),this.n.currentContinuationMarker=void 0,this.Q.debug("CommandDetectionCapability#handleContinuationEnd",this.n.continuations[this.n.continuations.length-1])}handleRightPromptStart(){this.n.commandRightPromptStartX=this.P.buffer.active.cursorX,this.Q.debug("CommandDetectionCapability#handleRightPromptStart",this.n.commandRightPromptStartX)}handleRightPromptEnd(){this.n.commandRightPromptEndX=this.P.buffer.active.cursorX,this.Q.debug("CommandDetectionCapability#handleRightPromptEnd",this.n.commandRightPromptEndX)}handleCommandStart(e){if(this.u=e,this.n.cwd=this.g,this.n.commandStartMarker=e?.marker||this.n.commandStartMarker,this.n.commandStartMarker?.line===this.P.buffer.active.cursorY){this.n.commandStartX=this.P.buffer.active.cursorX,this.G.fire(),this.Q.debug("CommandDetectionCapability#handleCommandStart",this.n.commandStartX,this.n.commandStartMarker?.line);return}this.D.value.handleCommandStart(e)}handleCommandExecuted(e){this.D.value.handleCommandExecuted(e),this.n.markExecutedTime()}handleCommandFinished(e,i){if(this.n.commandExecutedMarker||this.handleCommandExecuted(),this.n.markFinishedTime(),this.D.value.preHandleCommandFinished?.(),this.Q.debug("CommandDetectionCapability#handleCommandFinished",this.P.buffer.active.cursorX,i?.marker?.line,this.n.command,this.n),e===void 0){const r=this.commands.length>0?this.commands[this.commands.length-1]:void 0;this.n.command&&this.n.command.length>0&&r?.command===this.n.command&&(e=r.exitCode)}if(this.n.commandStartMarker===void 0||!this.P.buffer.active)return;this.n.commandFinishedMarker=i?.marker||this.P.registerMarker(0),this.D.value.postHandleCommandFinished?.();const s=this.n.promoteToFullCommand(this.g,e,this.u?.ignoreCommandLine??!1,i?.markProperties);s&&(this.f.push(s),this.H.fire(s),this.Q.debug("CommandDetectionCapability#onCommandFinished",s),this.I.fire(s)),this.n=new I1(this.P),this.u=void 0}setCommandLine(e,i){this.Q.debug("CommandDetectionCapability#setCommandLine",e,i),this.n.command=e,this.n.commandLineConfidence="high",this.n.isTrusted=i,i&&this.c.setConfidentCommandLine(e)}serialize(){const e=this.commands.map(s=>s.serialize(this.t)),i=this.n.serialize(this.g);return i&&e.push(i),{isWindowsPty:this.D.value instanceof Xe,hasRichCommandDetection:this.w,commands:e,promptInputModel:this.c.serialize()}}deserialize(e){e.isWindowsPty&&this.setIsWindowsPty(e.isWindowsPty),e.hasRichCommandDetection&&this.setHasRichCommandDetection(e.hasRichCommandDetection);const i=this.P.buffer.normal;for(const s of e.commands){if(!s.endLine){const n=s.startLine!==void 0?this.P.registerMarker(s.startLine-(i.baseY+i.cursorY)):void 0;if(!n)continue;this.n.commandStartMarker=s.startLine!==void 0?this.P.registerMarker(s.startLine-(i.baseY+i.cursorY)):void 0,this.n.commandStartX=s.startX,this.n.promptStartMarker=s.promptStartLine!==void 0?this.P.registerMarker(s.promptStartLine-(i.baseY+i.cursorY)):void 0,this.g=s.cwd,this.F.fire({marker:n});continue}const r=M1.deserialize(this.P,s,this.t);r&&(this.f.push(r),this.Q.debug("CommandDetectionCapability#onCommandFinished",r),this.I.fire(r))}e.promptInputModel&&this.c.deserialize(e.promptInputModel)}};__decorate([ls(500)],Ii.prototype,"S",null),Ii=__decorate([__param(1,Jt)],Ii);var Rs=class extends V{constructor(t,e,i,s){super(),this.c=t,this.f=e,this.g=i,this.h=s,this.B(t.parser.registerCsiHandler({final:"J"},r=>(r.length>=1&&(r[0]===2||r[0]===3)&&i.clearCommandsInViewport(),!1)))}handleCommandStart(t){const e=this.f.currentCommand;e.commandStartX=this.c.buffer.active.cursorX,e.commandStartMarker=t?.marker||this.c.registerMarker(0),e.commandExecutedMarker?.dispose(),e.commandExecutedMarker=void 0,e.commandExecutedX=void 0;for(const i of this.g.commandMarkers)i.dispose();this.g.commandMarkers.length=0,this.g.onCommandStartedEmitter.fire({marker:t?.marker||e.commandStartMarker,markProperties:t?.markProperties}),this.h.debug("CommandDetectionCapability#handleCommandStart",e.commandStartX,e.commandStartMarker?.line)}handleCommandExecuted(t){const e=this.f.currentCommand;e.commandExecutedMarker=t?.marker||this.c.registerMarker(0),e.commandExecutedX=this.c.buffer.active.cursorX,this.h.debug("CommandDetectionCapability#handleCommandExecuted",e.commandExecutedX,e.commandExecutedMarker?.line),!(!e.commandStartMarker||!e.commandExecutedMarker||e.commandStartX===void 0)&&(e.command=this.f.promptInputModel.ghostTextIndex>-1?this.f.promptInputModel.value.substring(0,this.f.promptInputModel.ghostTextIndex):this.f.promptInputModel.value,this.g.onCommandExecutedEmitter.fire(e))}},B1;(function(t){t[t.MaxCheckLineCount=10]="MaxCheckLineCount",t[t.Interval=20]="Interval",t[t.MaximumPollCount=10]="MaximumPollCount"})(B1||(B1={}));var Xe=class extends V{constructor(e,i,s,r){super(),this.n=e,this.r=i,this.s=s,this.t=r,this.c=this.B(new sr),this.g=0,this.h=0,this.B(e.parser.registerCsiHandler({final:"J"},n=>(n.length>=1&&(n[0]===2||n[0]===3)&&this.s.clearCommandsInViewport(),!1))),this.B(this.r.onBeforeCommandFinished(n=>{(n.command.trim().toLowerCase()==="clear"||n.command.trim().toLowerCase()==="cls")&&(this.f?.cancel(),this.f=void 0,this.s.clearCommandsInViewport(),this.r.currentCommand.isInvalid=!0,this.s.onCurrentCommandInvalidatedEmitter.fire({reason:"windows"}))}))}preHandleResize(e){const i=this.n.buffer.active.baseY,s=e.rows-this.s.dimensions.rows;s>0&&this.D().then(()=>{const r=Math.min(s,i);for(let n=this.r.commands.length-1;n>=0;n--){const o=this.r.commands[n];if(!o.marker||o.marker.line<i||o.commandStartLineContent===void 0)break;const a=this.n.buffer.active.getLine(o.marker.line);if(!a||a.translateToString(!0)===o.commandStartLineContent)continue;const c=o.marker.line-r;this.n.buffer.active.getLine(c)?.translateToString(!0)===o.commandStartLineContent&&this.n._core._bufferService.buffer.lines.onDeleteEmitter.fire({index:this.n.buffer.active.baseY,amount:r})}})}handleCommandStart(){this.r.currentCommand.commandStartX=this.n.buffer.active.cursorX,this.s.commandMarkers.length=0;const e=this.r.currentCommand.commandStartMarker=this.r.currentCommand.promptStartMarker?Ge(this.n,this.r.currentCommand.promptStartMarker):this.n.registerMarker(0);this.r.currentCommand.commandStartX=0,this.g=0,this.h=0,this.f=new Hh(()=>this.u(e),20),this.f.schedule()}u(e){if(this.q.isDisposed)return;const i=this.n.buffer.active;let s=this.g;for(;s<10&&e.line+s<i.baseY+this.n.rows;){if(this.C()){const r=this.F(e.line+s);if(r){const n=typeof r=="string"?r:r.prompt;if(this.r.currentCommand.commandStartMarker=this.n.registerMarker(0),typeof r=="object"&&r.likelySingleLine){this.t.debug("CommandDetectionCapability#_tryAdjustCommandStartMarker adjusted promptStart",`${this.r.currentCommand.promptStartMarker?.line} -> ${this.r.currentCommand.commandStartMarker.line}`),this.r.currentCommand.promptStartMarker?.dispose(),this.r.currentCommand.promptStartMarker=Ge(this.n,this.r.currentCommand.commandStartMarker);const o=this.r.commands.at(-1);o&&this.r.currentCommand.commandStartMarker.line!==o.endMarker?.line&&(o.endMarker?.dispose(),o.endMarker=Ge(this.n,this.r.currentCommand.commandStartMarker))}this.r.currentCommand.commandStartX=n.length,this.t.debug("CommandDetectionCapability#_tryAdjustCommandStartMarker adjusted commandStart",`${e.line} -> ${this.r.currentCommand.commandStartMarker.line}:${this.r.currentCommand.commandStartX}`),this.w();return}}s++}s<10?(this.g=s,++this.h<10?this.f?.schedule():this.w()):this.w()}w(){if(this.f&&(this.h=10,this.f.flush(),this.f=void 0),this.r.currentCommand.commandExecutedMarker||(this.c.value=this.n.onCursorMove(()=>{if(this.s.commandMarkers.length===0||this.s.commandMarkers[this.s.commandMarkers.length-1].line!==this.n.buffer.active.cursorY){const e=this.n.registerMarker(0);e&&this.s.commandMarkers.push(e)}})),this.r.currentCommand.commandStartMarker){const e=this.n.buffer.active.getLine(this.r.currentCommand.commandStartMarker.line);e&&(this.r.currentCommand.commandStartLineContent=e.translateToString(!0))}this.s.onCommandStartedEmitter.fire({marker:this.r.currentCommand.commandStartMarker}),this.t.debug("CommandDetectionCapability#_handleCommandStartWindows",this.r.currentCommand.commandStartX,this.r.currentCommand.commandStartMarker?.line)}handleCommandExecuted(e){this.f&&this.w(),this.c.clear(),this.z(),this.r.currentCommand.commandExecutedX=this.n.buffer.active.cursorX,this.s.onCommandExecutedEmitter.fire(this.r.currentCommand),this.t.debug("CommandDetectionCapability#handleCommandExecuted",this.r.currentCommand.commandExecutedX,this.r.currentCommand.commandExecutedMarker?.line)}preHandleCommandFinished(){this.r.currentCommand.commandExecutedMarker||(this.s.commandMarkers.length===0&&(this.r.currentCommand.commandStartMarker||(this.r.currentCommand.commandStartMarker=this.n.registerMarker(0)),this.r.currentCommand.commandStartMarker&&this.s.commandMarkers.push(this.r.currentCommand.commandStartMarker)),this.z())}postHandleCommandFinished(){const e=this.r.currentCommand,i=e.command,s=e.commandStartMarker?.line,r=e.commandExecutedMarker?.line;if(!i||i.length===0||s===void 0||s===-1||r===void 0||r===-1)return;let n=0,o=!1;for(let a=s;a<=r;a++){const c=this.n.buffer.active.getLine(a);if(!c)break;const h=c.translateToString(!0);for(let u=0;u<h.length;u++){for(;i.length<n&&i[n]===" ";)n++;if(h[u]===i[n]&&n++,n===i.length){const l=u>=this.n.cols-1;e.commandExecutedMarker=this.n.registerMarker(a-(this.n.buffer.active.baseY+this.n.buffer.active.cursorY)+(l?1:0)),e.commandExecutedX=l?0:u+1,o=!0;break}}if(o)break}}z(){if(this.s.commandMarkers.length!==0){if(this.s.commandMarkers=this.s.commandMarkers.sort((e,i)=>e.line-i.line),this.r.currentCommand.commandStartMarker=this.s.commandMarkers[0],this.r.currentCommand.commandStartMarker){const e=this.n.buffer.active.getLine(this.r.currentCommand.commandStartMarker.line);e&&(this.r.currentCommand.commandStartLineContent=e.translateToString(!0))}this.r.currentCommand.commandExecutedMarker=this.s.commandMarkers[this.s.commandMarkers.length-1],this.s.onCommandExecutedEmitter.fire(this.r.currentCommand)}}C(){const e=this.r.commands.at(-1);if(!e)return!0;const i=this.n.buffer.active.baseY+this.n.buffer.active.cursorY,s=(e.endMarker?e.endMarker.line:e.marker?.line)??-1;return i>s}D(){const e=this.n.buffer.active.cursorX,i=this.n.buffer.active.cursorY;let s=0;return new Promise((r,n)=>{const o=setInterval(()=>{if(e!==this.n.buffer.active.cursorX||i!==this.n.buffer.active.cursorY){r(),clearInterval(o);return}s+=10,s>1e3&&(clearInterval(o),r())},10)})}F(e=this.n.buffer.active.baseY+this.n.buffer.active.cursorY){const i=this.n.buffer.active.getLine(e);if(!i)return;const s=i.translateToString(!0);if(!s)return;const r=s.match(/(?<prompt>(\(.+\)\s)?(?:PS.+>\s?))/)?.groups?.prompt;if(r){const h=this.G(r,s,">");if(h)return{prompt:h,likelySingleLine:!0}}const n=s.match(/.*\u276f(?=[^\u276f]*$)/g)?.[0];if(n){const h=this.G(n,s,"\u276F");if(h)return h}const o=s.match(/^(?<prompt>\$)/)?.groups?.prompt;if(o){const h=this.G(o,s,"$");if(h)return h}const a=s.match(/^(?<prompt>>>> )/g)?.groups?.prompt;if(a)return{prompt:a,likelySingleLine:!0};if(this.r.promptTerminator&&s.trim().endsWith(this.r.promptTerminator)){const h=this.G(s,s,this.r.promptTerminator);if(h)return h}const c=s.match(/^(?<prompt>(\(.+\)\s)?(?:[A-Z]:\\.*>))/);return c?.groups?.prompt?{prompt:c.groups.prompt,likelySingleLine:!0}:void 0}G(e,i,s){if(e)return i===e&&e.endsWith(s)&&(e+=" "),e}};Xe=__decorate([__param(3,Jt)],Xe);function Ge(t,e,i=0){return t.registerMarker(e.line-(t.buffer.active.baseY+t.buffer.active.cursorY)+i)}var au=class extends V{constructor(){super(...arguments),this.type=0,this.a="",this.b=new Map,this.c=this.B(new x),this.onDidChangeCwd=this.c.event}get cwds(){return Array.from(this.b.keys())}getCwd(){return this.a}updateCwd(t){const e=this.a!==t;this.a=t;const i=this.b.get(this.a)||0;this.b.delete(this.a),this.b.set(this.a,i+1),e&&this.c.fire(t)}},F1;(function(t){t[t.MinimumPromptLength=2]="MinimumPromptLength"})(F1||(F1={}));var hu=class extends qt{get commands(){return this.a}constructor(t,e){super(),this.c=t,this.h=e,this.type=3,this.a=[],this.b=this.add(new x),this.onCommandFinished=this.b.event,this.add(this.c.onData(i=>this.j(i))),this.add(this.c.parser.registerCsiHandler({final:"J"},i=>(i.length>=1&&(i[0]===2||i[0]===3)&&this.n(),!1))),this.h&&this.add(this.h(()=>this.m()))}j(t){t==="\r"&&this.m()}m(){if(this.c&&this.c.buffer.active.cursorX>=2){const t=this.c.registerMarker(0);t&&(this.a.push(t),this.b.fire(t))}}n(){let t=0;for(let e=this.a.length-1;e>=0&&!(this.a[e].line<this.c.buffer.active.baseY);e--)t++;this.a.splice(this.a.length-t,t)}},cu=class extends V{constructor(t){super(),this.f=t,this.type=4,this.a=new Map,this.b=new Map,this.c=this.B(new x),this.onMarkAdded=this.c.event}*markers(){for(const t of this.a.values())yield t;for(const t of this.b.values())yield t}addMark(t){const e=t?.marker||this.f.registerMarker(),i=t?.id;e&&(i?(this.a.set(i,e),e.onDispose(()=>this.a.delete(i))):(this.b.set(e.id,e),e.onDispose(()=>this.b.delete(e.id))),this.c.fire({marker:e,id:i,hidden:t?.hidden,hoverMessage:t?.hoverMessage}))}getMark(t){return this.a.get(t)}},lu=class extends V{constructor(){super(...arguments),this.type=5,this.b={value:new Map,isTrusted:!0},this.c=this.B(new x),this.onDidChangeEnv=this.c.event}get env(){return this.g()}setEnvironment(t,e){if(!yi(this.env.value,t)){this.b.value.clear();for(const[i,s]of Object.entries(t))s!==void 0&&this.b.value.set(i,s);this.b.isTrusted=e,this.f()}}startEnvironmentSingleVar(t,e){t?this.a={value:new Map,isTrusted:e}:this.a={value:new Map(this.b.value),isTrusted:this.b.isTrusted&&e}}setEnvironmentSingleVar(t,e,i){this.a&&t!==void 0&&e!==void 0&&(this.a.value.set(t,e),this.a.isTrusted&&=i)}endEnvironmentSingleVar(t){if(!this.a)return;this.a.isTrusted&&=t,!Vo(this.b.value,this.a.value)&&(this.b=this.a,this.f()),this.a=void 0}deleteEnvironmentSingleVar(t,e,i){this.a&&t!==void 0&&e!==void 0&&(this.a.value.delete(t),this.a.isTrusted&&=i)}f(){this.c.fire(this.g())}g(){return{value:Object.fromEntries(this.b.value),isTrusted:this.b.isTrusted}}},U1;(function(t){t[t.FinalTerm=133]="FinalTerm",t[t.VSCode=633]="VSCode",t[t.ITerm=1337]="ITerm",t[t.SetCwd=7]="SetCwd",t[t.SetWindowsFriendlyCwd=9]="SetWindowsFriendlyCwd"})(U1||(U1={}));var j1;(function(t){t.PromptStart="A",t.CommandStart="B",t.CommandExecuted="C",t.CommandFinished="D"})(j1||(j1={}));var W1;(function(t){t.PromptStart="A",t.CommandStart="B",t.CommandExecuted="C",t.CommandFinished="D",t.CommandLine="E",t.ContinuationStart="F",t.ContinuationEnd="G",t.RightPromptStart="H",t.RightPromptEnd="I",t.Property="P",t.SetMark="SetMark",t.EnvJson="EnvJson",t.EnvSingleDelete="EnvSingleDelete",t.EnvSingleStart="EnvSingleStart",t.EnvSingleEntry="EnvSingleEntry",t.EnvSingleEnd="EnvSingleEnd"})(W1||(W1={}));var z1;(function(t){t.SetMark="SetMark",t.CurrentDir="CurrentDir"})(z1||(z1={}));var uu=class extends V{get seenSequences(){return this.g}get status(){return this.h}constructor(t,e,i,s,r){super(),this.n=t,this.r=e,this.s=i,this.t=s,this.u=r,this.capabilities=this.B(new nu),this.b=!1,this.f=[],this.g=new Set,this.h=0,this.j=new x,this.onDidChangeStatus=this.j.event,this.m=new x,this.onDidChangeSeenSequences=this.m.event,this.B(wt(()=>{this.G(),this.w()}))}w(){Rt(this.f),this.f.length=0}activate(t){this.a=t,this.capabilities.add(3,this.B(new hu(this.a,this.s))),this.B(t.parser.registerOscHandler(633,e=>this.D(e))),this.B(t.parser.registerOscHandler(1337,e=>this.M(e))),this.f.push(t.parser.registerOscHandler(133,e=>this.z(e))),this.B(t.parser.registerOscHandler(7,e=>this.O(e))),this.B(t.parser.registerOscHandler(9,e=>this.N(e))),this.F()}getMarkerId(t,e){this.R(t).getMark(e)}y(t){this.g.has(t)||(this.g.add(t),this.m.fire(this.g))}z(t){const e=this.C(t);return this.h===0&&(this.h=1,this.j.fire(this.h)),e}C(t){if(!this.a)return!1;const[e,...i]=t.split(";");switch(this.y(e),e){case"A":return this.Q(this.a).handlePromptStart(),!0;case"B":return this.Q(this.a).handleCommandStart({ignoreCommandLine:!0}),!0;case"C":return this.Q(this.a).handleCommandExecuted(),!0;case"D":{const s=i.length===1?parseInt(i[0]):void 0;return this.Q(this.a).handleCommandFinished(s),!0}}return!1}D(t){const e=this.H(t);return!this.b&&e&&(this.t?.publicLog2("terminal/shellIntegrationActivationSucceeded"),this.b=!0,this.G()),this.h!==2&&(this.h=2,this.j.fire(this.h)),e}async F(){!this.t||this.r||(this.c=setTimeout(()=>{!this.capabilities.get(2)&&!this.capabilities.get(0)&&(this.t?.publicLog2("terminal/shellIntegrationActivationTimeout"),this.u.warn("Shell integration failed to add capabilities within 10 seconds")),this.b=!0},1e4))}G(){this.c!==void 0&&(clearTimeout(this.c),this.c=void 0)}H(t){if(!this.a)return!1;const e=t.indexOf(";"),i=e===-1?t:t.substring(0,e);this.y(i);const s=e===-1?[]:t.substring(e+1).split(";");switch(i){case"A":return this.Q(this.a).handlePromptStart(),!0;case"B":return this.Q(this.a).handleCommandStart(),!0;case"C":return this.Q(this.a).handleCommandExecuted(),!0;case"D":{const r=s[0],n=r!==void 0?parseInt(r):void 0;return this.Q(this.a).handleCommandFinished(n),!0}case"E":{const r=s[0],n=s[1];let o;return r!==void 0?o=Qe(r):o="",this.Q(this.a).setCommandLine(o,n===this.n),!0}case"F":return this.Q(this.a).handleContinuationStart(),!0;case"G":return this.Q(this.a).handleContinuationEnd(),!0;case"EnvJson":{const r=s[0],n=s[1];if(r!==void 0)try{const o=JSON.parse(Qe(r));this.S().setEnvironment(o,n===this.n)}catch{this.u.warn("Failed to parse environment from shell integration sequence",r)}return!0}case"EnvSingleStart":return this.S().startEnvironmentSingleVar(s[0]==="1",s[1]===this.n),!0;case"EnvSingleDelete":{const r=s[0],n=s[1],o=s[2];if(r!==void 0&&n!==void 0){const a=Qe(n);this.S().deleteEnvironmentSingleVar(r,a,o===this.n)}return!0}case"EnvSingleEntry":{const r=s[0],n=s[1],o=s[2];if(r!==void 0&&n!==void 0){const a=Qe(n);this.S().setEnvironmentSingleVar(r,a,o===this.n)}return!0}case"EnvSingleEnd":return this.S().endEnvironmentSingleVar(s[0]===this.n),!0;case"H":return this.Q(this.a).handleRightPromptStart(),!0;case"I":return this.Q(this.a).handleRightPromptEnd(),!0;case"P":{const r=s[0],n=r!==void 0?Qe(r):"",{key:o,value:a}=H1(n);if(a===void 0)return!0;switch(o){case"ContinuationPrompt":return this.I(Ph(a)),!0;case"Cwd":return this.L(a),!0;case"IsWindows":return this.Q(this.a).setIsWindowsPty(a==="True"),!0;case"HasRichCommandDetection":return this.Q(this.a).setHasRichCommandDetection(a==="True"),!0;case"Prompt":{const c=a.replace(/\x1b\[[0-9;]*m/g,"");return this.J(c),!0}case"PromptType":return this.Q(this.a).setPromptType(a),!0;case"Task":return this.R(this.a),this.capabilities.get(2)?.setIsCommandStorageDisabled(),!0}}case"SetMark":return this.R(this.a).addMark(fu(s)),!0}return!1}I(t){this.a&&this.Q(this.a).setContinuationPrompt(t)}J(t){if(!this.a)return;const e=t.substring(t.lastIndexOf(`
`)+1),i=e.substring(e.lastIndexOf(" "));i&&this.Q(this.a).setPromptTerminator(i,e)}L(t){t=Fl(t),this.P().updateCwd(t),this.capabilities.get(2)?.setCwd(t)}M(t){if(!this.a)return!1;const[e]=t.split(";");switch(this.y(`1337;${e}`),e){case"SetMark":this.R(this.a).addMark();default:{const{key:i,value:s}=H1(e);if(s===void 0)return!0;switch(i){case"CurrentDir":return this.L(s),!0}}}return!1}N(t){if(!this.a)return!1;const[e,...i]=t.split(";");switch(this.y(`9;${e}`),e){case"9":return i.length&&this.L(i[0]),!0}return!1}O(t){if(!this.a)return!1;const[e]=t.split(";");if(this.y(`7;${e}`),e.match(/^file:\/\/.*\//)){const i=j.parse(e);if(i.path&&i.path.length>0)return this.L(i.path),!0}return!1}serialize(){return!this.a||!this.capabilities.has(2)?{isWindowsPty:!1,hasRichCommandDetection:!1,commands:[],promptInputModel:void 0}:this.Q(this.a).serialize()}deserialize(t){if(!this.a)throw new Error("Cannot restore commands before addon is activated");const e=this.Q(this.a);e.deserialize(t),e.cwd&&this.L(e.cwd)}P(){let t=this.capabilities.get(0);return t||(t=this.B(new au),this.capabilities.add(0,t)),t}Q(t){let e=this.capabilities.get(2);return e||(e=this.B(new Ii(t,this.u)),this.capabilities.add(2,e)),e}R(t){let e=this.capabilities.get(4);return e||(e=this.B(new cu(t)),this.capabilities.add(4,e)),e}S(){let t=this.capabilities.get(5);return t||(t=this.B(new lu),this.capabilities.add(5,t)),t}};function Qe(t){return t.replaceAll(/\\(\\|x([0-9a-f]{2}))/gi,(e,i,s)=>s?String.fromCharCode(parseInt(s,16)):i)}function H1(t){const e=t.indexOf("=");return e===-1?{key:t,value:void 0}:{key:t.substring(0,e),value:t.substring(1+e)}}function fu(t){let e,i=!1;for(const s of t)s!==void 0&&(s==="Hidden"&&(i=!0),s.startsWith("Id=")&&(e=s.substring(3)));return{id:e,hidden:i}}function du(t,e={}){let i="";return e.excludeLeadingNewLine||(i+=`\r
`),i+="\x1B[0m\x1B[7m * ",e.loudFormatting?i+="\x1B[0;104m":i+="\x1B[0m",i+=` ${t} \x1B[0m
\r`,i}function Ns(t){const e=[];typeof t=="number"&&e.push("code/timeOrigin",t);function i(r,n){e.push(r,n?.startTime??Date.now())}function s(){const r=[];for(let n=0;n<e.length;n+=2)r.push({name:e[n],startTime:e[n+1]});return r}return{mark:i,getMarks:s}}function pu(){if(typeof performance=="object"&&typeof performance.mark=="function"&&!performance.nodeTiming)return typeof performance.timeOrigin!="number"&&!performance.timing?Ns():{mark(t,e){performance.mark(t,e)},getMarks(){let t=performance.timeOrigin;typeof t!="number"&&(t=(performance.timing.navigationStart||performance.timing.redirectStart||performance.timing.fetchStart)??0);const e=[{name:"code/timeOrigin",startTime:Math.round(t)}];for(const i of performance.getEntriesByType("mark"))e.push({name:i.name,startTime:Math.round(t+i.startTime)});return e}};if(typeof process=="object"){const t=performance?.timeOrigin;return Ns(t)}else return console.trace("perf-util loaded in UNKNOWN environment"),Ns()}function gu(t){return t.MonacoPerformanceMarks||(t.MonacoPerformanceMarks=pu()),t.MonacoPerformanceMarks}var q1=gu(globalThis),Ye=q1.mark,mu=q1.getMarks;import vu from"@xterm/headless";var wu=class extends V{constructor(t,e,i,s){super(),this.a=0,this.b=!1,this.c=!1,this.B(t.onProcessData(r=>{if(this.b||this.c)return;const n=typeof r=="string"?r:r.data;for(let o=0;o<n.length;o++)n[o]===e[this.a]?this.a++:this.f(),this.a===e.length&&(s.debug(`Auto reply match: "${e}", response: "${i}"`),t.input(i),this.c=!0,Ft(1e3).then(()=>this.c=!1),this.f())}))}f(){this.a=0}handleResize(){R&&(this.b=!0)}handleInput(){this.b=!1}},Ts=class{constructor(e){this.d=e,this.a=new Map,this.b=new Map,this.c=new Map}async installAutoReply(e,i){this.a.set(e,i);for(const s of this.c.keys()){const r=this.b.get(s);if(!r){this.d.error("Could not find terminal process to install auto reply");continue}this.f(s,r,e,i)}}async uninstallAllAutoReplies(){for(const e of this.a.keys())for(const i of this.c.values())i.get(e)?.dispose(),i.delete(e)}handleProcessReady(e,i){this.b.set(e,i),this.c.set(e,new Map);for(const[s,r]of this.a.entries())this.f(e,i,s,r)}handleProcessDispose(e){const i=this.c.get(e);if(i){for(const s of i.values())s.dispose();i.clear()}}handleProcessInput(e,i){const s=this.c.get(e);if(s)for(const r of s.values())r.handleInput()}handleProcessResize(e,i,s){const r=this.c.get(e);if(r)for(const n of r.values())n.handleResize()}f(e,i,s,r){const n=this.c.get(e);n&&(n.get(s)?.dispose(),n.set(s,new wu(i,s,r,this.d)))}};Ts=__decorate([__param(0,Jt)],Ts);var{Terminal:yu}=vu;function _(t,e,i){if(typeof i.value!="function")throw new Error("not supported");const s="value",r=i.value;i[s]=async function(...n){this.traceRpcArgs.logService.getLevel()===k.Trace&&this.traceRpcArgs.logService.trace(`[RPC Request] PtyService#${r.name}(${n.map(a=>JSON.stringify(a)).join(", ")})`),this.traceRpcArgs.simulatedLatency&&await Ft(this.traceRpcArgs.simulatedLatency);let o;try{o=await r.apply(this,n)}catch(a){throw this.traceRpcArgs.logService.error(`[RPC Response] PtyService#${r.name}`,a),a}return this.traceRpcArgs.logService.getLevel()===k.Trace&&this.traceRpcArgs.logService.trace(`[RPC Response] PtyService#${r.name}`,o),o}}var Bs,Fs,I=class extends V{async installAutoReply(t,e){await this.j.installAutoReply(t,e)}async uninstallAllAutoReplies(){await this.j.uninstallAllAutoReplies()}I(t,e){return e(i=>{this.J.getLevel()===k.Trace&&this.J.trace(`[RPC Event] PtyService#${t}.fire(${JSON.stringify(i)})`)}),e}get traceRpcArgs(){return{logService:this.J,simulatedLatency:this.N}}constructor(t,e,i,s){super(),this.J=t,this.L=e,this.M=i,this.N=s,this.a=new Map,this.b=new Map,this.g=new Map,this.u=0,this.w=this.B(new x),this.onHeartbeat=this.I("_onHeartbeat",this.w.event),this.y=this.B(new x),this.onProcessData=this.I("_onProcessData",this.y.event),this.z=this.B(new x),this.onProcessReplay=this.I("_onProcessReplay",this.z.event),this.C=this.B(new x),this.onProcessReady=this.I("_onProcessReady",this.C.event),this.D=this.B(new x),this.onProcessExit=this.I("_onProcessExit",this.D.event),this.F=this.B(new x),this.onProcessOrphanQuestion=this.I("_onProcessOrphanQuestion",this.F.event),this.G=this.B(new x),this.onDidRequestDetach=this.I("_onDidRequestDetach",this.G.event),this.H=this.B(new x),this.onDidChangeProperty=this.I("_onDidChangeProperty",this.H.event),this.B(wt(()=>{for(const r of this.a.values())r.shutdown(!0);this.a.clear()})),this.f=this.B(new Ms(void 0,this.J)),this.f.onCreateRequest(this.G.fire,this.G),this.j=new Ts(this.J),this.n=[this.j]}async refreshIgnoreProcessNames(t){Is.length=0,Is.push(...t)}async requestDetachInstance(t,e){return this.f.createRequest({workspaceId:t,instanceId:e})}async acceptDetachInstanceReply(t,e){let i;const s=this.a.get(e);s&&(i=await this.U(e,s)),this.f.acceptReply(t,i)}async freePortKillProcess(t){const i=(await new Promise((s,r)=>{xl(R?`netstat -ano | findstr "${t}"`:`lsof -nP -iTCP -sTCP:LISTEN | grep ${t}`,{},(n,o)=>{if(n)return r("Problem occurred when listing active processes");s(o)})})).split(/\r?\n/).filter(s=>!!s.trim());if(i.length>=1){const s=/\s+(\d+)(?:\s+|$)/,r=i[0].match(s)?.[1];if(r)try{process.kill(Number.parseInt(r))}catch{}else throw new Error(`Processes for port ${t} were not found`);return{port:t,processId:r}}throw new Error(`Could not kill process with port ${t}`)}async serializeTerminalState(t){const e=[];for(const[s,r]of this.a.entries())r.hasWrittenData&&t.indexOf(s)!==-1&&e.push(cs.withAsyncBody(async n=>{n({id:s,shellLaunchConfig:r.shellLaunchConfig,processDetails:await this.U(s,r),processLaunchConfig:r.processLaunchOptions,unicodeVersion:r.unicodeVersion,replayEvent:await r.serializeNormalBuffer(),timestamp:Date.now()})}));const i={version:1,state:await Promise.all(e)};return JSON.stringify(i)}async reviveTerminalProcesses(t,e,i){const s=[];for(const r of e)s.push(this.O(t,r));await Promise.all(s)}async O(t,e){const i=y(2328,null);let s="";if(R){const o=e.replayEvent.events.length>0?e.replayEvent.events.at(-1):void 0;o&&(s+=`\r
`.repeat(o.rows-1)+"\x1B[H")}const r=await this.createProcess({...e.shellLaunchConfig,cwd:e.processDetails.cwd,color:e.processDetails.color,icon:e.processDetails.icon,name:e.processDetails.titleSource===ve.Api?e.processDetails.title:void 0,initialText:e.replayEvent.events[0].data+du(i,{loudFormatting:!0})+s},e.processDetails.cwd,e.replayEvent.events[0].cols,e.replayEvent.events[0].rows,e.unicodeVersion,e.processLaunchConfig.env,e.processLaunchConfig.executableEnv,e.processLaunchConfig.options,!0,e.processDetails.workspaceId,e.processDetails.workspaceName,!0,e.replayEvent.events[0].data),n=this.S(t,e.id);this.g.set(n,{newId:r,state:e}),this.J.info(`Revived process, old id ${n} -> new id ${r}`)}async shutdownAll(){this.dispose()}async createProcess(t,e,i,s,r,n,o,a,c,h,u,l,f){if(t.attachPersistentProcess)throw new Error("Attempt to create a process when attach object was provided");const d=++this.u,p=new _s(t,e,i,s,n,o,a,this.J,this.L),g={env:n,executableEnv:o,options:a},w=new bu(d,p,h,u,c,i,s,g,r,this.M,this.J,l&&typeof t.initialText=="string"?t.initialText:void 0,f,t.icon,t.color,t.name,t.fixedDimensions);return p.onProcessExit(v=>{for(const D of this.n)D.handleProcessDispose(d);w.dispose(),this.a.delete(d),this.D.fire({id:d,event:v})}),w.onProcessData(v=>this.y.fire({id:d,event:v})),w.onProcessReplay(v=>this.z.fire({id:d,event:v})),w.onProcessReady(v=>this.C.fire({id:d,event:v})),w.onProcessOrphanQuestion(()=>this.F.fire({id:d})),w.onDidChangeProperty(v=>this.H.fire({id:d,property:v})),w.onPersistentProcessReady(()=>{for(const v of this.n)v.handleProcessReady(d,p)}),this.a.set(d,w),d}async attachToProcess(t){try{await this.W(t).attach(),this.J.info(`Persistent process reconnection "${t}"`)}catch(e){throw this.J.warn(`Persistent process reconnection "${t}" failed`,e.message),e}}async updateTitle(t,e,i){this.W(t).setTitle(e,i)}async updateIcon(t,e,i,s){this.W(t).setIcon(e,i,s)}async clearBuffer(t){this.W(t).clearBuffer()}async refreshProperty(t,e){return this.W(t).refreshProperty(e)}async updateProperty(t,e,i){return this.W(t).updateProperty(e,i)}async detachFromProcess(t,e){return this.W(t).detach(e)}async reduceConnectionGraceTime(){for(const t of this.a.values())t.reduceGraceTime()}async listProcesses(){const t=Array.from(this.a.entries()).filter(([s,r])=>r.shouldPersistTerminal);this.J.info(`Listing ${t.length} persistent terminals, ${this.a.size} total terminals`);const e=t.map(async([s,r])=>this.U(s,r));return(await Promise.all(e)).filter(s=>s.isOrphan)}async getPerformanceMarks(){return mu()}async start(t){const e=this.a.get(t);return e?e.start():{message:`Could not find pty with id "${t}"`}}async shutdown(t,e){return this.a.get(t)?.shutdown(e)}async input(t,e){const i=this.W(t);if(i){for(const s of this.n)s.handleProcessInput(t,e);i.input(e)}}async sendSignal(t,e){return this.W(t).sendSignal(e)}async processBinary(t,e){return this.W(t).writeBinary(e)}async resize(t,e,i){const s=this.W(t);if(s){for(const r of this.n)r.handleProcessResize(t,e,i);s.resize(e,i)}}async getInitialCwd(t){return this.W(t).getInitialCwd()}async getCwd(t){return this.W(t).getCwd()}async acknowledgeDataEvent(t,e){return this.W(t).acknowledgeDataEvent(e)}async setUnicodeVersion(t,e){return this.W(t).setUnicodeVersion(e)}async getLatency(){return[]}async orphanQuestionReply(t){return this.W(t).orphanQuestionReply()}async getDefaultSystemShell(t=pr){return _l(t,process.env)}async getEnvironment(){return{...process.env}}async getWslPath(t,e){if(e==="win-to-unix"){if(!R)return t;if(te()<17063)return t.replace(/\\/g,"/");const i=this.P();return i?new Promise(s=>{u1(i,["-e","wslpath",t],{},(n,o,a)=>{s(n?t:Bl(o.trim(),"bash"))}).stdin.end()}):t}if(e==="unix-to-win"&&R){if(te()<17063)return t;const i=this.P();return i?new Promise(s=>{u1(i,["-e","wslpath","-w",t],{},(n,o,a)=>{s(n?t:o.trim())}).stdin.end()}):t}return t}P(){const t=te()>=16299,e=process.env.hasOwnProperty("PROCESSOR_ARCHITEW6432"),i=process.env.SystemRoot;if(i)return O(i,e?"Sysnative":"System32",t?"wsl.exe":"bash.exe")}async getRevivedPtyNewId(t,e){try{return this.g.get(this.S(t,e))?.newId}catch(i){this.J.warn(`Couldn't find terminal ID ${t}-${e}`,i.message)}}async setTerminalLayoutInfo(t){this.b.set(t.workspaceId,t)}async getTerminalLayoutInfo(t){Ye("code/willGetTerminalLayoutInfo");const e=this.b.get(t.workspaceId);if(e){const i=new Set,r=(await Promise.all(e.tabs.map(async n=>this.Q(t.workspaceId,n,i)))).filter(n=>n.terminals.length>0);return Ye("code/didGetTerminalLayoutInfo"),{tabs:r}}Ye("code/didGetTerminalLayoutInfo")}async Q(t,e,i){const r=(await Promise.all(e.terminals.map(n=>this.R(t,n,i)))).filter(n=>n.terminal!==null);return{isActive:e.isActive,activePersistentProcessId:e.activePersistentProcessId,terminals:r}}async R(t,e,i){try{const s=this.S(t,e.terminal),r=this.g.get(s)?.newId;this.J.info(`Expanding terminal instance, old id ${s} -> new id ${r}`),this.g.delete(s);const n=r??e.terminal;if(i.has(n))throw new Error(`Terminal ${n} has already been expanded`);i.add(n);const o=this.W(n);return{terminal:{...o&&await this.U(e.terminal,o,r!==void 0),id:n},relativeSize:e.relativeSize}}catch(s){return this.J.warn("Couldn't get layout info, a terminal was probably disconnected",s.message),this.J.debug("Reattach to wrong terminal debug info - layout info by id",e),this.J.debug("Reattach to wrong terminal debug info - _revivePtyIdMap",Array.from(this.g.values())),this.J.debug("Reattach to wrong terminal debug info - _ptys ids",Array.from(this.a.keys())),{terminal:null,relativeSize:e.relativeSize}}}S(t,e){return`${t}-${e}`}async U(t,e,i=!1){Ye(`code/willBuildProcessDetails/${t}`);const[s,r]=await Promise.all([e.getCwd(),i?!0:e.isOrphaned()]),n={id:t,title:e.title,titleSource:e.titleSource,pid:e.pid,workspaceId:e.workspaceId,workspaceName:e.workspaceName,cwd:s,isOrphan:r,icon:e.icon,color:e.color,fixedDimensions:e.fixedDimensions,environmentVariableCollections:e.processLaunchOptions.options.environmentVariableCollections,reconnectionProperties:e.shellLaunchConfig.reconnectionProperties,waitOnExit:e.shellLaunchConfig.waitOnExit,hideFromUser:e.shellLaunchConfig.hideFromUser,isFeatureTerminal:e.shellLaunchConfig.isFeatureTerminal,type:e.shellLaunchConfig.type,hasChildProcesses:e.hasChildProcesses,shellIntegrationNonce:e.processLaunchOptions.options.shellIntegration.nonce,tabActions:e.shellLaunchConfig.tabActions};return Ye(`code/didBuildProcessDetails/${t}`),n}W(t){const e=this.a.get(t);if(!e)throw new re(`Could not find pty ${t} on pty host`);return e}};__decorate([_],I.prototype,"installAutoReply",null),__decorate([_],I.prototype,"uninstallAllAutoReplies",null),__decorate([W],I.prototype,"traceRpcArgs",null),__decorate([_],I.prototype,"refreshIgnoreProcessNames",null),__decorate([_],I.prototype,"requestDetachInstance",null),__decorate([_],I.prototype,"acceptDetachInstanceReply",null),__decorate([_],I.prototype,"freePortKillProcess",null),__decorate([_],I.prototype,"serializeTerminalState",null),__decorate([_],I.prototype,"reviveTerminalProcesses",null),__decorate([_],I.prototype,"shutdownAll",null),__decorate([_],I.prototype,"createProcess",null),__decorate([_],I.prototype,"attachToProcess",null),__decorate([_],I.prototype,"updateTitle",null),__decorate([_],I.prototype,"updateIcon",null),__decorate([_],I.prototype,"clearBuffer",null),__decorate([_],I.prototype,"refreshProperty",null),__decorate([_],I.prototype,"updateProperty",null),__decorate([_],I.prototype,"detachFromProcess",null),__decorate([_],I.prototype,"reduceConnectionGraceTime",null),__decorate([_],I.prototype,"listProcesses",null),__decorate([_],I.prototype,"getPerformanceMarks",null),__decorate([_],I.prototype,"start",null),__decorate([_],I.prototype,"shutdown",null),__decorate([_],I.prototype,"input",null),__decorate([_],I.prototype,"sendSignal",null),__decorate([_],I.prototype,"processBinary",null),__decorate([_],I.prototype,"resize",null),__decorate([_],I.prototype,"getInitialCwd",null),__decorate([_],I.prototype,"getCwd",null),__decorate([_],I.prototype,"acknowledgeDataEvent",null),__decorate([_],I.prototype,"setUnicodeVersion",null),__decorate([_],I.prototype,"getLatency",null),__decorate([_],I.prototype,"orphanQuestionReply",null),__decorate([_],I.prototype,"getDefaultSystemShell",null),__decorate([_],I.prototype,"getEnvironment",null),__decorate([_],I.prototype,"getWslPath",null),__decorate([_],I.prototype,"getRevivedPtyNewId",null),__decorate([_],I.prototype,"setTerminalLayoutInfo",null),__decorate([_],I.prototype,"getTerminalLayoutInfo",null);var V1;(function(t){t.None="None",t.ReplayOnly="ReplayOnly",t.Session="Session"})(V1||(V1={}));var bu=class extends V{get pid(){return this.J}get shellLaunchConfig(){return this.U.shellLaunchConfig}get hasWrittenData(){return this.g.value!=="None"}get title(){return this.M||this.U.currentTitle}get titleSource(){return this.N}get icon(){return this.X}get color(){return this.Y}get fixedDimensions(){return this.Q}get hasChildProcesses(){return this.U.hasChildProcesses}setTitle(t,e){e===ve.Api&&(this.g.setValue("Session","setTitle"),this.O.freeRawReviveBuffer()),this.M=t,this.N=e}setIcon(t,e,i){(!this.X||"id"in e&&"id"in this.X&&e.id!==this.X.id||!this.color||i!==this.Y)&&(this.O.freeRawReviveBuffer(),t&&this.g.setValue("Session","setIcon")),this.X=e,this.Y=i}R(t){this.Q=t}constructor(t,e,i,s,r,n,o,a,c,h,u,l,f,d,p,g,w){super(),this.S=t,this.U=e,this.workspaceId=i,this.workspaceName=s,this.shouldPersistTerminal=r,this.processLaunchOptions=a,this.unicodeVersion=c,this.W=u,this.X=d,this.Y=p,this.b=new Map,this.f=!1,this.u=new zr,this.z=this.B(new x),this.onProcessReplay=this.z.event,this.C=this.B(new x),this.onProcessReady=this.C.event,this.D=this.B(new x),this.onPersistentProcessReady=this.D.event,this.F=this.B(new x),this.onProcessData=this.F.event,this.G=this.B(new x),this.onProcessOrphanQuestion=this.G.event,this.H=this.B(new x),this.onDidChangeProperty=this.H.event,this.I=!1,this.J=-1,this.L="",this.N=ve.Process,this.g=new Cu(`Persistent process "${this.S}" interaction state`,"None",this.W),this.P=l!==void 0,this.O=new Eu(n,o,h.scrollback,c,l,a.options.shellIntegration.nonce,r?f:void 0,this.W),g&&this.setTitle(g,ve.Api),this.Q=w,this.j=null,this.n=0,this.w=this.B(new Hr(()=>{this.W.info(`Persistent process "${this.S}": The reconnection grace time of ${X1(h.graceTime)} has expired, shutting down pid "${this.J}"`),this.shutdown(!0)},h.graceTime)),this.y=this.B(new Hr(()=>{this.W.info(`Persistent process "${this.S}": The short reconnection grace time of ${X1(h.shortGraceTime)} has expired, shutting down pid ${this.J}`),this.shutdown(!0)},h.shortGraceTime)),this.B(this.U.onProcessExit(()=>this.a.stopBuffering(this.S))),this.B(this.U.onProcessReady(v=>{this.J=v.pid,this.L=v.cwd,this.C.fire(v)})),this.B(this.U.onDidChangeProperty(v=>{this.H.fire(v)})),this.a=new Tl((v,D)=>this.F.fire(D)),this.B(this.a.startBuffering(this.S,this.U.onProcessData)),this.B(this.onProcessData(v=>this.O.handleData(v)))}async attach(){!this.w.isScheduled()&&!this.y.isScheduled()&&this.W.warn(`Persistent process "${this.S}": Process had no disconnect runners but was an orphan`),this.w.cancel(),this.y.cancel()}async detach(t){this.shouldPersistTerminal&&(this.g.value!=="None"||t)?this.w.schedule():this.shutdown(!0)}serializeNormalBuffer(){return this.O.generateReplayEvent(!0,this.g.value!=="Session")}async refreshProperty(t){return this.U.refreshProperty(t)}async updateProperty(t,e){if(t==="fixedDimensions")return this.R(e)}async start(){if(!this.f){const t=await this.U.start();return t&&"message"in t||(this.f=!0,this.P?this.triggerReplay():this.D.fire()),t}this.C.fire({pid:this.J,cwd:this.L,windowsPty:this.U.getWindowsPty()}),this.H.fire({type:"title",value:this.U.currentTitle}),this.H.fire({type:"shellType",value:this.U.shellType}),this.triggerReplay()}shutdown(t){return this.U.shutdown(t)}input(t){if(this.g.setValue("Session","input"),this.O.freeRawReviveBuffer(),!this.I)return this.U.input(t)}sendSignal(t){if(!this.I)return this.U.sendSignal(t)}writeBinary(t){return this.U.processBinary(t)}resize(t,e){if(!this.I)return this.O.handleResize(t,e),this.a.flushBuffer(this.S),this.U.resize(t,e)}async clearBuffer(){this.O.clearBuffer(),this.U.clearBuffer()}setUnicodeVersion(t){this.unicodeVersion=t,this.O.setUnicodeVersion?.(t)}acknowledgeDataEvent(t){if(!this.I)return this.U.acknowledgeDataEvent(t)}getInitialCwd(){return this.U.getInitialCwd()}getCwd(){return this.U.getCwd()}async triggerReplay(){this.g.value==="None"&&this.g.setValue("ReplayOnly","triggerReplay");const t=await this.O.generateReplayEvent();let e=0;for(const i of t.events)e+=i.data.length;this.W.info(`Persistent process "${this.S}": Replaying ${e} chars and ${t.events.length} size events`),this.z.fire(t),this.U.clearUnacknowledgedChars(),this.D.fire()}sendCommandResult(t,e,i){this.b.get(t)&&this.b.delete(t)}orphanQuestionReply(){if(this.n=Date.now(),this.j){const t=this.j;this.j=null,t.open()}}reduceGraceTime(){this.y.isScheduled()||this.w.isScheduled()&&this.y.schedule()}async isOrphaned(){return await this.u.queue(async()=>this.Z())}async Z(){return this.w.isScheduled()||this.y.isScheduled()?!0:(this.j||(this.j=new jh(4e3),this.n=0,this.G.fire()),await this.j.wait(),Date.now()-this.n>500)}},Cu=class{get value(){return this.b}setValue(t,e){this.b!==t&&(this.b=t,this.f(e))}constructor(t,e,i){this.a=t,this.b=e,this.d=i,this.f("initialized")}f(t){this.d.debug(`MutationLogger "${this.a}" set to "${this.b}", reason: ${t}`)}},Eu=class{constructor(t,e,i,s,r,n,o,a){this.f=o,this.a=new yu({cols:t,rows:e,scrollback:i,allowProposedApi:!0}),r&&this.a.writeln(r),this.setUnicodeVersion(s),this.b=new uu(n,!0,void 0,void 0,a),this.a.loadAddon(this.b)}freeRawReviveBuffer(){this.f=void 0}handleData(t){this.a.write(t)}handleResize(t,e){this.a.resize(t,e)}clearBuffer(){this.a.clear()}async generateReplayEvent(t,e){const i=new(await this._getSerializeConstructor());this.a.loadAddon(i);const s={scrollback:this.a.options.scrollback};t&&(s.excludeAltBuffer=!0,s.excludeModes=!0);let r;return e&&this.f?r=this.f:r=i.serialize(s),{events:[{cols:this.a.cols,rows:this.a.rows,data:r}],commands:this.b.serialize()}}async setUnicodeVersion(t){this.a.unicode.activeVersion!==t&&(t==="11"?(this.d=new(await this._getUnicode11Constructor()),this.a.loadAddon(this.d)):(this.d?.dispose(),this.d=void 0),this.a.unicode.activeVersion=t)}async _getUnicode11Constructor(){return Fs||(Fs=(await import("@xterm/addon-unicode11")).Unicode11Addon),Fs}async _getSerializeConstructor(){return Bs||(Bs=(await import("@xterm/addon-serialize")).SerializeAddon),Bs}};function X1(t){let e=0,i=0,s=0;t>=1e3&&(s=Math.floor(t/1e3),t-=s*1e3),s>=60&&(i=Math.floor(s/60),s-=i*60),i>=60&&(e=Math.floor(i/60),i-=e*60);const r=e?`${e}h`:"",n=i?`${i}m`:"",o=s?`${s}s`:"",a=t?`${t}ms`:"";return`${r}${n}${o}${a}`}xu();async function xu(){const t=parseInt(process.env.VSCODE_STARTUP_DELAY??"0"),e=parseInt(process.env.VSCODE_LATENCY??"0"),i={graceTime:parseInt(process.env.VSCODE_RECONNECT_GRACE_TIME||"0"),shortGraceTime:parseInt(process.env.VSCODE_RECONNECT_SHORT_GRACE_TIME||"0"),scrollback:parseInt(process.env.VSCODE_RECONNECT_SCROLLBACK||"100")};delete process.env.VSCODE_RECONNECT_GRACE_TIME,delete process.env.VSCODE_RECONNECT_SHORT_GRACE_TIME,delete process.env.VSCODE_RECONNECT_SCROLLBACK,delete process.env.VSCODE_LATENCY,delete process.env.VSCODE_STARTUP_DELAY,t&&await Ft(t);const s=gn(process);let r;s?r=new yc:r=new vc(Zt.PtyHost);const n={_serviceBrand:void 0,...vl},o=new Rc(mn(process.argv,Cc),n),a=new ml(rl(o),o.logsHome);r.registerChannel(Zt.Logger,new al(a,()=>Ya));const c=a.createLogger("ptyhost",{name:y(2327,null)}),h=new hl(c);t&&h.warn(`Pty Host startup is delayed ${t}ms`),e&&h.warn(`Pty host is simulating ${e}ms latency`);const u=new qt,l=new El;r.registerChannel(Zt.Heartbeat,wi.fromService(l,u));const f=new I(h,n,i,e),d=wi.fromService(f,u);r.registerChannel(Zt.PtyHost,d),s&&r.registerChannel(Zt.PtyHostWindow,d),process.once("exit",()=>{h.trace("Pty host exiting"),h.dispose(),l.dispose(),f.dispose()})}

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6f17636121051a53c88d3e605c491d22af2ba755/core/vs/platform/terminal/node/ptyHostMain.js.map
