"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatProvider = void 0;
const vscode = __importStar(require("vscode"));
class ChatProvider {
    constructor(context, ollamaClient, contextEngine) {
        this.context = context;
        this.ollamaClient = ollamaClient;
        this.contextEngine = contextEngine;
        this._onDidChangeTreeData = new vscode.EventEmitter();
        this.onDidChangeTreeData = this._onDidChangeTreeData.event;
        this.conversationHistory = [];
    }
    getTreeItem(element) {
        return element;
    }
    getChildren(element) {
        if (!element) {
            return Promise.resolve([
                new ChatTreeItem('New Chat', vscode.TreeItemCollapsibleState.None, 'openChat'),
                new ChatTreeItem('Clear History', vscode.TreeItemCollapsibleState.None, 'clearHistory')
            ]);
        }
        return Promise.resolve([]);
    }
    getTreeDataProvider() {
        return this;
    }
    async openChat() {
        if (this.chatPanel) {
            this.chatPanel.reveal(vscode.ViewColumn.Beside);
            return;
        }
        this.chatPanel = vscode.window.createWebviewPanel('ollama-chat', 'Ollama Assistant', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true,
            localResourceRoots: [
                vscode.Uri.joinPath(this.context.extensionUri, 'media'),
                vscode.Uri.joinPath(this.context.extensionUri, 'out')
            ]
        });
        this.chatPanel.webview.html = this.getWebviewContent();
        // Handle messages from webview
        this.chatPanel.webview.onDidReceiveMessage(async (message) => {
            switch (message.type) {
                case 'sendMessage':
                    await this.handleSendMessage(message.text, message.includeContext);
                    break;
                case 'clearHistory':
                    await this.handleClearHistory();
                    break;
                case 'getModels':
                    await this.handleGetModels();
                    break;
                case 'selectModel':
                    await this.handleSelectModel(message.model);
                    break;
            }
        }, undefined, this.context.subscriptions);
        this.chatPanel.onDidDispose(() => {
            this.chatPanel = undefined;
        });
        // Send initial data
        await this.sendInitialData();
    }
    async sendInitialData() {
        if (!this.chatPanel) {
            return;
        }
        // Send conversation history
        this.chatPanel.webview.postMessage({
            type: 'conversationHistory',
            messages: this.conversationHistory
        });
        // Send available models
        try {
            const models = this.ollamaClient.getAvailableModels();
            this.chatPanel.webview.postMessage({
                type: 'availableModels',
                models: models.map(m => m.name)
            });
        }
        catch (error) {
            console.error('Error getting models:', error);
        }
    }
    async handleSendMessage(text, includeContext) {
        if (!this.chatPanel || !text.trim()) {
            return;
        }
        const userMessage = {
            role: 'user',
            content: text
        };
        // Add user message to history
        this.conversationHistory.push(userMessage);
        // Send user message to webview
        this.chatPanel.webview.postMessage({
            type: 'userMessage',
            message: userMessage
        });
        try {
            // Get context if requested
            let contextPrompt = '';
            if (includeContext) {
                const activeEditor = vscode.window.activeTextEditor;
                if (activeEditor) {
                    const context = await this.contextEngine.gatherContext(activeEditor.document, activeEditor.selection.active, activeEditor.selection);
                    contextPrompt = this.buildContextPrompt(context);
                }
            }
            // Prepare chat request
            const messages = [...this.conversationHistory];
            // Add context to the last user message if available
            if (contextPrompt) {
                messages[messages.length - 1].content = `${contextPrompt}\n\nUser question: ${text}`;
            }
            const model = vscode.workspace.getConfiguration('ollama-assistant').get('chatModel') ||
                vscode.workspace.getConfiguration('ollama-assistant').get('defaultModel') || '';
            if (!model) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: 'No model selected. Please select a model first.'
                });
                return;
            }
            const request = {
                model,
                messages,
                stream: true,
                options: vscode.workspace.getConfiguration('ollama-assistant').get('chatOptions') || {
                    temperature: 0.7,
                    top_p: 0.9,
                    num_ctx: 4096
                }
            };
            // Start streaming response
            let assistantMessage = '';
            this.chatPanel.webview.postMessage({
                type: 'assistantMessageStart'
            });
            for await (const chunk of this.ollamaClient.chatStream(request)) {
                if (chunk.message?.content) {
                    assistantMessage += chunk.message.content;
                    this.chatPanel.webview.postMessage({
                        type: 'assistantMessageChunk',
                        content: chunk.message.content
                    });
                }
                if (chunk.done) {
                    break;
                }
            }
            // Add assistant message to history
            const finalAssistantMessage = {
                role: 'assistant',
                content: assistantMessage
            };
            this.conversationHistory.push(finalAssistantMessage);
            this.chatPanel.webview.postMessage({
                type: 'assistantMessageEnd',
                message: finalAssistantMessage
            });
        }
        catch (error) {
            console.error('Error sending message:', error);
            this.chatPanel.webview.postMessage({
                type: 'error',
                message: `Error: ${error}`
            });
        }
    }
    buildContextPrompt(context) {
        let prompt = 'Current code context:\n\n';
        if (context.currentFile) {
            prompt += `File: ${context.currentFile.path}\n`;
            prompt += `Language: ${context.currentFile.language}\n\n`;
            if (context.currentFile.selection) {
                prompt += 'Selected code:\n```' + context.currentFile.language + '\n';
                prompt += context.currentFile.selection.text + '\n```\n\n';
            }
            else {
                // Include relevant portion of the file
                const lines = context.currentFile.content.split('\n');
                const cursorLine = context.currentFile.cursorPosition?.line || 0;
                const start = Math.max(0, cursorLine - 20);
                const end = Math.min(lines.length, cursorLine + 20);
                const relevantLines = lines.slice(start, end);
                prompt += 'Code around cursor:\n```' + context.currentFile.language + '\n';
                prompt += relevantLines.join('\n') + '\n```\n\n';
            }
        }
        if (context.relatedFiles && context.relatedFiles.length > 0) {
            prompt += 'Related files:\n';
            context.relatedFiles.slice(0, 2).forEach((file) => {
                prompt += `- ${file.path}\n`;
            });
            prompt += '\n';
        }
        return prompt;
    }
    async handleClearHistory() {
        this.conversationHistory = [];
        if (this.chatPanel) {
            this.chatPanel.webview.postMessage({
                type: 'historyCleared'
            });
        }
    }
    async clearHistory() {
        await this.handleClearHistory();
    }
    async handleGetModels() {
        try {
            await this.ollamaClient.refreshModels();
            const models = this.ollamaClient.getAvailableModels();
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'availableModels',
                    models: models.map(m => m.name)
                });
            }
        }
        catch (error) {
            console.error('Error getting models:', error);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'error',
                    message: 'Failed to load models'
                });
            }
        }
    }
    async handleSelectModel(model) {
        try {
            await vscode.workspace.getConfiguration('ollama-assistant').update('chatModel', model, vscode.ConfigurationTarget.Global);
            if (this.chatPanel) {
                this.chatPanel.webview.postMessage({
                    type: 'modelSelected',
                    model
                });
            }
        }
        catch (error) {
            console.error('Error selecting model:', error);
        }
    }
    getWebviewContent() {
        const htmlContent = this.getHtmlTemplate();
        const jsContent = this.getJavaScriptCode();
        return htmlContent.replace('{{JAVASCRIPT_CODE}}', jsContent);
    }
    getHtmlTemplate() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Assistant</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }

        .header-title {
            font-size: 1.2em;
            font-weight: bold;
        }

        .model-selector {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 10px;
        }

        .model-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        select {
            background-color: var(--vscode-dropdown-background);
            color: var(--vscode-dropdown-foreground);
            border: 1px solid var(--vscode-dropdown-border);
            padding: 8px;
            border-radius: 3px;
            min-width: 200px;
            flex: 1;
            max-width: 300px;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 5px;
        }

        .user-message {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: 20%;
        }

        .assistant-message {
            background-color: var(--vscode-editor-selectionBackground);
            margin-right: 20%;
        }

        .input-container {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 10px;
            border-top: 1px solid var(--vscode-panel-border);
            background-color: var(--vscode-editor-background);
        }

        .input-wrapper {
            display: flex;
            flex-direction: column;
            width: 100%;
        }

        .input-row {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        textarea {
            width: 100%;
            min-height: 80px;
            max-height: 150px;
            padding: 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 5px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-family: inherit;
            font-size: inherit;
            resize: vertical;
            box-sizing: border-box;
            line-height: 1.4;
        }

        .input-options {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 5px;
        }

        button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            white-space: nowrap;
            font-size: 13px;
            min-height: 28px;
        }

        .send-button {
            align-self: flex-end;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            min-width: 80px;
        }

        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .error {
            color: var(--vscode-errorForeground);
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }

        .typing-indicator {
            font-style: italic;
            opacity: 0.7;
        }

        pre {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            white-space: pre-wrap;
        }

        code {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 2px 4px;
            border-radius: 3px;
        }

        /* Responsive design */
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }

            .header {
                gap: 10px;
                margin-bottom: 15px;
                padding-bottom: 10px;
            }

            .model-selector {
                flex-direction: column;
                align-items: stretch;
                gap: 8px;
            }

            select {
                min-width: unset;
                max-width: unset;
            }

            .model-controls {
                justify-content: space-between;
            }

            .user-message {
                margin-left: 2%;
            }

            .assistant-message {
                margin-right: 2%;
            }

            .send-button {
                align-self: stretch;
                margin-top: 5px;
            }
        }

        @media (max-width: 400px) {
            .model-controls {
                flex-direction: column;
            }

            button {
                padding: 10px;
                font-size: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-title">Ollama Assistant</div>
        <div class="model-selector">
            <label for="model-select">Model:</label>
            <select id="model-select">
                <option value="">Select a model...</option>
            </select>
            <div class="model-controls">
                <button onclick="refreshModels()">Refresh</button>
                <button onclick="clearHistory()">Clear</button>
            </div>
        </div>
    </div>

    <div class="chat-container">
        <div id="messages" class="messages"></div>

        <div class="input-container">
            <div class="input-wrapper">
                <div class="input-options">
                    <label>
                        <input type="checkbox" id="include-context" checked>
                        Include current file context
                    </label>
                </div>
                <div class="input-row">
                    <textarea id="message-input" placeholder="Ask me anything about your code..."></textarea>
                    <button onclick="sendMessage()" id="send-button" class="send-button">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        {{JAVASCRIPT_CODE}}
    </script>
</body>
</html>`;
    }
    getJavaScriptCode() {
        return `
        const vscode = acquireVsCodeApi();
        let currentAssistantMessage = null;

        // Handle messages from extension
        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.type) {
                case 'conversationHistory':
                    displayConversationHistory(message.messages);
                    break;
                case 'availableModels':
                    updateModelSelector(message.models);
                    break;
                case 'userMessage':
                    displayUserMessage(message.message);
                    break;
                case 'assistantMessageStart':
                    startAssistantMessage();
                    break;
                case 'assistantMessageChunk':
                    appendToAssistantMessage(message.content);
                    break;
                case 'assistantMessageEnd':
                    endAssistantMessage();
                    break;
                case 'error':
                    displayError(message.message);
                    break;
                case 'historyCleared':
                    clearMessages();
                    break;
                case 'modelSelected':
                    document.getElementById('model-select').value = message.model;
                    break;
            }
        });

        function sendMessage() {
            const input = document.getElementById('message-input');
            const includeContext = document.getElementById('include-context').checked;
            const text = input.value.trim();

            if (!text) return;

            vscode.postMessage({
                type: 'sendMessage',
                text: text,
                includeContext: includeContext
            });

            input.value = '';
            document.getElementById('send-button').disabled = true;
        }

        function clearHistory() {
            vscode.postMessage({ type: 'clearHistory' });
        }

        function refreshModels() {
            vscode.postMessage({ type: 'getModels' });
        }

        function displayConversationHistory(messages) {
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '';

            messages.forEach(message => {
                if (message.role === 'user') {
                    displayUserMessage(message);
                } else if (message.role === 'assistant') {
                    displayAssistantMessage(message);
                }
            });
        }

        function displayUserMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML = formatMessage(message.content);
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function displayAssistantMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant-message';
            messageDiv.innerHTML = formatMessage(message.content);
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function startAssistantMessage() {
            const messagesDiv = document.getElementById('messages');
            currentAssistantMessage = document.createElement('div');
            currentAssistantMessage.className = 'message assistant-message';
            currentAssistantMessage.innerHTML = '<div class="typing-indicator">Thinking...</div>';
            messagesDiv.appendChild(currentAssistantMessage);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function appendToAssistantMessage(content) {
            if (currentAssistantMessage) {
                const currentContent = currentAssistantMessage.textContent || '';
                const newContent = currentContent.replace('Thinking...', '') + content;
                currentAssistantMessage.innerHTML = formatMessage(newContent);

                const messagesDiv = document.getElementById('messages');
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
            }
        }

        function endAssistantMessage() {
            currentAssistantMessage = null;
            document.getElementById('send-button').disabled = false;
        }

        function displayError(message) {
            const messagesDiv = document.getElementById('messages');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;
            messagesDiv.appendChild(errorDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            document.getElementById('send-button').disabled = false;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function updateModelSelector(models) {
            const select = document.getElementById('model-select');
            const currentValue = select.value;

            select.innerHTML = '<option value="">Select a model...</option>';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                select.appendChild(option);
            });

            if (models.includes(currentValue)) {
                select.value = currentValue;
            }
        }

        function formatMessage(content) {
            // Simple markdown-like formatting
            const codeBlockRegex = /\`\`\`([^\`]+)\`\`\`/g;
            const inlineCodeRegex = /\`([^\`]+)\`/g;

            return content
                .replace(codeBlockRegex, '<pre><code>$1</code></pre>')
                .replace(inlineCodeRegex, '<code>$1</code>')
                .replace(/\\n/g, '<br>');
        }

        // Handle Enter key in textarea
        document.getElementById('message-input').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        // Handle model selection
        document.getElementById('model-select').addEventListener('change', function(e) {
            if (e.target.value) {
                vscode.postMessage({
                    type: 'selectModel',
                    model: e.target.value
                });
            }
        });

        // Request initial data
        vscode.postMessage({ type: 'getModels' });
        `;
    }
    dispose() {
        if (this.chatPanel) {
            this.chatPanel.dispose();
        }
    }
}
exports.ChatProvider = ChatProvider;
class ChatTreeItem extends vscode.TreeItem {
    constructor(label, collapsibleState, commandId) {
        super(label, collapsibleState);
        this.label = label;
        this.collapsibleState = collapsibleState;
        this.commandId = commandId;
        if (commandId) {
            this.command = {
                command: `ollama-assistant.${commandId}`,
                title: label
            };
        }
    }
}
//# sourceMappingURL=chatProvider.js.map