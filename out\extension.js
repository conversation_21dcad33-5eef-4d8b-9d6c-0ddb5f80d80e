"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.activate = activate;
exports.deactivate = deactivate;
const vscode = __importStar(require("vscode"));
const ollamaClient_1 = require("./services/ollamaClient");
const chatProvider_1 = require("./providers/chatProvider");
const completionProvider_1 = require("./providers/completionProvider");
const contextEngine_1 = require("./services/contextEngine");
const configurationManager_1 = require("./services/configurationManager");
let ollamaClient;
let chatProvider;
let completionProvider;
let contextEngine;
let configManager;
async function activate(context) {
    console.log('Ollama Code Assistant is now active!');
    // Initialize services
    configManager = new configurationManager_1.ConfigurationManager();
    ollamaClient = new ollamaClient_1.OllamaClient(configManager);
    contextEngine = new contextEngine_1.ContextEngine();
    chatProvider = new chatProvider_1.ChatProvider(context, ollamaClient, contextEngine);
    completionProvider = new completionProvider_1.CompletionProvider(ollamaClient, contextEngine, configManager);
    // Register commands
    const openChatCommand = vscode.commands.registerCommand('ollama-assistant.openChat', () => {
        chatProvider.openChat();
    });
    const selectModelCommand = vscode.commands.registerCommand('ollama-assistant.selectModel', async () => {
        await selectModel();
    });
    const refreshModelsCommand = vscode.commands.registerCommand('ollama-assistant.refreshModels', async () => {
        await ollamaClient.refreshModels();
        vscode.window.showInformationMessage('Models refreshed successfully!');
    });
    const clearHistoryCommand = vscode.commands.registerCommand('ollama-assistant.clearHistory', async () => {
        await chatProvider.clearHistory();
        vscode.window.showInformationMessage('Chat history cleared!');
    });
    // Register providers
    const completionDisposable = vscode.languages.registerInlineCompletionItemProvider({ pattern: '**' }, completionProvider);
    // Register tree view
    const treeDataProvider = chatProvider.getTreeDataProvider();
    vscode.window.createTreeView('ollama-assistant.chatView', {
        treeDataProvider,
        showCollapseAll: true
    });
    // Add to subscriptions
    context.subscriptions.push(openChatCommand, selectModelCommand, refreshModelsCommand, clearHistoryCommand, completionDisposable);
    // Initialize connection to Ollama
    try {
        await ollamaClient.initialize();
        vscode.window.showInformationMessage('Connected to Ollama server successfully!');
    }
    catch (error) {
        vscode.window.showErrorMessage(`Failed to connect to Ollama: ${error}`);
    }
}
async function selectModel() {
    try {
        const models = await ollamaClient.getAvailableModels();
        if (models.length === 0) {
            vscode.window.showWarningMessage('No models available. Please ensure Ollama is running and models are installed.');
            return;
        }
        const modelNames = models.map(model => model.name);
        const selected = await vscode.window.showQuickPick(modelNames, {
            placeHolder: 'Select an Ollama model'
        });
        if (selected) {
            await configManager.setDefaultModel(selected);
            vscode.window.showInformationMessage(`Selected model: ${selected}`);
        }
    }
    catch (error) {
        vscode.window.showErrorMessage(`Failed to load models: ${error}`);
    }
}
function deactivate() {
    // Cleanup resources
    if (ollamaClient) {
        ollamaClient.dispose();
    }
    if (chatProvider) {
        chatProvider.dispose();
    }
}
//# sourceMappingURL=extension.js.map