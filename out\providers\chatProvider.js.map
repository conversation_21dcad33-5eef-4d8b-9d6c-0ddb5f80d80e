{"version": 3, "file": "chatProvider.js", "sourceRoot": "", "sources": ["../../src/providers/chatProvider.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AAMjC,MAAa,YAAY;IAOrB,YACY,OAAgC,EAChC,YAA0B,EAC1B,aAA4B;QAF5B,YAAO,GAAP,OAAO,CAAyB;QAChC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,kBAAa,GAAb,aAAa,CAAe;QAThC,yBAAoB,GAAgE,IAAI,MAAM,CAAC,YAAY,EAA0C,CAAC;QACrJ,wBAAmB,GAAyD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC;QAG7G,wBAAmB,GAAkB,EAAE,CAAC;IAM7C,CAAC;IAEJ,WAAW,CAAC,OAAqB;QAC7B,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,OAAsB;QAC9B,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,OAAO,CAAC,OAAO,CAAC;gBACnB,IAAI,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,UAAU,CAAC;gBAC9E,IAAI,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,wBAAwB,CAAC,IAAI,EAAE,cAAc,CAAC;aAC1F,CAAC,CAAC;QACP,CAAC;QACD,OAAO,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IAC/B,CAAC;IAED,mBAAmB;QACf,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,QAAQ;QACV,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YAChD,OAAO;QACX,CAAC;QAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAC7C,aAAa,EACb,kBAAkB,EAClB,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;YAC7B,kBAAkB,EAAE;gBAChB,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC;gBACvD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC;aACxD;SACJ,CACJ,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEvD,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,mBAAmB,CACtC,KAAK,EAAE,OAAO,EAAE,EAAE;YACd,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACnB,KAAK,aAAa;oBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,cAAc,CAAC,CAAC;oBACnE,MAAM;gBACV,KAAK,cAAc;oBACf,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;oBAChC,MAAM;gBACV,KAAK,WAAW;oBACZ,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;oBAC7B,MAAM;gBACV,KAAK,aAAa;oBACd,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAC5C,MAAM;YACd,CAAC;QACL,CAAC,EACD,SAAS,EACT,IAAI,CAAC,OAAO,CAAC,aAAa,CAC7B,CAAC;QAEF,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE;YAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;IACjC,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YAClB,OAAO;QACX,CAAC;QAED,4BAA4B;QAC5B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;YAC/B,IAAI,EAAE,qBAAqB;YAC3B,QAAQ,EAAE,IAAI,CAAC,mBAAmB;SACrC,CAAC,CAAC;QAEH,wBAAwB;QACxB,IAAI,CAAC;YACD,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;YACtD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/B,IAAI,EAAE,iBAAiB;gBACvB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;aAClC,CAAC,CAAC;QACP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,IAAY,EAAE,cAAuB;QACjE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC;YAClC,OAAO;QACX,CAAC;QAED,MAAM,WAAW,GAAgB;YAC7B,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,IAAI;SAChB,CAAC;QAEF,8BAA8B;QAC9B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE3C,+BAA+B;QAC/B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;YAC/B,IAAI,EAAE,aAAa;YACnB,OAAO,EAAE,WAAW;SACvB,CAAC,CAAC;QAEH,IAAI,CAAC;YACD,2BAA2B;YAC3B,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,cAAc,EAAE,CAAC;gBACjB,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;gBACpD,IAAI,YAAY,EAAE,CAAC;oBACf,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,aAAa,CAClD,YAAY,CAAC,QAAQ,EACrB,YAAY,CAAC,SAAS,CAAC,MAAM,EAC7B,YAAY,CAAC,SAAS,CACzB,CAAC;oBACF,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;gBACrD,CAAC;YACL,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAkB,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAE9D,oDAAoD;YACpD,IAAI,aAAa,EAAE,CAAC;gBAChB,QAAQ,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,GAAG,GAAG,aAAa,sBAAsB,IAAI,EAAE,CAAC;YACzF,CAAC;YAED,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAS,WAAW,CAAC;gBAC/E,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAS,cAAc,CAAC,IAAI,EAAE,CAAC;YAErG,IAAI,CAAC,KAAK,EAAE,CAAC;gBACT,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC/B,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,iDAAiD;iBAC7D,CAAC,CAAC;gBACH,OAAO;YACX,CAAC;YAED,MAAM,OAAO,GAAgB;gBACzB,KAAK;gBACL,QAAQ;gBACR,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI;oBACjF,WAAW,EAAE,GAAG;oBAChB,KAAK,EAAE,GAAG;oBACV,OAAO,EAAE,IAAI;iBAChB;aACJ,CAAC;YAEF,2BAA2B;YAC3B,IAAI,gBAAgB,GAAG,EAAE,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/B,IAAI,EAAE,uBAAuB;aAChC,CAAC,CAAC;YAEH,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC9D,IAAI,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;oBACzB,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;oBAC1C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;wBAC/B,IAAI,EAAE,uBAAuB;wBAC7B,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,OAAO;qBACjC,CAAC,CAAC;gBACP,CAAC;gBAED,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;oBACb,MAAM;gBACV,CAAC;YACL,CAAC;YAED,mCAAmC;YACnC,MAAM,qBAAqB,GAAgB;gBACvC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,gBAAgB;aAC5B,CAAC;YACF,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YAErD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/B,IAAI,EAAE,qBAAqB;gBAC3B,OAAO,EAAE,qBAAqB;aACjC,CAAC,CAAC;QAEP,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/B,IAAI,EAAE,OAAO;gBACb,OAAO,EAAE,UAAU,KAAK,EAAE;aAC7B,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEO,kBAAkB,CAAC,OAAY;QACnC,IAAI,MAAM,GAAG,2BAA2B,CAAC;QAEzC,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,SAAS,OAAO,CAAC,WAAW,CAAC,IAAI,IAAI,CAAC;YAChD,MAAM,IAAI,aAAa,OAAO,CAAC,WAAW,CAAC,QAAQ,MAAM,CAAC;YAE1D,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,CAAC;gBAChC,MAAM,IAAI,qBAAqB,GAAG,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;gBACtE,MAAM,IAAI,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,GAAG,WAAW,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACJ,uCAAuC;gBACvC,MAAM,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACtD,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,IAAI,CAAC,CAAC;gBACjE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC;gBAC3C,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,UAAU,GAAG,EAAE,CAAC,CAAC;gBACpD,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBAE9C,MAAM,IAAI,0BAA0B,GAAG,OAAO,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;gBAC3E,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC;YACrD,CAAC;QACL,CAAC;QAED,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC1D,MAAM,IAAI,kBAAkB,CAAC;YAC7B,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;gBACnD,MAAM,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;YACjC,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;QACnB,CAAC;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,KAAK,CAAC,kBAAkB;QAC5B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/B,IAAI,EAAE,gBAAgB;aACzB,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAED,KAAK,CAAC,YAAY;QACd,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACpC,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,IAAI,CAAC;YACD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,CAAC;YACxC,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;YACtD,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC/B,IAAI,EAAE,iBAAiB;oBACvB,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;iBAClC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;YAC9C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC/B,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,uBAAuB;iBACnC,CAAC,CAAC;YACP,CAAC;QACL,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,KAAa;QACzC,IAAI,CAAC;YACD,MAAM,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,WAAW,EAAE,KAAK,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;YAC1H,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;gBACjB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC;oBAC/B,IAAI,EAAE,eAAe;oBACrB,KAAK;iBACR,CAAC,CAAC;YACP,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC;IACL,CAAC;IAEO,iBAAiB;QACrB,MAAM,WAAW,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAE3C,OAAO,WAAW,CAAC,OAAO,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;IACjE,CAAC;IAEO,eAAe;QACnB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QA8LP,CAAC;IACL,CAAC;IAEO,iBAAiB;QACrB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SAsLN,CAAC;IACN,CAAC;IAED,OAAO;QACH,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;CACJ;AA7qBD,oCA6qBC;AAED,MAAM,YAAa,SAAQ,MAAM,CAAC,QAAQ;IACtC,YACoB,KAAa,EACb,gBAAiD,EACjD,SAAkB;QAElC,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QAJf,UAAK,GAAL,KAAK,CAAQ;QACb,qBAAgB,GAAhB,gBAAgB,CAAiC;QACjD,cAAS,GAAT,SAAS,CAAS;QAIlC,IAAI,SAAS,EAAE,CAAC;YACZ,IAAI,CAAC,OAAO,GAAG;gBACX,OAAO,EAAE,oBAAoB,SAAS,EAAE;gBACxC,KAAK,EAAE,KAAK;aACf,CAAC;QACN,CAAC;IACL,CAAC;CACJ"}