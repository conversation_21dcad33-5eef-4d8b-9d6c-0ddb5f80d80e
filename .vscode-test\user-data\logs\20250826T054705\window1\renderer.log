2025-08-26 05:47:07.189 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vsliveshare.vsliveshare' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-08-26 05:47:07.189 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.gather' wants API proposal 'notebookCellExecutionState' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-08-26 05:47:07.189 [warning] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.vscode-pylance' wants API proposal 'mcpConfigurationProvider' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-08-26 05:47:07.588 [info] Started local extension host with pid 5032.
2025-08-26 05:47:07.599 [info] Started initializing default profile extensions in extensions installation folder. file:///c%3A/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/extensions
2025-08-26 05:47:07.708 [info] ChatSessionStore: Migrating 0 chat sessions from storage service to file system
2025-08-26 05:47:07.793 [info] Completed initializing default profile extensions in extensions installation folder. file:///c%3A/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/extensions
2025-08-26 05:47:07.838 [info] Loading development extension at c:\Users\<USER>\Documents\AiCodingAssistant
2025-08-26 05:47:07.980 [info] Settings Sync: Account status changed from uninitialized to unavailable
2025-08-26 05:47:10.264 [info] Ollama Code Assistant is now active!
2025-08-26 05:47:10.301 [info] [0m%s%s[0m  
2025-08-26 05:47:10.301 [info] [0m%s%s[0m    Extension Test Suite
2025-08-26 05:47:10.305 [info]   [31m  %d) %s[0m 1 Extension should be present
2025-08-26 05:47:10.308 [info]   [32m  ✔[0m[90m %s[0m Configuration Manager should work
2025-08-26 05:47:10.309 [info]   [32m  ✔[0m[90m %s[0m Ollama Client should initialize
2025-08-26 05:47:10.313 [info]   [32m  ✔[0m[90m %s[0m Commands should be registered
2025-08-26 05:47:10.314 [info] [92m [0m[32m %d passing[0m[90m (%s)[0m 3 24ms
2025-08-26 05:47:10.315 [info] [31m  %d failing[0m 1
2025-08-26 05:47:10.315 [info] [0m  %s) %s:
[0m[31m     %s[0m[90m
%s
[0m 1 Extension Test Suite
       Extension should be present AssertionError [ERR_ASSERTION]: The expression evaluated to a falsy value:

  assert.ok(vscode.extensions.getExtension('ollama-code-assistant'))
   	at Context.<anonymous> (c:\Users\<USER>\Documents\AiCodingAssistant\out\test\suite\extension.test.js:43:16)
  	at process.processImmediate (node:internal/timers:485:21)
2025-08-26 05:47:10.315 [error] Error: 1 tests failed.
	at c:\Users\<USER>\Documents\AiCodingAssistant\out\test\suite\index.js:61:27
	at done (c:\Users\<USER>\Documents\AiCodingAssistant\node_modules\mocha\lib\mocha.js:1028:7)
