/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var we=function(e,t){return we=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(r[n]=i[n])},we(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");we(e,t);function r(){this.constructor=e}e.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}export var __assign=function(){return __assign=Object.assign||function(t){for(var r,i=1,n=arguments.length;i<n;i++){r=arguments[i];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(t[s]=r[s])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r}export function __decorate(e,t,r,i){var n=arguments.length,s=n<3?t:i===null?i=Object.getOwnPropertyDescriptor(t,r):i,o;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(e,t,r,i);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(n<3?o(s):n>3?o(t,r,s):o(t,r))||s);return n>3&&s&&Object.defineProperty(t,r,s),s}export function __param(e,t){return function(r,i){t(r,i,e)}}export function __esDecorate(e,t,r,i,n,s){function o(I){if(I!==void 0&&typeof I!="function")throw new TypeError("Function expected");return I}for(var a=i.kind,l=a==="getter"?"get":a==="setter"?"set":"value",c=!t&&e?i.static?e:e.prototype:null,u=t||(c?Object.getOwnPropertyDescriptor(c,i.name):{}),f,d=!1,p=r.length-1;p>=0;p--){var $={};for(var _ in i)$[_]=_==="access"?{}:i[_];for(var _ in i.access)$.access[_]=i.access[_];$.addInitializer=function(I){if(d)throw new TypeError("Cannot add initializers after decoration has completed");s.push(o(I||null))};var E=(0,r[p])(a==="accessor"?{get:u.get,set:u.set}:u[l],$);if(a==="accessor"){if(E===void 0)continue;if(E===null||typeof E!="object")throw new TypeError("Object expected");(f=o(E.get))&&(u.get=f),(f=o(E.set))&&(u.set=f),(f=o(E.init))&&n.unshift(f)}else(f=o(E))&&(a==="field"?n.unshift(f):u[l]=f)}c&&Object.defineProperty(c,i.name,u),d=!0}export function __runInitializers(e,t,r){for(var i=arguments.length>2,n=0;n<t.length;n++)r=i?t[n].call(e,r):t[n].call(e);return i?r:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,r){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:r?"".concat(r," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,r,i){function n(s){return s instanceof r?s:new r(function(o){o(s)})}return new(r||(r=Promise))(function(s,o){function a(u){try{c(i.next(u))}catch(f){o(f)}}function l(u){try{c(i.throw(u))}catch(f){o(f)}}function c(u){u.done?s(u.value):n(u.value).then(a,l)}c((i=i.apply(e,t||[])).next())})}export function __generator(e,t){var r={label:0,sent:function(){if(s[0]&1)throw s[1];return s[1]},trys:[],ops:[]},i,n,s,o;return o={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(o[Symbol.iterator]=function(){return this}),o;function a(c){return function(u){return l([c,u])}}function l(c){if(i)throw new TypeError("Generator is already executing.");for(;o&&(o=0,c[0]&&(r=0)),r;)try{if(i=1,n&&(s=c[0]&2?n.return:c[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,c[1])).done)return s;switch(n=0,s&&(c=[c[0]&2,s.value]),c[0]){case 0:case 1:s=c;break;case 4:return r.label++,{value:c[1],done:!1};case 5:r.label++,n=c[1],c=[0];continue;case 7:c=r.ops.pop(),r.trys.pop();continue;default:if(s=r.trys,!(s=s.length>0&&s[s.length-1])&&(c[0]===6||c[0]===2)){r=0;continue}if(c[0]===3&&(!s||c[1]>s[0]&&c[1]<s[3])){r.label=c[1];break}if(c[0]===6&&r.label<s[1]){r.label=s[1],s=c;break}if(s&&r.label<s[2]){r.label=s[2],r.ops.push(c);break}s[2]&&r.ops.pop(),r.trys.pop();continue}c=t.call(e,r)}catch(u){c=[6,u],n=0}finally{i=s=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,r,i){i===void 0&&(i=r);var n=Object.getOwnPropertyDescriptor(t,r);(!n||("get"in n?!t.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,i,n)}:function(e,t,r,i){i===void 0&&(i=r),e[i]=t[r]};export function __exportStar(e,t){for(var r in e)r!=="default"&&!Object.prototype.hasOwnProperty.call(t,r)&&__createBinding(t,e,r)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,r=t&&e[t],i=0;if(r)return r.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var r=typeof Symbol=="function"&&e[Symbol.iterator];if(!r)return e;var i=r.call(e),n,s=[],o;try{for(;(t===void 0||t-- >0)&&!(n=i.next()).done;)s.push(n.value)}catch(a){o={error:a}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return s}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var i=Array(e),n=0,t=0;t<r;t++)for(var s=arguments[t],o=0,a=s.length;o<a;o++,n++)i[n]=s[o];return i}export function __spreadArray(e,t,r){if(r||arguments.length===2)for(var i=0,n=t.length,s;i<n;i++)(s||!(i in t))&&(s||(s=Array.prototype.slice.call(t,0,i)),s[i]=t[i]);return e.concat(s||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=r.apply(e,t||[]),n,s=[];return n={},a("next"),a("throw"),a("return",o),n[Symbol.asyncIterator]=function(){return this},n;function o(p){return function($){return Promise.resolve($).then(p,f)}}function a(p,$){i[p]&&(n[p]=function(_){return new Promise(function(E,I){s.push([p,_,E,I])>1||l(p,_)})},$&&(n[p]=$(n[p])))}function l(p,$){try{c(i[p]($))}catch(_){d(s[0][3],_)}}function c(p){p.value instanceof __await?Promise.resolve(p.value.v).then(u,f):d(s[0][2],p)}function u(p){l("next",p)}function f(p){l("throw",p)}function d(p,$){p($),s.shift(),s.length&&l(s[0][0],s[0][1])}}export function __asyncDelegator(e){var t,r;return t={},i("next"),i("throw",function(n){throw n}),i("return"),t[Symbol.iterator]=function(){return this},t;function i(n,s){t[n]=e[n]?function(o){return(r=!r)?{value:__await(e[n](o)),done:!1}:s?s(o):o}:s}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],r;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),r={},i("next"),i("throw"),i("return"),r[Symbol.asyncIterator]=function(){return this},r);function i(s){r[s]=e[s]&&function(o){return new Promise(function(a,l){o=e[s](o),n(a,l,o.done,o.value)})}}function n(s,o,a,l){Promise.resolve(l).then(function(c){s({value:c,done:a})},o)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var pr=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var r in e)r!=="default"&&Object.prototype.hasOwnProperty.call(e,r)&&__createBinding(t,e,r);return pr(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,r,i){if(r==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!i:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return r==="m"?i:r==="a"?i.call(e):i?i.value:t.get(e)}export function __classPrivateFieldSet(e,t,r,i,n){if(i==="m")throw new TypeError("Private method is not writable");if(i==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!n:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i==="a"?n.call(e,r):n?n.value=r:t.set(e,r),r}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,r){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var i,n;if(r){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");i=t[Symbol.asyncDispose]}if(i===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");i=t[Symbol.dispose],r&&(n=i)}if(typeof i!="function")throw new TypeError("Object not disposable.");n&&(i=function(){try{n.call(this)}catch(s){return Promise.reject(s)}}),e.stack.push({value:t,dispose:i,async:r})}else r&&e.stack.push({async:!0});return t}var wr=typeof SuppressedError=="function"?SuppressedError:function(e,t,r){var i=new Error(r);return i.name="SuppressedError",i.error=e,i.suppressed=t,i};export function __disposeResources(e){function t(i){e.error=e.hasError?new wr(i,e.error,"An error was suppressed during disposal."):i,e.hasError=!0}function r(){for(;e.stack.length;){var i=e.stack.pop();try{var n=i.dispose&&i.dispose.call(i.value);if(i.async)return Promise.resolve(n).then(r,function(s){return t(s),r()})}catch(s){t(s)}}if(e.hasError)throw e.error}return r()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};import"child_process";var Be;function yr(e,t){const r=Object.create(null);for(const i of e){const n=t(i);let s=r[n];s||(s=r[n]=[]),s.push(i)}return r}var mn=class{static{Be=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[Be]="SetWithKey";for(const r of e)this.add(r)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(r=>e.call(t,r,r,this))}[Symbol.iterator](){return this.values()}},br=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?O1.isErrorNoTelemetry(e)?new O1(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},Ar=new br;function L1(e){$r(e)||Ar.onUnexpectedError(e)}var ye="Canceled";function $r(e){return e instanceof a1?!0:e instanceof Error&&e.name===ye&&e.message===ye}var a1=class extends Error{constructor(){super(ye),this.name=this.message}},pn=class fe extends Error{static{this.a="PendingMigrationError"}static is(t){return t instanceof fe||t instanceof Error&&t.name===fe.a}constructor(t){super(t),this.name=fe.a}},O1=class qe extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof qe)return t;const r=new qe;return r.message=t.message,r.stack=t.stack,r}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}};function He(e,t){const r=this;let i=!1,n;return function(){if(i)return n;if(i=!0,t)try{n=e.apply(r,arguments)}finally{t()}else n=e.apply(r,arguments);return n}}function Cr(e,t,r=0,i=e.length){let n=r,s=i;for(;n<s;){const o=Math.floor((n+s)/2);t(e[o])?n=o+1:s=o}return n-1}var wn=class ar{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(ar.assertInvariants){if(this.d){for(const i of this.e)if(this.d(i)&&!t(i))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const r=Cr(this.e,t,this.c);return this.c=r+1,r===-1?void 0:this.e[r]}},be;(function(e){function t(s){return s<0}e.isLessThan=t;function r(s){return s<=0}e.isLessThanOrEqual=r;function i(s){return s>0}e.isGreaterThan=i;function n(s){return s===0}e.isNeitherLessOrGreaterThan=n,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(be||(be={}));function Er(e,t){return(r,i)=>t(e(r),e(i))}var _r=(e,t)=>e-t,yn=class ue{static{this.empty=new ue(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(r=>(t(r),!0))}toArray(){const t=[];return this.iterate(r=>(t.push(r),!0)),t}filter(t){return new ue(r=>this.iterate(i=>t(i)?r(i):!0))}map(t){return new ue(r=>this.iterate(i=>r(t(i))))}some(t){let r=!1;return this.iterate(i=>(r=t(i),!r)),r}findFirst(t){let r;return this.iterate(i=>t(i)?(r=i,!1):!0),r}findLast(t){let r;return this.iterate(i=>(t(i)&&(r=i),!0)),r}findLastMaxBy(t){let r,i=!0;return this.iterate(n=>((i||be.isGreaterThan(t(n,r)))&&(i=!1,r=n),!0)),r}},Ke,Je,Qe,Sr=class{constructor(e,t){this.uri=e,this.value=t}};function Pr(e){return Array.isArray(e)}var Ze=class F1{static{this.c=t=>t.toString()}constructor(t,r){if(this[Ke]="ResourceMap",t instanceof F1)this.d=new Map(t.d),this.e=r??F1.c;else if(Pr(t)){this.d=new Map,this.e=r??F1.c;for(const[i,n]of t)this.set(i,n)}else this.d=new Map,this.e=t??F1.c}set(t,r){return this.d.set(this.e(t),new Sr(t,r)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,r){typeof r<"u"&&(t=t.bind(r));for(const[i,n]of this.d)t(n.value,n.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(Ke=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},bn=class{constructor(e,t){this[Je]="ResourceSet",!e||typeof e=="function"?this.c=new Ze(e):(this.c=new Ze(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((r,i)=>e.call(t,i,i,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(Je=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},Ge;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(Ge||(Ge={}));var Lr=class{constructor(){this[Qe]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const r=this.c.get(e);if(r)return t!==0&&this.n(r,t),r.value}set(e,t,r=0){let i=this.c.get(e);if(i)i.value=t,r!==0&&this.n(i,r);else{switch(i={key:e,value:t,next:void 0,previous:void 0},r){case 0:this.l(i);break;case 1:this.k(i);break;case 2:this.l(i);break;default:this.l(i);break}this.c.set(e,i),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const r=this.g;let i=this.d;for(;i;){if(t?e.bind(t)(i.value,i.key,this):e(i.value,i.key,this),this.g!==r)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){const e=this,t=this.g;let r=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const n={value:r.key,done:!1};return r=r.next,n}else return{value:void 0,done:!0}}};return i}values(){const e=this,t=this.g;let r=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const n={value:r.value,done:!1};return r=r.next,n}else return{value:void 0,done:!0}}};return i}entries(){const e=this,t=this.g;let r=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(r){const n={value:[r.key,r.value],done:!1};return r=r.next,n}else return{value:void 0,done:!0}}};return i}[(Qe=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.next,r--;this.d=t,this.f=r,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,r=this.size;for(;t&&r>e;)this.c.delete(t.key),t=t.previous,r--;this.e=t,this.f=r,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,r=e.previous;if(!t||!r)throw new Error("Invalid list");t.previous=r,r.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const r=e.next,i=e.previous;e===this.e?(i.next=void 0,this.e=i):(r.previous=i,i.next=r),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const r=e.next,i=e.previous;e===this.d?(r.previous=void 0,this.d=r):(r.previous=i,i.next=r),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,r)=>{e.push([r,t])}),e}fromJSON(e){this.clear();for(const[t,r]of e)this.set(t,r)}},Or=class extends Lr{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},Ye=class extends Or{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},xr=class{constructor(){this.c=new Map}add(e,t){let r=this.c.get(e);r||(r=new Set,this.c.set(e,r)),r.add(t)}delete(e,t){const r=this.c.get(e);r&&(r.delete(t),r.size===0&&this.c.delete(e))}forEach(e,t){const r=this.c.get(e);r&&r.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function y1(e){return typeof e=="object"&&e!==null&&!Array.isArray(e)&&!(e instanceof RegExp)&&!(e instanceof Date)}function kr(e){return!!e&&typeof e[Symbol.iterator]=="function"}function Dr(e){return typeof e>"u"}function Ir(e){return Dr(e)||e===null}var Ae;(function(e){function t(w){return!!w&&typeof w=="object"&&typeof w[Symbol.iterator]=="function"}e.is=t;const r=Object.freeze([]);function i(){return r}e.empty=i;function*n(w){yield w}e.single=n;function s(w){return t(w)?w:n(w)}e.wrap=s;function o(w){return w||r}e.from=o;function*a(w){for(let b=w.length-1;b>=0;b--)yield w[b]}e.reverse=a;function l(w){return!w||w[Symbol.iterator]().next().done===!0}e.isEmpty=l;function c(w){return w[Symbol.iterator]().next().value}e.first=c;function u(w,b){let P=0;for(const U of w)if(b(U,P++))return!0;return!1}e.some=u;function f(w,b){let P=0;for(const U of w)if(!b(U,P++))return!1;return!0}e.every=f;function d(w,b){for(const P of w)if(b(P))return P}e.find=d;function*p(w,b){for(const P of w)b(P)&&(yield P)}e.filter=p;function*$(w,b){let P=0;for(const U of w)yield b(U,P++)}e.map=$;function*_(w,b){let P=0;for(const U of w)yield*b(U,P++)}e.flatMap=_;function*E(...w){for(const b of w)kr(b)?yield*b:yield b}e.concat=E;function I(w,b,P){let U=P;for(const w1 of w)U=b(U,w1);return U}e.reduce=I;function o1(w){let b=0;for(const P of w)b++;return b}e.length=o1;function*j(w,b,P=w.length){for(b<-w.length&&(b=0),b<0&&(b+=w.length),P<0?P+=w.length:P>w.length&&(P=w.length);b<P;b++)yield w[b]}e.slice=j;function ve(w,b=Number.POSITIVE_INFINITY){const P=[];if(b===0)return[P,w];const U=w[Symbol.iterator]();for(let w1=0;w1<b;w1++){const B1=U.next();if(B1.done)return[P,e.empty()];P.push(B1.value)}return[P,{[Symbol.iterator](){return U}}]}e.consume=ve;async function me(w){const b=[];for await(const P of w)b.push(P);return b}e.asyncToArray=me;async function pe(w){let b=[];for await(const P of w)b=b.concat(P);return b}e.asyncToArrayFlat=pe})(Ae||(Ae={}));var Rr=!1,b1=null,An=class cr{constructor(){this.b=new Map}static{this.a=0}c(t){let r=this.b.get(t);return r||(r={parent:null,source:null,isSingleton:!1,value:t,idx:cr.a++},this.b.set(t,r)),r}trackDisposable(t){const r=this.c(t);r.source||(r.source=new Error().stack)}setParent(t,r){const i=this.c(t);i.parent=r}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,r){const i=r.get(t);if(i)return i;const n=t.parent?this.f(this.c(t.parent),r):t;return r.set(t,n),n}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,i])=>i.source!==null&&!this.f(i,t).isSingleton).flatMap(([i])=>i)}computeLeakingDisposables(t=10,r){let i;if(r)i=r;else{const l=new Map,c=[...this.b.values()].filter(f=>f.source!==null&&!this.f(f,l).isSingleton);if(c.length===0)return;const u=new Set(c.map(f=>f.value));if(i=c.filter(f=>!(f.parent&&u.has(f.parent))),i.length===0)throw new Error("There are cyclic diposable chains!")}if(!i)return;function n(l){function c(f,d){for(;f.length>0&&d.some(p=>typeof p=="string"?p===f[0]:f[0].match(p));)f.shift()}const u=l.source.split(`
`).map(f=>f.trim().replace("at ","")).filter(f=>f!=="");return c(u,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),u.reverse()}const s=new xr;for(const l of i){const c=n(l);for(let u=0;u<=c.length;u++)s.add(c.slice(0,u).join(`
`),l)}i.sort(Er(l=>l.idx,_r));let o="",a=0;for(const l of i.slice(0,t)){a++;const c=n(l),u=[];for(let f=0;f<c.length;f++){let d=c[f];d=`(shared with ${s.get(c.slice(0,f+1).join(`
`)).size}/${i.length} leaks) at ${d}`;const $=s.get(c.slice(0,f).join(`
`)),_=yr([...$].map(E=>n(E)[f]),E=>E);delete _[c[f]];for(const[E,I]of Object.entries(_))u.unshift(`    - stacktraces of ${I.length} other leaks continue with ${E}`);u.unshift(d)}o+=`


==================== Leaking disposable ${a}/${i.length}: ${l.value.constructor.name} ====================
${u.join(`
`)}
============================================================

`}return i.length>t&&(o+=`


... and ${i.length-t} more leaking disposables

`),{leaks:i,details:o}}};function Nr(e){b1=e}if(Rr){const e="__is_disposable_tracked__";Nr(new class{trackDisposable(t){const r=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(r)},3e3)}setParent(t,r){if(t&&t!==D1.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==D1.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function J1(e){return b1?.trackDisposable(e),e}function Q1(e){b1?.markAsDisposed(e)}function x1(e,t){b1?.setParent(e,t)}function jr(e,t){if(b1)for(const r of e)b1.setParent(r,t)}function Tr(e){return typeof e=="object"&&e!==null&&typeof e.dispose=="function"&&e.dispose.length===0}function k1(e){if(Ae.is(e)){const t=[];for(const r of e)if(r)try{r.dispose()}catch(i){t.push(i)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function Mr(...e){const t=$e(()=>k1(e));return jr(e,t),t}function $e(e){const t=J1({dispose:He(()=>{Q1(t),e()})});return t}var Ce=class lr{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,J1(this)}dispose(){this.g||(Q1(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{k1(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return x1(t,this),this.g?lr.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),x1(t,null))}},D1=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new Ce,J1(this),x1(this.q,this)}dispose(){Q1(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},Ur=class{constructor(){this.a=new Map,this.b=!1,J1(this)}dispose(){Q1(this),this.b=!0,this.clearAndDisposeAll()}clearAndDisposeAll(){if(this.a.size)try{k1(this.a.values())}finally{this.a.clear()}}has(e){return this.a.has(e)}get size(){return this.a.size}get(e){return this.a.get(e)}set(e,t,r=!1){this.b&&console.warn(new Error("Trying to add a disposable to a DisposableMap that has already been disposed of. The added object will be leaked!").stack),r||this.a.get(e)?.dispose(),this.a.set(e,t),x1(t,this)}deleteAndDispose(e){this.a.get(e)?.dispose(),this.a.delete(e)}deleteAndLeak(e){const t=this.a.get(e);return t&&x1(t,null),this.a.delete(e),t}keys(){return this.a.keys()}values(){return this.a.values()}[Symbol.iterator](){return this.a[Symbol.iterator]()}},$n=class he{static{this.Undefined=new he(void 0)}constructor(t){this.element=t,this.next=he.Undefined,this.prev=he.Undefined}},zr=globalThis.performance.now.bind(globalThis.performance),Fr=class fr{static create(t){return new fr(t)}constructor(t){this.c=t===!1?Date.now:zr,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},Xe=!1,qr=!1,Z;(function(e){e.None=()=>D1.None;function t(m){if(qr){const{onDidAddListener:h}=m,v=Ee.create();let g=0;m.onDidAddListener=()=>{++g===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),v.print()),h?.()}}}function r(m,h){return p(m,()=>{},0,void 0,!0,void 0,h)}e.defer=r;function i(m){return(h,v=null,g)=>{let y=!1,C;return C=m(L=>{if(!y)return C?C.dispose():y=!0,h.call(v,L)},null,g),y&&C.dispose(),C}}e.once=i;function n(m,h){return e.once(e.filter(m,h))}e.onceIf=n;function s(m,h,v){return f((g,y=null,C)=>m(L=>g.call(y,h(L)),null,C),v)}e.map=s;function o(m,h,v){return f((g,y=null,C)=>m(L=>{h(L),g.call(y,L)},null,C),v)}e.forEach=o;function a(m,h,v){return f((g,y=null,C)=>m(L=>h(L)&&g.call(y,L),null,C),v)}e.filter=a;function l(m){return m}e.signal=l;function c(...m){return(h,v=null,g)=>{const y=Mr(...m.map(C=>C(L=>h.call(v,L))));return d(y,g)}}e.any=c;function u(m,h,v,g){let y=v;return s(m,C=>(y=h(y,C),y),g)}e.reduce=u;function f(m,h){let v;const g={onWillAddFirstListener(){v=m(y.fire,y)},onDidRemoveLastListener(){v?.dispose()}};h||t(g);const y=new K(g);return h?.add(y),y.event}function d(m,h){return h instanceof Array?h.push(m):h&&h.add(m),m}function p(m,h,v=100,g=!1,y=!1,C,L){let T,z,u1,H1=0,P1;const Ve={leakWarningThreshold:C,onWillAddFirstListener(){T=m(vr=>{H1++,z=h(z,vr),g&&!u1&&(K1.fire(z),z=void 0),P1=()=>{const mr=z;z=void 0,u1=void 0,(!g||H1>1)&&K1.fire(mr),H1=0},typeof v=="number"?(u1&&clearTimeout(u1),u1=setTimeout(P1,v)):u1===void 0&&(u1=null,queueMicrotask(P1))})},onWillRemoveListener(){y&&H1>0&&P1?.()},onDidRemoveLastListener(){P1=void 0,T.dispose()}};L||t(Ve);const K1=new K(Ve);return L?.add(K1),K1.event}e.debounce=p;function $(m,h=0,v){return e.debounce(m,(g,y)=>g?(g.push(y),g):[y],h,void 0,!0,void 0,v)}e.accumulate=$;function _(m,h=(g,y)=>g===y,v){let g=!0,y;return a(m,C=>{const L=g||!h(C,y);return g=!1,y=C,L},v)}e.latch=_;function E(m,h,v){return[e.filter(m,h,v),e.filter(m,g=>!h(g),v)]}e.split=E;function I(m,h=!1,v=[],g){let y=v.slice(),C=m(z=>{y?y.push(z):T.fire(z)});g&&g.add(C);const L=()=>{y?.forEach(z=>T.fire(z)),y=null},T=new K({onWillAddFirstListener(){C||(C=m(z=>T.fire(z)),g&&g.add(C))},onDidAddFirstListener(){y&&(h?setTimeout(L):L())},onDidRemoveLastListener(){C&&C.dispose(),C=null}});return g&&g.add(T),T.event}e.buffer=I;function o1(m,h){return(g,y,C)=>{const L=h(new ve);return m(function(T){const z=L.evaluate(T);z!==j&&g.call(y,z)},void 0,C)}}e.chain=o1;const j=Symbol("HaltChainable");class ve{constructor(){this.f=[]}map(h){return this.f.push(h),this}forEach(h){return this.f.push(v=>(h(v),v)),this}filter(h){return this.f.push(v=>h(v)?v:j),this}reduce(h,v){let g=v;return this.f.push(y=>(g=h(g,y),g)),this}latch(h=(v,g)=>v===g){let v=!0,g;return this.f.push(y=>{const C=v||!h(y,g);return v=!1,g=y,C?y:j}),this}evaluate(h){for(const v of this.f)if(h=v(h),h===j)break;return h}}function me(m,h,v=g=>g){const g=(...T)=>L.fire(v(...T)),y=()=>m.on(h,g),C=()=>m.removeListener(h,g),L=new K({onWillAddFirstListener:y,onDidRemoveLastListener:C});return L.event}e.fromNodeEventEmitter=me;function pe(m,h,v=g=>g){const g=(...T)=>L.fire(v(...T)),y=()=>m.addEventListener(h,g),C=()=>m.removeEventListener(h,g),L=new K({onWillAddFirstListener:y,onDidRemoveLastListener:C});return L.event}e.fromDOMEventEmitter=pe;function w(m,h){let v;const g=new Promise((y,C)=>{const L=i(m)(y,null,h);v=()=>L.dispose()});return g.cancel=v,g}e.toPromise=w;function b(m){const h=new K;return m.then(v=>{h.fire(v)},()=>{h.fire(void 0)}).finally(()=>{h.dispose()}),h.event}e.fromPromise=b;function P(m,h){return m(v=>h.fire(v))}e.forward=P;function U(m,h,v){return h(v),m(g=>h(g))}e.runAndSubscribe=U;class w1{constructor(h,v){this._observable=h,this.f=0,this.g=!1;const g={onWillAddFirstListener:()=>{h.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{h.removeObserver(this)}};v||t(g),this.emitter=new K(g),v&&v.add(this.emitter)}beginUpdate(h){this.f++}handlePossibleChange(h){}handleChange(h,v){this.g=!0}endUpdate(h){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function B1(m,h){return new w1(m,h).emitter.event}e.fromObservable=B1;function gr(m){return(h,v,g)=>{let y=0,C=!1;const L={beginUpdate(){y++},endUpdate(){y--,y===0&&(m.reportChanges(),C&&(C=!1,h.call(v)))},handlePossibleChange(){},handleChange(){C=!0}};m.addObserver(L),m.reportChanges();const T={dispose(){m.removeObserver(L)}};return g instanceof Ce?g.add(T):Array.isArray(g)&&g.push(T),T}}e.fromObservableLight=gr})(Z||(Z={}));var Wr=class We{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${We.f++}`,We.all.add(this)}start(t){this.g=new Fr,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},et=-1,Vr=class ur{static{this.f=1}constructor(t,r,i=(ur.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=r,this.name=i,this.h=0}dispose(){this.g?.clear()}check(t,r){const i=this.threshold;if(i<=0||r<i)return;this.g||(this.g=new Map);const n=this.g.get(t.value)||0;if(this.g.set(t.value,n+1),this.h-=1,this.h<=0){this.h=i*.5;const[s,o]=this.getMostFrequentStack(),a=`[${this.name}] potential listener LEAK detected, having ${r} listeners already. MOST frequent listener (${o}):`;console.warn(a),console.warn(s);const l=new Br(a,s);this.j(l)}return()=>{const s=this.g.get(t.value)||0;this.g.set(t.value,s-1)}}getMostFrequentStack(){if(!this.g)return;let t,r=0;for(const[i,n]of this.g)(!t||r<n)&&(t=[i,n],r=n);return t}},Ee=class hr{static create(){const t=new Error;return new hr(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},Br=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},Hr=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},Kr=0,Z1=class{constructor(e){this.value=e,this.id=Kr++}},Jr=2,Qr=(e,t)=>{if(e instanceof Z1)t(e);else for(let r=0;r<e.length;r++){const i=e[r];i&&t(i)}},K=class{constructor(e){this.A=0,this.g=e,this.j=et>0||this.g?.leakWarningThreshold?new Vr(e?.onListenerError??L1,this.g?.leakWarningThreshold??et):void 0,this.m=this.g?._profName?new Wr(this.g._profName):void 0,this.z=this.g?.deliveryQueue}dispose(){if(!this.q){if(this.q=!0,this.z?.current===this&&this.z.reset(),this.w){if(Xe){const e=this.w;queueMicrotask(()=>{Qr(e,t=>t.stack?.print())})}this.w=void 0,this.A=0}this.g?.onDidRemoveLastListener?.(),this.j?.dispose()}}get event(){return this.u??=(e,t,r)=>{if(this.j&&this.A>this.j.threshold**2){const a=`[${this.j.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.A} vs ${this.j.threshold})`;console.warn(a);const l=this.j.getMostFrequentStack()??["UNKNOWN stack",-1],c=new Hr(`${a}. HINT: Stack shows most frequent listener (${l[1]}-times)`,l[0]);return(this.g?.onListenerError||L1)(c),D1.None}if(this.q)return D1.None;t&&(e=e.bind(t));const i=new Z1(e);let n,s;this.j&&this.A>=Math.ceil(this.j.threshold*.2)&&(i.stack=Ee.create(),n=this.j.check(i.stack,this.A+1)),Xe&&(i.stack=s??Ee.create()),this.w?this.w instanceof Z1?(this.z??=new Zr,this.w=[this.w,i]):this.w.push(i):(this.g?.onWillAddFirstListener?.(this),this.w=i,this.g?.onDidAddFirstListener?.(this)),this.g?.onDidAddListener?.(this),this.A++;const o=$e(()=>{n?.(),this.B(i)});return r instanceof Ce?r.add(o):Array.isArray(r)&&r.push(o),o},this.u}B(e){if(this.g?.onWillRemoveListener?.(this),!this.w)return;if(this.A===1){this.w=void 0,this.g?.onDidRemoveLastListener?.(this),this.A=0;return}const t=this.w,r=t.indexOf(e);if(r===-1)throw console.log("disposed?",this.q),console.log("size?",this.A),console.log("arr?",JSON.stringify(this.w)),new Error("Attempted to dispose unknown listener");this.A--,t[r]=void 0;const i=this.z.current===this;if(this.A*Jr<=t.length){let n=0;for(let s=0;s<t.length;s++)t[s]?t[n++]=t[s]:i&&n<this.z.end&&(this.z.end--,n<this.z.i&&this.z.i--);t.length=n}}C(e,t){if(!e)return;const r=this.g?.onListenerError||L1;if(!r){e.value(t);return}try{e.value(t)}catch(i){r(i)}}D(e){const t=e.current.w;for(;e.i<e.end;)this.C(t[e.i++],e.value);e.reset()}fire(e){if(this.z?.current&&(this.D(this.z),this.m?.stop()),this.m?.start(this.A),this.w)if(this.w instanceof Z1)this.C(this.w,e);else{const t=this.z;t.enqueue(this,e,this.w.length),this.D(t)}this.m?.stop()}hasListeners(){return this.A>0}},Zr=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,r){this.i=0,this.end=r,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}},tt=Object.freeze(function(e,t){const r=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(r)}}}),A1;(function(e){function t(r){return r===e.None||r===e.Cancelled||r instanceof G1?!0:!r||typeof r!="object"?!1:typeof r.isCancellationRequested=="boolean"&&typeof r.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Z.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:tt})})(A1||(A1={}));var G1=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?tt:(this.b||(this.b=new K),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},rt=class{constructor(e){this.f=void 0,this.g=void 0,this.g=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new G1),this.f}cancel(){this.f?this.f instanceof G1&&this.f.cancel():this.f=A1.Cancelled}dispose(e=!1){e&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof G1&&this.f.dispose():this.f=A1.None}};function Gr(){return globalThis._VSCODE_NLS_MESSAGES}function it(){return globalThis._VSCODE_NLS_LANGUAGE}var Yr=it()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function nt(e,t){let r;return t.length===0?r=e:r=e.replace(/\{(\d+)\}/g,(i,n)=>{const s=n[0],o=t[s];let a=i;return typeof o=="string"?a=o:(typeof o=="number"||typeof o=="boolean"||o===void 0||o===null)&&(a=String(o)),a}),Yr&&(r="\uFF3B"+r.replace(/[aouei]/g,"$&$&")+"\uFF3D"),r}function Xr(e,t,...r){return nt(typeof e=="number"?e2(e,t):t,r)}function e2(e,t){const r=Gr()?.[e];if(typeof r!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return r}var $1="en",Y1=!1,X1=!1,I1=!1,t2=!1,st=!1,_e=!1,r2=!1,i2=!1,n2=!1,s2=!1,ee=void 0,te=$1,ot=$1,o2=void 0,e1=void 0,t1=globalThis,q=void 0;typeof t1.vscode<"u"&&typeof t1.vscode.process<"u"?q=t1.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(q=process);var at=typeof q?.versions?.electron=="string",a2=at&&q?.type==="renderer";if(typeof q=="object"){Y1=q.platform==="win32",X1=q.platform==="darwin",I1=q.platform==="linux",t2=I1&&!!q.env.SNAP&&!!q.env.SNAP_REVISION,r2=at,n2=!!q.env.CI||!!q.env.BUILD_ARTIFACTSTAGINGDIRECTORY||!!q.env.GITHUB_WORKSPACE,ee=$1,te=$1;const e=q.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);ee=t.userLocale,ot=t.osLocale,te=t.resolvedLanguage||$1,o2=t.languagePack?.translationsConfigFile}catch{}st=!0}else typeof navigator=="object"&&!a2?(e1=navigator.userAgent,Y1=e1.indexOf("Windows")>=0,X1=e1.indexOf("Macintosh")>=0,i2=(e1.indexOf("Macintosh")>=0||e1.indexOf("iPad")>=0||e1.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,I1=e1.indexOf("Linux")>=0,s2=e1?.indexOf("Mobi")>=0,_e=!0,te=it()||$1,ee=navigator.language.toLowerCase(),ot=ee):console.error("Unable to resolve platform.");var ct;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(ct||(ct={}));var Se=0;X1?Se=1:Y1?Se=3:I1&&(Se=2);var J=Y1,Pe=X1,lt=I1,c2=st,re=_e,l2=_e&&typeof t1.importScripts=="function",f2=l2?t1.origin:void 0,X=e1,c1=te,ft;(function(e){function t(){return c1}e.value=t;function r(){return c1.length===2?c1==="en":c1.length>=3?c1[0]==="e"&&c1[1]==="n"&&c1[2]==="-":!1}e.isDefaultVariant=r;function i(){return c1==="en"}e.isDefault=i})(ft||(ft={}));var u2=typeof t1.postMessage=="function"&&!t1.importScripts,h2=(()=>{if(u2){const e=[];t1.addEventListener("message",r=>{if(r.data&&r.data.vscodeScheduleAsyncWork)for(let i=0,n=e.length;i<n;i++){const s=e[i];if(s.id===r.data.vscodeScheduleAsyncWork){e.splice(i,1),s.callback();return}}});let t=0;return r=>{const i=++t;e.push({id:i,callback:r}),t1.postMessage({vscodeScheduleAsyncWork:i},"*")}}return e=>setTimeout(e)})(),ut;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(ut||(ut={}));var d2=!!(X&&X.indexOf("Chrome")>=0),Cn=!!(X&&X.indexOf("Firefox")>=0),En=!!(!d2&&X&&X.indexOf("Safari")>=0),_n=!!(X&&X.indexOf("Edg/")>=0),Sn=!!(X&&X.indexOf("Android")>=0),h1,Le=globalThis.vscode;if(typeof Le<"u"&&typeof Le.process<"u"){const e=Le.process;h1={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?h1={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:h1={get platform(){return J?"win32":Pe?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var ie=h1.cwd,g2=h1.env,v2=h1.platform,Pn=h1.arch,m2=65,p2=97,w2=90,y2=122,d1=46,M=47,W=92,r1=58,b2=63,ht=class extends Error{constructor(e,t,r){let i;typeof t=="string"&&t.indexOf("not ")===0?(i="must not be",t=t.replace(/^not /,"")):i="must be";const n=e.indexOf(".")!==-1?"property":"argument";let s=`The "${e}" ${n} ${i} of type ${t}`;s+=`. Received type ${typeof r}`,super(s),this.code="ERR_INVALID_ARG_TYPE"}};function A2(e,t){if(e===null||typeof e!="object")throw new ht(t,"Object",e)}function D(e,t){if(typeof e!="string")throw new ht(t,"string",e)}var V=v2==="win32";function A(e){return e===M||e===W}function Oe(e){return e===M}function i1(e){return e>=m2&&e<=w2||e>=p2&&e<=y2}function ne(e,t,r,i){let n="",s=0,o=-1,a=0,l=0;for(let c=0;c<=e.length;++c){if(c<e.length)l=e.charCodeAt(c);else{if(i(l))break;l=M}if(i(l)){if(!(o===c-1||a===1))if(a===2){if(n.length<2||s!==2||n.charCodeAt(n.length-1)!==d1||n.charCodeAt(n.length-2)!==d1){if(n.length>2){const u=n.lastIndexOf(r);u===-1?(n="",s=0):(n=n.slice(0,u),s=n.length-1-n.lastIndexOf(r)),o=c,a=0;continue}else if(n.length!==0){n="",s=0,o=c,a=0;continue}}t&&(n+=n.length>0?`${r}..`:"..",s=2)}else n.length>0?n+=`${r}${e.slice(o+1,c)}`:n=e.slice(o+1,c),s=c-o-1;o=c,a=0}else l===d1&&a!==-1?++a:a=-1}return n}function $2(e){return e?`${e[0]==="."?"":"."}${e}`:""}function dt(e,t){A2(t,"pathObject");const r=t.dir||t.root,i=t.base||`${t.name||""}${$2(t.ext)}`;return r?r===t.root?`${r}${i}`:`${r}${e}${i}`:i}var R={resolve(...e){let t="",r="",i=!1;for(let n=e.length-1;n>=-1;n--){let s;if(n>=0){if(s=e[n],D(s,`paths[${n}]`),s.length===0)continue}else t.length===0?s=ie():(s=g2[`=${t}`]||ie(),(s===void 0||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===W)&&(s=`${t}\\`));const o=s.length;let a=0,l="",c=!1;const u=s.charCodeAt(0);if(o===1)A(u)&&(a=1,c=!0);else if(A(u))if(c=!0,A(s.charCodeAt(1))){let f=2,d=f;for(;f<o&&!A(s.charCodeAt(f));)f++;if(f<o&&f!==d){const p=s.slice(d,f);for(d=f;f<o&&A(s.charCodeAt(f));)f++;if(f<o&&f!==d){for(d=f;f<o&&!A(s.charCodeAt(f));)f++;(f===o||f!==d)&&(l=`\\\\${p}\\${s.slice(d,f)}`,a=f)}}}else a=1;else i1(u)&&s.charCodeAt(1)===r1&&(l=s.slice(0,2),a=2,o>2&&A(s.charCodeAt(2))&&(c=!0,a=3));if(l.length>0)if(t.length>0){if(l.toLowerCase()!==t.toLowerCase())continue}else t=l;if(i){if(t.length>0)break}else if(r=`${s.slice(a)}\\${r}`,i=c,c&&t.length>0)break}return r=ne(r,!i,"\\",A),i?`${t}\\${r}`:`${t}${r}`||"."},normalize(e){D(e,"path");const t=e.length;if(t===0)return".";let r=0,i,n=!1;const s=e.charCodeAt(0);if(t===1)return Oe(s)?"\\":e;if(A(s))if(n=!0,A(e.charCodeAt(1))){let a=2,l=a;for(;a<t&&!A(e.charCodeAt(a));)a++;if(a<t&&a!==l){const c=e.slice(l,a);for(l=a;a<t&&A(e.charCodeAt(a));)a++;if(a<t&&a!==l){for(l=a;a<t&&!A(e.charCodeAt(a));)a++;if(a===t)return`\\\\${c}\\${e.slice(l)}\\`;a!==l&&(i=`\\\\${c}\\${e.slice(l,a)}`,r=a)}}}else r=1;else i1(s)&&e.charCodeAt(1)===r1&&(i=e.slice(0,2),r=2,t>2&&A(e.charCodeAt(2))&&(n=!0,r=3));let o=r<t?ne(e.slice(r),!n,"\\",A):"";if(o.length===0&&!n&&(o="."),o.length>0&&A(e.charCodeAt(t-1))&&(o+="\\"),!n&&i===void 0&&e.includes(":")){if(o.length>=2&&i1(o.charCodeAt(0))&&o.charCodeAt(1)===r1)return`.\\${o}`;let a=e.indexOf(":");do if(a===t-1||A(e.charCodeAt(a+1)))return`.\\${o}`;while((a=e.indexOf(":",a+1))!==-1)}return i===void 0?n?`\\${o}`:o:n?`${i}\\${o}`:`${i}${o}`},isAbsolute(e){D(e,"path");const t=e.length;if(t===0)return!1;const r=e.charCodeAt(0);return A(r)||t>2&&i1(r)&&e.charCodeAt(1)===r1&&A(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,r;for(let s=0;s<e.length;++s){const o=e[s];D(o,"path"),o.length>0&&(t===void 0?t=r=o:t+=`\\${o}`)}if(t===void 0)return".";let i=!0,n=0;if(typeof r=="string"&&A(r.charCodeAt(0))){++n;const s=r.length;s>1&&A(r.charCodeAt(1))&&(++n,s>2&&(A(r.charCodeAt(2))?++n:i=!1))}if(i){for(;n<t.length&&A(t.charCodeAt(n));)n++;n>=2&&(t=`\\${t.slice(n)}`)}return R.normalize(t)},relative(e,t){if(D(e,"from"),D(t,"to"),e===t)return"";const r=R.resolve(e),i=R.resolve(t);if(r===i||(e=r.toLowerCase(),t=i.toLowerCase(),e===t))return"";if(r.length!==e.length||i.length!==t.length){const $=r.split("\\"),_=i.split("\\");$[$.length-1]===""&&$.pop(),_[_.length-1]===""&&_.pop();const E=$.length,I=_.length,o1=E<I?E:I;let j;for(j=0;j<o1&&$[j].toLowerCase()===_[j].toLowerCase();j++);return j===0?i:j===o1?I>o1?_.slice(j).join("\\"):E>o1?"..\\".repeat(E-1-j)+"..":"":"..\\".repeat(E-j)+_.slice(j).join("\\")}let n=0;for(;n<e.length&&e.charCodeAt(n)===W;)n++;let s=e.length;for(;s-1>n&&e.charCodeAt(s-1)===W;)s--;const o=s-n;let a=0;for(;a<t.length&&t.charCodeAt(a)===W;)a++;let l=t.length;for(;l-1>a&&t.charCodeAt(l-1)===W;)l--;const c=l-a,u=o<c?o:c;let f=-1,d=0;for(;d<u;d++){const $=e.charCodeAt(n+d);if($!==t.charCodeAt(a+d))break;$===W&&(f=d)}if(d!==u){if(f===-1)return i}else{if(c>u){if(t.charCodeAt(a+d)===W)return i.slice(a+d+1);if(d===2)return i.slice(a+d)}o>u&&(e.charCodeAt(n+d)===W?f=d:d===2&&(f=3)),f===-1&&(f=0)}let p="";for(d=n+f+1;d<=s;++d)(d===s||e.charCodeAt(d)===W)&&(p+=p.length===0?"..":"\\..");return a+=f,p.length>0?`${p}${i.slice(a,l)}`:(i.charCodeAt(a)===W&&++a,i.slice(a,l))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=R.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===W){if(t.charCodeAt(1)===W){const r=t.charCodeAt(2);if(r!==b2&&r!==d1)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(i1(t.charCodeAt(0))&&t.charCodeAt(1)===r1&&t.charCodeAt(2)===W)return`\\\\?\\${t}`;return t},dirname(e){D(e,"path");const t=e.length;if(t===0)return".";let r=-1,i=0;const n=e.charCodeAt(0);if(t===1)return A(n)?e:".";if(A(n)){if(r=i=1,A(e.charCodeAt(1))){let a=2,l=a;for(;a<t&&!A(e.charCodeAt(a));)a++;if(a<t&&a!==l){for(l=a;a<t&&A(e.charCodeAt(a));)a++;if(a<t&&a!==l){for(l=a;a<t&&!A(e.charCodeAt(a));)a++;if(a===t)return e;a!==l&&(r=i=a+1)}}}}else i1(n)&&e.charCodeAt(1)===r1&&(r=t>2&&A(e.charCodeAt(2))?3:2,i=r);let s=-1,o=!0;for(let a=t-1;a>=i;--a)if(A(e.charCodeAt(a))){if(!o){s=a;break}}else o=!1;if(s===-1){if(r===-1)return".";s=r}return e.slice(0,s)},basename(e,t){t!==void 0&&D(t,"suffix"),D(e,"path");let r=0,i=-1,n=!0,s;if(e.length>=2&&i1(e.charCodeAt(0))&&e.charCodeAt(1)===r1&&(r=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(s=e.length-1;s>=r;--s){const l=e.charCodeAt(s);if(A(l)){if(!n){r=s+1;break}}else a===-1&&(n=!1,a=s+1),o>=0&&(l===t.charCodeAt(o)?--o===-1&&(i=s):(o=-1,i=a))}return r===i?i=a:i===-1&&(i=e.length),e.slice(r,i)}for(s=e.length-1;s>=r;--s)if(A(e.charCodeAt(s))){if(!n){r=s+1;break}}else i===-1&&(n=!1,i=s+1);return i===-1?"":e.slice(r,i)},extname(e){D(e,"path");let t=0,r=-1,i=0,n=-1,s=!0,o=0;e.length>=2&&e.charCodeAt(1)===r1&&i1(e.charCodeAt(0))&&(t=i=2);for(let a=e.length-1;a>=t;--a){const l=e.charCodeAt(a);if(A(l)){if(!s){i=a+1;break}continue}n===-1&&(s=!1,n=a+1),l===d1?r===-1?r=a:o!==1&&(o=1):r!==-1&&(o=-1)}return r===-1||n===-1||o===0||o===1&&r===n-1&&r===i+1?"":e.slice(r,n)},format:dt.bind(null,"\\"),parse(e){D(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.length;let i=0,n=e.charCodeAt(0);if(r===1)return A(n)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(A(n)){if(i=1,A(e.charCodeAt(1))){let f=2,d=f;for(;f<r&&!A(e.charCodeAt(f));)f++;if(f<r&&f!==d){for(d=f;f<r&&A(e.charCodeAt(f));)f++;if(f<r&&f!==d){for(d=f;f<r&&!A(e.charCodeAt(f));)f++;f===r?i=f:f!==d&&(i=f+1)}}}}else if(i1(n)&&e.charCodeAt(1)===r1){if(r<=2)return t.root=t.dir=e,t;if(i=2,A(e.charCodeAt(2))){if(r===3)return t.root=t.dir=e,t;i=3}}i>0&&(t.root=e.slice(0,i));let s=-1,o=i,a=-1,l=!0,c=e.length-1,u=0;for(;c>=i;--c){if(n=e.charCodeAt(c),A(n)){if(!l){o=c+1;break}continue}a===-1&&(l=!1,a=c+1),n===d1?s===-1?s=c:u!==1&&(u=1):s!==-1&&(u=-1)}return a!==-1&&(s===-1||u===0||u===1&&s===a-1&&s===o+1?t.base=t.name=e.slice(o,a):(t.name=e.slice(o,s),t.base=e.slice(o,a),t.ext=e.slice(s,a))),o>0&&o!==i?t.dir=e.slice(0,o-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},C2=(()=>{if(V){const e=/\\/g;return()=>{const t=ie().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>ie()})(),x={resolve(...e){let t="",r=!1;for(let i=e.length-1;i>=0&&!r;i--){const n=e[i];D(n,`paths[${i}]`),n.length!==0&&(t=`${n}/${t}`,r=n.charCodeAt(0)===M)}if(!r){const i=C2();t=`${i}/${t}`,r=i.charCodeAt(0)===M}return t=ne(t,!r,"/",Oe),r?`/${t}`:t.length>0?t:"."},normalize(e){if(D(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===M,r=e.charCodeAt(e.length-1)===M;return e=ne(e,!t,"/",Oe),e.length===0?t?"/":r?"./":".":(r&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return D(e,"path"),e.length>0&&e.charCodeAt(0)===M},join(...e){if(e.length===0)return".";const t=[];for(let r=0;r<e.length;++r){const i=e[r];D(i,"path"),i.length>0&&t.push(i)}return t.length===0?".":x.normalize(t.join("/"))},relative(e,t){if(D(e,"from"),D(t,"to"),e===t||(e=x.resolve(e),t=x.resolve(t),e===t))return"";const r=1,i=e.length,n=i-r,s=1,o=t.length-s,a=n<o?n:o;let l=-1,c=0;for(;c<a;c++){const f=e.charCodeAt(r+c);if(f!==t.charCodeAt(s+c))break;f===M&&(l=c)}if(c===a)if(o>a){if(t.charCodeAt(s+c)===M)return t.slice(s+c+1);if(c===0)return t.slice(s+c)}else n>a&&(e.charCodeAt(r+c)===M?l=c:c===0&&(l=0));let u="";for(c=r+l+1;c<=i;++c)(c===i||e.charCodeAt(c)===M)&&(u+=u.length===0?"..":"/..");return`${u}${t.slice(s+l)}`},toNamespacedPath(e){return e},dirname(e){if(D(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===M;let r=-1,i=!0;for(let n=e.length-1;n>=1;--n)if(e.charCodeAt(n)===M){if(!i){r=n;break}}else i=!1;return r===-1?t?"/":".":t&&r===1?"//":e.slice(0,r)},basename(e,t){t!==void 0&&D(t,"suffix"),D(e,"path");let r=0,i=-1,n=!0,s;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let o=t.length-1,a=-1;for(s=e.length-1;s>=0;--s){const l=e.charCodeAt(s);if(l===M){if(!n){r=s+1;break}}else a===-1&&(n=!1,a=s+1),o>=0&&(l===t.charCodeAt(o)?--o===-1&&(i=s):(o=-1,i=a))}return r===i?i=a:i===-1&&(i=e.length),e.slice(r,i)}for(s=e.length-1;s>=0;--s)if(e.charCodeAt(s)===M){if(!n){r=s+1;break}}else i===-1&&(n=!1,i=s+1);return i===-1?"":e.slice(r,i)},extname(e){D(e,"path");let t=-1,r=0,i=-1,n=!0,s=0;for(let o=e.length-1;o>=0;--o){const a=e[o];if(a==="/"){if(!n){r=o+1;break}continue}i===-1&&(n=!1,i=o+1),a==="."?t===-1?t=o:s!==1&&(s=1):t!==-1&&(s=-1)}return t===-1||i===-1||s===0||s===1&&t===i-1&&t===r+1?"":e.slice(t,i)},format:dt.bind(null,"/"),parse(e){D(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const r=e.charCodeAt(0)===M;let i;r?(t.root="/",i=1):i=0;let n=-1,s=0,o=-1,a=!0,l=e.length-1,c=0;for(;l>=i;--l){const u=e.charCodeAt(l);if(u===M){if(!a){s=l+1;break}continue}o===-1&&(a=!1,o=l+1),u===d1?n===-1?n=l:c!==1&&(c=1):n!==-1&&(c=-1)}if(o!==-1){const u=s===0&&r?1:s;n===-1||c===0||c===1&&n===o-1&&n===s+1?t.base=t.name=e.slice(u,o):(t.name=e.slice(u,n),t.base=e.slice(u,o),t.ext=e.slice(n,o))}return s>0?t.dir=e.slice(0,s-1):r&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};x.win32=R.win32=R,x.posix=R.posix=x;var xe=V?R.normalize:x.normalize,Ln=V?R.isAbsolute:x.isAbsolute,g1=V?R.join:x.join,E2=V?R.resolve:x.resolve,_2=V?R.relative:x.relative,S2=V?R.dirname:x.dirname,On=V?R.basename:x.basename,xn=V?R.extname:x.extname,kn=V?R.format:x.format,Dn=V?R.parse:x.parse,In=V?R.toNamespacedPath:x.toNamespacedPath,R1=V?R.sep:x.sep,Rn=V?R.delimiter:x.delimiter;function P2(e){return e}var L2=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=P2):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}},ke=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function O2(e,t){if(!e||!t)return e;const r=t.length,i=e.length;if(r===0||i===0)return e;let n=i,s=-1;for(;s=e.lastIndexOf(t,n-1),!(s===-1||s+r!==n);){if(s===0)return"";n=s}return e.substring(0,n)}function x2(e,t){return e<t?-1:e>t?1:0}function k2(e,t,r=0,i=e.length,n=0,s=t.length){for(;r<i&&n<s;r++,n++){const l=e.charCodeAt(r),c=t.charCodeAt(n);if(l<c)return-1;if(l>c)return 1}const o=i-r,a=s-n;return o<a?-1:o>a?1:0}function gt(e,t,r=0,i=e.length,n=0,s=t.length){for(;r<i&&n<s;r++,n++){let l=e.charCodeAt(r),c=t.charCodeAt(n);if(l===c)continue;if(l>=128||c>=128)return k2(e.toLowerCase(),t.toLowerCase(),r,i,n,s);vt(l)&&(l-=32),vt(c)&&(c-=32);const u=l-c;if(u!==0)return u}const o=i-r,a=s-n;return o<a?-1:o>a?1:0}function vt(e){return e>=97&&e<=122}function mt(e){return e>=65&&e<=90}function D2(e,t){return e.length===t.length&&gt(e,t)===0}function I2(e,t){const r=t.length;return t.length>e.length?!1:gt(e,t,0,r)===0}var R2=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,N2=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,j2=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,Nn=new RegExp("(?:"+[R2.source,N2.source,j2.source].join("|")+")","g"),jn="\uFEFF",pt;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(pt||(pt={}));var Tn=class q1{static{this.c=null}static getInstance(){return q1.c||(q1.c=new q1),q1.c}constructor(){this.d=T2()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const r=this.d,i=r.length/3;let n=1;for(;n<=i;)if(t<r[3*n])n=2*n;else if(t>r[3*n+1])n=2*n+1;else return r[3*n+2];return 0}};function T2(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var wt;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(wt||(wt={}));var Mn=class W1{static{this.c=new ke(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new L2({getCacheKey:JSON.stringify},t=>{function r(u){const f=new Map;for(let d=0;d<u.length;d+=2)f.set(u[d],u[d+1]);return f}function i(u,f){const d=new Map(u);for(const[p,$]of f)d.set(p,$);return d}function n(u,f){if(!u)return f;const d=new Map;for(const[p,$]of u)f.has(p)&&d.set(p,$);return d}const s=this.c.value;let o=t.filter(u=>!u.startsWith("_")&&u in s);o.length===0&&(o=["_default"]);let a;for(const u of o){const f=r(s[u]);a=n(a,f)}const l=r(s._common),c=i(l,a);return new W1(c)})}static getInstance(t){return W1.d.get(Array.from(t))}static{this.e=new ke(()=>Object.keys(W1.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return W1.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let r=0;r<t.length;r++){const i=t.codePointAt(r);if(typeof i=="number"&&this.isAmbiguous(i))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},Un=class V1{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(V1.c())].flat())),this.d}static isInvisibleCharacter(t){return V1.e().has(t)}static containsInvisibleCharacter(t){for(let r=0;r<t.length;r++){const i=t.codePointAt(r);if(typeof i=="number"&&(V1.isInvisibleCharacter(i)||i===32))return!0}return!1}static get codePoints(){return V1.e()}};function l1(e){return e===47||e===92}function yt(e){return e.replace(/[\\/]/g,x.sep)}function M2(e){return e.indexOf("/")===-1&&(e=yt(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function bt(e,t=x.sep){if(!e)return"";const r=e.length,i=e.charCodeAt(0);if(l1(i)){if(l1(e.charCodeAt(1))&&!l1(e.charCodeAt(2))){let s=3;const o=s;for(;s<r&&!l1(e.charCodeAt(s));s++);if(o!==s&&!l1(e.charCodeAt(s+1))){for(s+=1;s<r;s++)if(l1(e.charCodeAt(s)))return e.slice(0,s+1).replace(/[\\/]/g,t)}}return t}else if(At(i)&&e.charCodeAt(1)===58)return l1(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let n=e.indexOf("://");if(n!==-1){for(n+=3;n<r;n++)if(l1(e.charCodeAt(n)))return e.slice(0,n+1)}return""}function De(e,t,r,i=R1){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(r){if(!I2(e,t))return!1;if(t.length===e.length)return!0;let s=t.length;return t.charAt(t.length-1)===i&&s--,e.charAt(s)===i}return t.charAt(t.length-1)!==i&&(t+=i),e.indexOf(t)===0}function At(e){return e>=65&&e<=90||e>=97&&e<=122}function $t(e){const t=xe(e);return J?e.length>3?!1:U2(t)&&(e.length===2||t.charCodeAt(2)===92):t===x.sep}function U2(e,t=J){return t?At(e.charCodeAt(0))&&e.charCodeAt(1)===58:!1}var z2="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",F2="BDEFGHIJKMOQRSTUVWXYZbdefghijkmoqrstuvwxyz0123456789";function q2(e,t,r=8){let i="";for(let s=0;s<r;s++){let o;s===0&&J&&!t&&(r===3||r===4)?o=F2:o=z2,i+=o.charAt(Math.floor(Math.random()*o.length))}let n;return t?n=`${t}-${i}`:n=i,e?g1(e,n):n}var W2=/^\w[\w\d+.-]*$/,V2=/^\//,B2=/^\/\//;function H2(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!W2.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!V2.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(B2.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function K2(e,t){return!e&&!t?"file":e}function J2(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==G&&(t=G+t):t=G;break}return t}var k="",G="/",Q2=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,B=class de{static isUri(t){return t instanceof de?!0:!t||typeof t!="object"?!1:typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function"}constructor(t,r,i,n,s,o=!1){typeof t=="object"?(this.scheme=t.scheme||k,this.authority=t.authority||k,this.path=t.path||k,this.query=t.query||k,this.fragment=t.fragment||k):(this.scheme=K2(t,o),this.authority=r||k,this.path=J2(this.scheme,i||k),this.query=n||k,this.fragment=s||k,H2(this,o))}get fsPath(){return se(this,!1)}with(t){if(!t)return this;let{scheme:r,authority:i,path:n,query:s,fragment:o}=t;return r===void 0?r=this.scheme:r===null&&(r=k),i===void 0?i=this.authority:i===null&&(i=k),n===void 0?n=this.path:n===null&&(n=k),s===void 0?s=this.query:s===null&&(s=k),o===void 0?o=this.fragment:o===null&&(o=k),r===this.scheme&&i===this.authority&&n===this.path&&s===this.query&&o===this.fragment?this:new C1(r,i,n,s,o)}static parse(t,r=!1){const i=Q2.exec(t);return i?new C1(i[2]||k,oe(i[4]||k),oe(i[5]||k),oe(i[7]||k),oe(i[9]||k),r):new C1(k,k,k,k,k)}static file(t){let r=k;if(J&&(t=t.replace(/\\/g,G)),t[0]===G&&t[1]===G){const i=t.indexOf(G,2);i===-1?(r=t.substring(2),t=G):(r=t.substring(2,i),t=t.substring(i)||G)}return new C1("file",r,t,k,k)}static from(t,r){return new C1(t.scheme,t.authority,t.path,t.query,t.fragment,r)}static joinPath(t,...r){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let i;return J&&t.scheme==="file"?i=de.file(R.join(se(t,!0),...r)).path:i=x.join(t.path,...r),t.with({path:i})}toString(t=!1){return Ie(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof de)return t;{const r=new C1(t);return r._formatted=t.external??null,r._fsPath=t._sep===Ct?t.fsPath??null:null,r}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},Ct=J?1:void 0,C1=class extends B{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=se(this,!1)),this._fsPath}toString(e=!1){return e?Ie(this,!0):(this._formatted||(this._formatted=Ie(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=Ct),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},Et={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function _t(e,t,r){let i,n=-1;for(let s=0;s<e.length;s++){const o=e.charCodeAt(s);if(o>=97&&o<=122||o>=65&&o<=90||o>=48&&o<=57||o===45||o===46||o===95||o===126||t&&o===47||r&&o===91||r&&o===93||r&&o===58)n!==-1&&(i+=encodeURIComponent(e.substring(n,s)),n=-1),i!==void 0&&(i+=e.charAt(s));else{i===void 0&&(i=e.substr(0,s));const a=Et[o];a!==void 0?(n!==-1&&(i+=encodeURIComponent(e.substring(n,s)),n=-1),i+=a):n===-1&&(n=s)}}return n!==-1&&(i+=encodeURIComponent(e.substring(n))),i!==void 0?i:e}function Z2(e){let t;for(let r=0;r<e.length;r++){const i=e.charCodeAt(r);i===35||i===63?(t===void 0&&(t=e.substr(0,r)),t+=Et[i]):t!==void 0&&(t+=e[r])}return t!==void 0?t:e}function se(e,t){let r;return e.authority&&e.path.length>1&&e.scheme==="file"?r=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?r=e.path.substr(1):r=e.path[1].toLowerCase()+e.path.substr(2):r=e.path,J&&(r=r.replace(/\//g,"\\")),r}function Ie(e,t){const r=t?Z2:_t;let i="",{scheme:n,authority:s,path:o,query:a,fragment:l}=e;if(n&&(i+=n,i+=":"),(s||n==="file")&&(i+=G,i+=G),s){let c=s.indexOf("@");if(c!==-1){const u=s.substr(0,c);s=s.substr(c+1),c=u.lastIndexOf(":"),c===-1?i+=r(u,!1,!1):(i+=r(u.substr(0,c),!1,!1),i+=":",i+=r(u.substr(c+1),!1,!0)),i+="@"}s=s.toLowerCase(),c=s.lastIndexOf(":"),c===-1?i+=r(s,!1,!0):(i+=r(s.substr(0,c),!1,!0),i+=s.substr(c))}if(o){if(o.length>=3&&o.charCodeAt(0)===47&&o.charCodeAt(2)===58){const c=o.charCodeAt(1);c>=65&&c<=90&&(o=`/${String.fromCharCode(c+32)}:${o.substr(3)}`)}else if(o.length>=2&&o.charCodeAt(1)===58){const c=o.charCodeAt(0);c>=65&&c<=90&&(o=`${String.fromCharCode(c+32)}:${o.substr(2)}`)}i+=r(o,!0,!1)}return a&&(i+="?",i+=r(a,!1,!1)),l&&(i+="#",i+=t?l:_t(l,!1,!1)),i}function St(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+St(e.substr(3)):e}}var Pt=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function oe(e){return e.match(Pt)?e.replace(Pt,t=>St(t)):e}var N;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatEditor="vscode-chat-editor",e.vscodeChatInput="chatSessionInput",e.vscodeChatSession="vscode-chat-session",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(N||(N={}));var G2="tkn",Y2=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=x.join(t??"/",ei(e))}getServerRootPath(){return this.f}get g(){return x.join(this.f,N.vscodeRemoteResource)}set(e,t,r){this.a[e]=t,this.b[e]=r}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(o){return L1(o),e}const t=e.authority;let r=this.a[t];r&&r.indexOf(":")!==-1&&r.indexOf("[")===-1&&(r=`[${r}]`);const i=this.b[t],n=this.c[t];let s=`path=${encodeURIComponent(e.path)}`;return typeof n=="string"&&(s+=`&${G2}=${encodeURIComponent(n)}`),B.from({scheme:re?this.d:N.vscodeRemoteResource,authority:`${r}:${i}`,path:this.g,query:s})}},X2=new Y2;function ei(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var ti="vs/../../node_modules",ri="vs/../../node_modules.asar",Lt="vscode-app",ii=class ge{static{this.a=Lt}asBrowserUri(t){const r=this.b(t);return this.uriToBrowserUri(r)}uriToBrowserUri(t){return t.scheme===N.vscodeRemote?X2.rewrite(t):t.scheme===N.file&&(c2||f2===`${N.vscodeFileResource}://${ge.a}`)?t.with({scheme:N.vscodeFileResource,authority:t.authority||ge.a,query:null,fragment:null}):t}asFileUri(t){const r=this.b(t);return this.uriToFileUri(r)}uriToFileUri(t){return t.scheme===N.vscodeFileResource?t.with({scheme:N.file,authority:t.authority!==ge.a?t.authority:null,query:null,fragment:null}):t}b(t){if(B.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const r=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(r))return B.joinPath(B.parse(r,!0),t);const i=g1(r,t);return B.file(i)}throw new Error("Cannot determine URI for module id!")}},ni=new ii,zn=Object.freeze({"Cache-Control":"no-cache, no-store"}),Fn=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),Ot;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const r="vscode-coi";function i(s){let o;typeof s=="string"?o=new URL(s).searchParams:s instanceof URL?o=s.searchParams:B.isUri(s)&&(o=new URL(s.toString(!0)).searchParams);const a=o?.get(r);if(a)return t.get(a)}e.getHeadersFromQuery=i;function n(s,o,a){if(!globalThis.crossOriginIsolated)return;const l=o&&a?"3":a?"2":"1";s instanceof URLSearchParams?s.set(r,l):s[r]=l}e.addSearchParam=n})(Ot||(Ot={}));function n1(e){return se(e,!0)}var Re=class{constructor(e){this.a=e}compare(e,t,r=!1){return e===t?0:x2(this.getComparisonKey(e,r),this.getComparisonKey(t,r))}isEqual(e,t,r=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,r)===this.getComparisonKey(t,r)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,r=!1){if(e.scheme===t.scheme){if(e.scheme===N.file)return De(n1(e),n1(t),this.a(e))&&e.query===t.query&&(r||e.fragment===t.fragment);if(xt(e.authority,t.authority))return De(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(r||e.fragment===t.fragment)}return!1}joinPath(e,...t){return B.joinPath(e,...t)}basenameOrAuthority(e){return oi(e)||e.authority}basename(e){return x.basename(e.path)}extname(e){return x.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===N.file?t=B.file(S2(n1(e))).path:(t=x.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===N.file?t=B.file(xe(n1(e))).path:t=x.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!xt(e.authority,t.authority))return;if(e.scheme===N.file){const n=_2(n1(e),n1(t));return J?yt(n):n}let r=e.path||"/";const i=t.path||"/";if(this.a(e)){let n=0;for(const s=Math.min(r.length,i.length);n<s&&!(r.charCodeAt(n)!==i.charCodeAt(n)&&r.charAt(n).toLowerCase()!==i.charAt(n).toLowerCase());n++);r=i.substr(0,n)+r.substr(n)}return x.relative(r,i)}resolvePath(e,t){if(e.scheme===N.file){const r=B.file(E2(n1(e),t));return e.with({authority:r.authority,path:r.path})}return t=M2(t),e.with({path:x.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&D2(e,t)}hasTrailingPathSeparator(e,t=R1){if(e.scheme===N.file){const r=n1(e);return r.length>bt(r).length&&r[r.length-1]===t}else{const r=e.path;return r.length>1&&r.charCodeAt(r.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=R1){return kt(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=R1){let r=!1;if(e.scheme===N.file){const i=n1(e);r=i!==void 0&&i.length===bt(i).length&&i[i.length-1]===t}else{t="/";const i=e.path;r=i.length===1&&i.charCodeAt(i.length-1)===47}return!r&&!kt(e,t)?e.with({path:e.path+"/"}):e}},S=new Re(()=>!1),si=new Re(e=>e.scheme===N.file?!lt:!0),qn=new Re(e=>!0),Wn=S.isEqual.bind(S),Vn=S.isEqualOrParent.bind(S),Bn=S.getComparisonKey.bind(S),Hn=S.basenameOrAuthority.bind(S),oi=S.basename.bind(S),Kn=S.extname.bind(S),Jn=S.dirname.bind(S),Qn=S.joinPath.bind(S),Zn=S.normalizePath.bind(S),Gn=S.relativePath.bind(S),Yn=S.resolvePath.bind(S),Xn=S.isAbsolutePath.bind(S),xt=S.isEqualAuthority.bind(S),kt=S.hasTrailingPathSeparator.bind(S),e0=S.removeTrailingPathSeparator.bind(S),t0=S.addTrailingPathSeparator.bind(S),Dt;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(r){const i=new Map;r.path.substring(r.path.indexOf(";")+1,r.path.lastIndexOf(";")).split(";").forEach(o=>{const[a,l]=o.split(":");a&&l&&i.set(a,l)});const s=r.path.substring(0,r.path.indexOf(";"));return s&&i.set(e.META_DATA_MIME,s),i}e.parseMetaData=t})(Dt||(Dt={}));var r0=Symbol("MicrotaskDelay");function Ne(e){const t=new rt,r=e(t.token);let i=!1;const n=new Promise((s,o)=>{const a=t.token.onCancellationRequested(()=>{i=!0,a.dispose(),o(new a1)});Promise.resolve(r).then(l=>{a.dispose(),t.dispose(),i?Tr(l)&&l.dispose():s(l)},l=>{a.dispose(),t.dispose(),o(l)})});return new class{cancel(){t.cancel(),t.dispose()}then(s,o){return n.then(s,o)}catch(s){return this.then(void 0,s)}finally(s){return n.finally(s)}}}function It(e,t){return t?new Promise((r,i)=>{const n=setTimeout(()=>{s.dispose(),r()},e),s=t.onCancellationRequested(()=>{clearTimeout(n),s.dispose(),i(new a1)})}):Ne(r=>It(e,r))}var ai=class{constructor(e){this.a=0,this.b=!1,this.f=e,this.g=[],this.d=0,this.h=new K}whenIdle(){return this.size>0?Z.toPromise(this.onDrained):Promise.resolve()}get onDrained(){return this.h.event}get size(){return this.a}queue(e){if(this.b)throw new Error("Object has been disposed");return this.a++,new Promise((t,r)=>{this.g.push({factory:e,c:t,e:r}),this.j()})}j(){for(;this.g.length&&this.d<this.f;){const e=this.g.shift();this.d++;const t=e.factory();t.then(e.c,e.e),t.then(()=>this.k(),()=>this.k())}}k(){this.b||(this.d--,--this.a===0&&this.h.fire(),this.g.length>0&&this.j())}clear(){if(this.b)throw new Error("Object has been disposed");this.g.length=0,this.a=this.d}dispose(){this.b=!0,this.g.length=0,this.a=0,this.h.dispose()}},ci=class extends ai{constructor(){super(1)}},li=class{constructor(){this.a=new Map,this.b=new Set,this.d=void 0,this.f=0}async whenDrained(){if(this.g())return;const e=new ui;return this.b.add(e),e.p}g(){for(const[,e]of this.a)if(e.size>0)return!1;return!0}queueSize(e,t=S){const r=t.getComparisonKey(e);return this.a.get(r)?.size??0}queueFor(e,t,r=S){const i=r.getComparisonKey(e);let n=this.a.get(i);if(!n){n=new ci;const s=this.f++,o=Z.once(n.onDrained)(()=>{n?.dispose(),this.a.delete(i),this.h(),this.d?.deleteAndDispose(s),this.d?.size===0&&(this.d.dispose(),this.d=void 0)});this.d||(this.d=new Ur),this.d.set(s,o),this.a.set(i,n)}return n.queue(t)}h(){this.g()&&this.j()}j(){for(const e of this.b)e.complete();this.b.clear()}dispose(){for(const[,e]of this.a)e.dispose();this.a.clear(),this.j(),this.d?.dispose()}},fi,je;(function(){const e=globalThis;typeof e.requestIdleCallback!="function"||typeof e.cancelIdleCallback!="function"?je=(t,r,i)=>{h2(()=>{if(n)return;const s=Date.now()+15;r(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,s-Date.now())}}))});let n=!1;return{dispose(){n||(n=!0)}}}:je=(t,r,i)=>{const n=t.requestIdleCallback(r,typeof i=="number"?{timeout:i}:void 0);let s=!1;return{dispose(){s||(s=!0,t.cancelIdleCallback(n))}}},fi=(t,r)=>je(globalThis,t,r)})();var Rt;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})(Rt||(Rt={}));var ui=class{get isRejected(){return this.d?.outcome===1}get isResolved(){return this.d?.outcome===0}get isSettled(){return!!this.d}get value(){return this.d?.outcome===0?this.d?.value:void 0}constructor(){this.p=new Promise((e,t)=>{this.a=e,this.b=t})}complete(e){return new Promise(t=>{this.a(e),this.d={outcome:0,value:e},t()})}error(e){return new Promise(t=>{this.b(e),this.d={outcome:1,value:e},t()})}settleWith(e){return e.then(t=>this.complete(t),t=>this.error(t))}cancel(){return this.error(new a1)}},Nt;(function(e){async function t(i){let n;const s=await Promise.all(i.map(o=>o.then(a=>a,a=>{n||(n=a)})));if(typeof n<"u")throw n;return s}e.settled=t;function r(i){return new Promise(async(n,s)=>{try{await i(n,s)}catch(o){s(o)}})}e.withAsyncBody=r})(Nt||(Nt={}));var jt;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})(jt||(jt={}));var i0=class H{static fromArray(t){return new H(r=>{r.emitMany(t)})}static fromPromise(t){return new H(async r=>{r.emitMany(await t)})}static fromPromisesResolveOrder(t){return new H(async r=>{await Promise.all(t.map(async i=>r.emitOne(await i)))})}static merge(t){return new H(async r=>{await Promise.all(t.map(async i=>{for await(const n of i)r.emitOne(n)}))})}static{this.EMPTY=H.fromArray([])}constructor(t,r){this.a=0,this.b=[],this.d=null,this.f=r,this.g=new K,queueMicrotask(async()=>{const i={emitOne:n=>this.h(n),emitMany:n=>this.j(n),reject:n=>this.l(n)};try{await Promise.resolve(t(i)),this.k()}catch(n){this.l(n)}finally{i.emitOne=void 0,i.emitMany=void 0,i.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await Z.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,r){return new H(async i=>{for await(const n of t)i.emitOne(r(n))})}map(t){return H.map(this,t)}static filter(t,r){return new H(async i=>{for await(const n of t)r(n)&&i.emitOne(n)})}filter(t){return H.filter(this,t)}static coalesce(t){return H.filter(t,r=>!!r)}coalesce(){return H.coalesce(this)}static async toPromise(t){const r=[];for await(const i of t)r.push(i);return r}toPromise(){return H.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}},n0=Symbol("AsyncReaderEndOfStream");function hi(e,t){return new Promise((r,i)=>{const n=[];di(e,{onData:s=>{t&&n.push(s)},onError:s=>{t?i(s):r(void 0)},onEnd:()=>{r(t?t(n):void 0)}})})}function di(e,t,r){e.on("error",i=>{r?.isCancellationRequested||t.onError(i)}),e.on("end",()=>{r?.isCancellationRequested||t.onEnd()}),e.on("data",i=>{r?.isCancellationRequested||t.onData(i)})}var N1=typeof Buffer<"u",gi=new ke(()=>new Uint8Array(256)),Te,Me,Y=class Q{static alloc(t){return N1?new Q(Buffer.allocUnsafe(t)):new Q(new Uint8Array(t))}static wrap(t){return N1&&!Buffer.isBuffer(t)&&(t=Buffer.from(t.buffer,t.byteOffset,t.byteLength)),new Q(t)}static fromString(t,r){return!(r?.dontUseNodeBuffer||!1)&&N1?new Q(Buffer.from(t)):(Te||(Te=new TextEncoder),new Q(Te.encode(t)))}static fromByteArray(t){const r=Q.alloc(t.length);for(let i=0,n=t.length;i<n;i++)r.buffer[i]=t[i];return r}static concat(t,r){if(typeof r>"u"){r=0;for(let s=0,o=t.length;s<o;s++)r+=t[s].byteLength}const i=Q.alloc(r);let n=0;for(let s=0,o=t.length;s<o;s++){const a=t[s];i.set(a,n),n+=a.byteLength}return i}static isNativeBuffer(t){return N1&&Buffer.isBuffer(t)}constructor(t){this.buffer=t,this.byteLength=this.buffer.byteLength}clone(){const t=Q.alloc(this.byteLength);return t.set(this),t}toString(){return N1?this.buffer.toString():(Me||(Me=new TextDecoder),Me.decode(this.buffer))}slice(t,r){return new Q(this.buffer.subarray(t,r))}set(t,r){if(t instanceof Q)this.buffer.set(t.buffer,r);else if(t instanceof Uint8Array)this.buffer.set(t,r);else if(t instanceof ArrayBuffer)this.buffer.set(new Uint8Array(t),r);else if(ArrayBuffer.isView(t))this.buffer.set(new Uint8Array(t.buffer,t.byteOffset,t.byteLength),r);else throw new Error("Unknown argument 'array'")}readUInt32BE(t){return mi(this.buffer,t)}writeUInt32BE(t,r){pi(this.buffer,t,r)}readUInt32LE(t){return wi(this.buffer,t)}writeUInt32LE(t,r){yi(this.buffer,t,r)}readUInt8(t){return bi(this.buffer,t)}writeUInt8(t,r){Ai(this.buffer,t,r)}indexOf(t,r=0){return vi(this.buffer,t instanceof Q?t.buffer:t,r)}equals(t){return this===t?!0:this.byteLength!==t.byteLength?!1:this.buffer.every((r,i)=>r===t.buffer[i])}};function vi(e,t,r=0){const i=t.byteLength,n=e.byteLength;if(i===0)return 0;if(i===1)return e.indexOf(t[0]);if(i>n-r)return-1;const s=gi.value;s.fill(t.length);for(let c=0;c<t.length;c++)s[t[c]]=t.length-c-1;let o=r+t.length-1,a=o,l=-1;for(;o<n;)if(e[o]===t[a]){if(a===0){l=o;break}o--,a--}else o+=Math.max(t.length-a,s[e[o]]),a=t.length-1;return l}function mi(e,t){return e[t]*2**24+e[t+1]*2**16+e[t+2]*2**8+e[t+3]}function pi(e,t,r){e[r+3]=t,t=t>>>8,e[r+2]=t,t=t>>>8,e[r+1]=t,t=t>>>8,e[r]=t}function wi(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0|e[t+2]<<16>>>0|e[t+3]<<24>>>0}function yi(e,t,r){e[r+0]=t&255,t=t>>>8,e[r+1]=t&255,t=t>>>8,e[r+2]=t&255,t=t>>>8,e[r+3]=t&255}function bi(e,t){return e[t]}function Ai(e,t,r){e[r]=t}function $i(e){return hi(e,t=>Y.concat(t))}function Tt(e,t,r=!0){return y1(e)?(y1(t)&&Object.keys(t).forEach(i=>{i in e?r&&(y1(e[i])&&y1(t[i])?Tt(e[i],t[i],r):e[i]=t[i]):e[i]=t[i]}),e):t}function Mt(e){const t=new Set;return JSON.stringify(e,(r,i)=>{if(y1(i)||Array.isArray(i)){if(t.has(i))return"[Circular]";t.add(i)}return typeof i=="bigint"?`[BigInt ${i.toString()}]`:i})}import"child_process";import"fs";var Ut;(function(e){e[e.stdout=0]="stdout",e[e.stderr=1]="stderr"})(Ut||(Ut={}));var zt;(function(e){e[e.Success=0]="Success",e[e.Unknown=1]="Unknown",e[e.AccessDenied=2]="AccessDenied",e[e.ProcessNotFound=3]="ProcessNotFound"})(zt||(zt={}));import*as O from"fs";import{tmpdir as Ci}from"os";import{promisify as j1}from"util";var Ei=new Ye(1e4);function Ft(e){return Si(e,"NFC",Ei)}var f0=new Ye(1e4),_i=/[^\u0000-\u0080]/;function Si(e,t,r){if(!e)return e;const i=r.get(e);if(i)return i;let n;return _i.test(e)?n=e.normalize(t):n=e,r.set(e,n),n}var T1;(function(e){e[e.UNLINK=0]="UNLINK",e[e.MOVE=1]="MOVE"})(T1||(T1={}));async function qt(e,t=T1.UNLINK,r){if($t(e))throw new Error("rimraf - will refuse to recursively delete root");return t===T1.UNLINK?Ue(e):Pi(e,r)}async function Pi(e,t=q2(Ci())){try{try{await O.promises.rename(e,t)}catch(r){return r.code==="ENOENT"?void 0:Ue(e)}Ue(t).catch(r=>{})}catch(r){if(r.code!=="ENOENT")throw r}}async function Ue(e){return O.promises.rm(e,{recursive:!0,force:!0,maxRetries:3})}async function ae(e,t){try{return await Wt(e,t)}catch(r){if(r.code==="ENOENT"&&J&&$t(e))try{return await Wt(e.slice(0,-1),t)}catch{}throw r}}async function Wt(e,t){return Oi(await(t?Li(e):O.promises.readdir(e)))}async function Li(e){try{return await O.promises.readdir(e,{withFileTypes:!0})}catch(i){console.warn("[node.js fs] readdir with filetypes failed with error: ",i)}const t=[],r=await ae(e);for(const i of r){let n=!1,s=!1,o=!1;try{const a=await O.promises.lstat(g1(e,i));n=a.isFile(),s=a.isDirectory(),o=a.isSymbolicLink()}catch(a){console.warn("[node.js fs] unexpected error from lstat after readdir: ",a)}t.push({name:i,isFile:()=>n,isDirectory:()=>s,isSymbolicLink:()=>o})}return t}function Oi(e){return e.map(t=>typeof t=="string"?Pe?Ft(t):t:(t.name=Pe?Ft(t.name):t.name,t))}async function xi(e){const t=await ae(e),r=[];for(const i of t)await M1.existsDirectory(g1(e,i))&&r.push(i);return r}var M1;(function(e){async function t(n){let s;try{if(s=await O.promises.lstat(n),!s.isSymbolicLink())return{stat:s}}catch{}try{return{stat:await O.promises.stat(n),symbolicLink:s?.isSymbolicLink()?{dangling:!1}:void 0}}catch(o){if(o.code==="ENOENT"&&s)return{stat:s,symbolicLink:{dangling:!0}};if(J&&o.code==="EACCES")try{return{stat:await O.promises.stat(await O.promises.readlink(n)),symbolicLink:{dangling:!1}}}catch(a){if(a.code==="ENOENT"&&s)return{stat:s,symbolicLink:{dangling:!0}};throw a}throw o}}e.stat=t;async function r(n){try{const{stat:s,symbolicLink:o}=await e.stat(n);return s.isFile()&&o?.dangling!==!0}catch{}return!1}e.existsFile=r;async function i(n){try{const{stat:s,symbolicLink:o}=await e.stat(n);return s.isDirectory()&&o?.dangling!==!0}catch{}return!1}e.existsDirectory=i})(M1||(M1={}));var ki=new li;function Di(e,t,r){return ki.queueFor(B.file(e),()=>{const i=Ni(r);return new Promise((n,s)=>Ri(e,t,i,o=>o?s(o):n()))},si)}var Vt=!0;function Ii(e){Vt=e}function Ri(e,t,r,i){if(!Vt)return O.writeFile(e,t,{mode:r.mode,flag:r.flag},i);O.open(e,r.flag,r.mode,(n,s)=>{if(n)return i(n);O.writeFile(s,t,o=>{if(o)return O.close(s,()=>i(o));O.fdatasync(s,a=>(a&&(console.warn("[node.js fs] fdatasync is now disabled for this session because it failed: ",a),Ii(!1)),O.close(s,l=>i(l))))})})}function Ni(e){return e?{mode:typeof e.mode=="number"?e.mode:438,flag:typeof e.flag=="string"?e.flag:"w"}:{mode:438,flag:"w"}}async function ji(e,t,r=6e4){if(e!==t)try{J&&typeof r=="number"?await Bt(e,t,Date.now(),r):await O.promises.rename(e,t)}catch(i){if(e.toLowerCase()!==t.toLowerCase()&&i.code==="EXDEV"||e.endsWith("."))await Ht(e,t,{preserveSymlinks:!1}),await qt(e,T1.MOVE);else throw i}}async function Bt(e,t,r,i,n=0){try{return await O.promises.rename(e,t)}catch(s){if(s.code!=="EACCES"&&s.code!=="EPERM"&&s.code!=="EBUSY")throw s;if(Date.now()-r>=i)throw console.error(`[node.js fs] rename failed after ${n} retries with error: ${s}`),s;if(n===0){let o=!1;try{const{stat:a}=await M1.stat(t);a.isFile()||(o=!0)}catch{}if(o)throw s}return await It(Math.min(100,n*10)),Bt(e,t,r,i,n+1)}}async function Ht(e,t,r){return Jt(e,t,{root:{source:e,target:t},options:r,handledSourcePaths:new Set})}var Kt=511;async function Jt(e,t,r){if(r.handledSourcePaths.has(e))return;r.handledSourcePaths.add(e);const{stat:i,symbolicLink:n}=await M1.stat(e);if(n){if(r.options.preserveSymlinks)try{return await Ui(e,t,r)}catch{}if(n.dangling)return}return i.isDirectory()?Ti(e,t,i.mode&Kt,r):Mi(e,t,i.mode&Kt)}async function Ti(e,t,r,i){await O.promises.mkdir(t,{recursive:!0,mode:r});const n=await ae(e);for(const s of n)await Jt(g1(e,s),g1(t,s),i)}async function Mi(e,t,r){await O.promises.copyFile(e,t),await O.promises.chmod(t,r)}async function Ui(e,t,r){let i=await O.promises.readlink(e);De(i,r.root.source,!lt)&&(i=g1(r.root.target,i.substr(r.root.source.length+1))),await O.promises.symlink(i,t)}async function zi(e){try{return await j1(O.realpath)(e)}catch{const r=Fi(e);return await O.promises.access(r,O.constants.R_OK),r}}function Fi(e){return O2(xe(e),R1)}var u0=new class{get read(){return(e,t,r,i,n)=>new Promise((s,o)=>{O.read(e,t,r,i,n,(a,l,c)=>a?o(a):s({bytesRead:l,buffer:c}))})}get write(){return(e,t,r,i,n)=>new Promise((s,o)=>{O.write(e,t,r,i,n,(a,l,c)=>a?o(a):s({bytesWritten:l,buffer:c}))})}get fdatasync(){return j1(O.fdatasync)}get open(){return j1(O.open)}get close(){return j1(O.close)}get ftruncate(){return j1(O.ftruncate)}async exists(e){try{return await O.promises.access(e),!0}catch{return!1}}get readdir(){return ae}get readDirsInDir(){return xi}get writeFile(){return Di}get rm(){return qt}get rename(){return ji}get copy(){return Ht}get realpath(){return zi}};function qi(e,t,r){let i=null,n=null;if(typeof r.value=="function"?(i="value",n=r.value,n.length!==0&&console.warn("Memoize should only be used in functions with zero parameters")):typeof r.get=="function"&&(i="get",n=r.get),!n)throw new Error("not supported");const s=`$memoize$${t}`;r[i]=function(...o){return this.hasOwnProperty(s)||Object.defineProperty(this,s,{configurable:!1,enumerable:!1,writable:!1,value:n.apply(this,o)}),this[s]}}function ce(e,t=0){if(!e||t>200)return e;if(typeof e=="object"){switch(e.$mid){case 1:return B.revive(e);case 2:return new RegExp(e.source,e.flags);case 17:return new Date(e.source)}if(e instanceof Y||e instanceof Uint8Array)return e;if(Array.isArray(e))for(let r=0;r<e.length;++r)e[r]=ce(e[r],t+1);else for(const r in e)Object.hasOwnProperty.call(e,r)&&(e[r]=ce(e[r],t+1))}return e}var Qt;(function(e){e[e.Promise=100]="Promise",e[e.PromiseCancel=101]="PromiseCancel",e[e.EventListen=102]="EventListen",e[e.EventDispose=103]="EventDispose"})(Qt||(Qt={}));function E1(e){switch(e){case 100:return"req";case 101:return"cancel";case 102:return"subscribe";case 103:return"unsubscribe"}}var Zt;(function(e){e[e.Initialize=200]="Initialize",e[e.PromiseSuccess=201]="PromiseSuccess",e[e.PromiseError=202]="PromiseError",e[e.PromiseErrorObj=203]="PromiseErrorObj",e[e.EventFire=204]="EventFire"})(Zt||(Zt={}));function le(e){switch(e){case 200:return"init";case 201:return"reply:";case 202:case 203:return"replyErr:";case 204:return"event:"}}var v1;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Idle=1]="Idle"})(v1||(v1={}));function _1(e){let t=0;for(let r=0;;r+=7){const i=e.read(1);if(t|=(i.buffer[0]&127)<<r,!(i.buffer[0]&128))return t}}var Wi=f1(0);function S1(e,t){if(t===0){e.write(Wi);return}let r=0;for(let n=t;n!==0;n=n>>>7)r++;const i=Y.alloc(r);for(let n=0;t!==0;n++)i.buffer[n]=t&127,t=t>>>7,t>0&&(i.buffer[n]|=128);e.write(i)}var Gt=class{constructor(e){this.b=e,this.a=0}read(e){const t=this.b.slice(this.a,this.a+e);return this.a+=t.byteLength,t}},Yt=class{constructor(){this.a=[]}get buffer(){return Y.concat(this.a)}write(e){this.a.push(e)}},F;(function(e){e[e.Undefined=0]="Undefined",e[e.String=1]="String",e[e.Buffer=2]="Buffer",e[e.VSBuffer=3]="VSBuffer",e[e.Array=4]="Array",e[e.Object=5]="Object",e[e.Int=6]="Int"})(F||(F={}));function f1(e){const t=Y.alloc(1);return t.writeUInt8(e,0),t}var m1={Undefined:f1(F.Undefined),String:f1(F.String),Buffer:f1(F.Buffer),VSBuffer:f1(F.VSBuffer),Array:f1(F.Array),Object:f1(F.Object),Uint:f1(F.Int)};function U1(e,t){if(typeof t>"u")e.write(m1.Undefined);else if(typeof t=="string"){const r=Y.fromString(t);e.write(m1.String),S1(e,r.byteLength),e.write(r)}else if(Y.isNativeBuffer(t)){const r=Y.wrap(t);e.write(m1.Buffer),S1(e,r.byteLength),e.write(r)}else if(t instanceof Y)e.write(m1.VSBuffer),S1(e,t.byteLength),e.write(t);else if(Array.isArray(t)){e.write(m1.Array),S1(e,t.length);for(const r of t)U1(e,r)}else if(typeof t=="number"&&(t|0)===t)e.write(m1.Uint),S1(e,t);else{const r=Y.fromString(JSON.stringify(t));e.write(m1.Object),S1(e,r.byteLength),e.write(r)}}function z1(e){switch(e.read(1).readUInt8(0)){case F.Undefined:return;case F.String:return e.read(_1(e)).toString();case F.Buffer:return e.read(_1(e)).buffer;case F.VSBuffer:return e.read(_1(e));case F.Array:{const r=_1(e),i=[];for(let n=0;n<r;n++)i.push(z1(e));return i}case F.Object:return JSON.parse(e.read(_1(e)).toString());case F.Int:return _1(e)}}var Vi=class{constructor(e,t,r=null,i=1e3){this.h=e,this.j=t,this.k=r,this.l=i,this.b=new Map,this.d=new Map,this.g=new Map,this.f=this.h.onMessage(n=>this.q(n)),this.m({type:200})}registerChannel(e,t){this.b.set(e,t),setTimeout(()=>this.w(e),0)}m(e){switch(e.type){case 200:{const t=this.o([e.type]);this.k?.logOutgoing(t,0,1,le(e.type));return}case 201:case 202:case 204:case 203:{const t=this.o([e.type,e.id],e.data);this.k?.logOutgoing(t,e.id,1,le(e.type),e.data);return}}}o(e,t=void 0){const r=new Yt;return U1(r,e),U1(r,t),this.p(r.buffer)}p(e){try{return this.h.send(e),e.byteLength}catch{return 0}}q(e){const t=new Gt(e),r=z1(t),i=z1(t),n=r[0];switch(n){case 100:return this.k?.logIncoming(e.byteLength,r[1],1,`${E1(n)}: ${r[2]}.${r[3]}`,i),this.s({type:n,id:r[1],channelName:r[2],name:r[3],arg:i});case 102:return this.k?.logIncoming(e.byteLength,r[1],1,`${E1(n)}: ${r[2]}.${r[3]}`,i),this.t({type:n,id:r[1],channelName:r[2],name:r[3],arg:i});case 101:return this.k?.logIncoming(e.byteLength,r[1],1,`${E1(n)}`),this.u({type:n,id:r[1]});case 103:return this.k?.logIncoming(e.byteLength,r[1],1,`${E1(n)}`),this.u({type:n,id:r[1]})}}s(e){const t=this.b.get(e.channelName);if(!t){this.v(e);return}const r=new rt;let i;try{i=t.call(this.j,e.name,e.arg,r.token)}catch(o){i=Promise.reject(o)}const n=e.id;i.then(o=>{this.m({id:n,data:o,type:201})},o=>{o instanceof Error?this.m({id:n,data:{message:o.message,name:o.name,stack:o.stack?o.stack.split(`
`):void 0},type:202}):this.m({id:n,data:o,type:203})}).finally(()=>{s.dispose(),this.d.delete(e.id)});const s=$e(()=>r.cancel());this.d.set(e.id,s)}t(e){const t=this.b.get(e.channelName);if(!t){this.v(e);return}const r=e.id,n=t.listen(this.j,e.name,e.arg)(s=>this.m({id:r,data:s,type:204}));this.d.set(e.id,n)}u(e){const t=this.d.get(e.id);t&&(t.dispose(),this.d.delete(e.id))}v(e){let t=this.g.get(e.channelName);t||(t=[],this.g.set(e.channelName,t));const r=setTimeout(()=>{console.error(`Unknown channel: ${e.channelName}`),e.type===100&&this.m({id:e.id,data:{name:"Unknown channel",message:`Channel name '${e.channelName}' timed out after ${this.l}ms`,stack:void 0},type:202})},this.l);t.push({request:e,timeoutTimer:r})}w(e){const t=this.g.get(e);if(t){for(const r of t)switch(clearTimeout(r.timeoutTimer),r.request.type){case 100:this.s(r.request);break;case 102:this.t(r.request);break}this.g.delete(e)}}dispose(){this.f&&(this.f.dispose(),this.f=null),k1(this.d.values()),this.d.clear()}},Xt;(function(e){e[e.LocalSide=0]="LocalSide",e[e.OtherSide=1]="OtherSide"})(Xt||(Xt={}));var Bi=class{constructor(e,t=null){this.l=e,this.a=!1,this.b=v1.Uninitialized,this.d=new Set,this.f=new Map,this.g=0,this.k=new K,this.onDidInitialize=this.k.event,this.h=this.l.onMessage(r=>this.s(r)),this.j=t}getChannel(e){const t=this;return{call(r,i,n){return t.a?Promise.reject(new a1):t.m(e,r,i,n)},listen(r,i){return t.a?Z.None:t.o(e,r,i)}}}m(e,t,r,i=A1.None){const n=this.g++,o={id:n,type:100,channelName:e,name:t,arg:r};if(i.isCancellationRequested)return Promise.reject(new a1);let a,l;return new Promise((u,f)=>{if(i.isCancellationRequested)return f(new a1);const d=()=>{const _=E=>{switch(E.type){case 201:this.f.delete(n),u(E.data);break;case 202:{this.f.delete(n);const I=new Error(E.data.message);I.stack=Array.isArray(E.data.stack)?E.data.stack.join(`
`):E.data.stack,I.name=E.data.name,f(I);break}case 203:this.f.delete(n),f(E.data);break}};this.f.set(n,_),this.p(o)};let p=null;this.b===v1.Idle?d():(p=Ne(_=>this.u()),p.then(()=>{p=null,d()}));const $=()=>{p?(p.cancel(),p=null):this.p({id:n,type:101}),f(new a1)};a=i.onCancellationRequested($),l={dispose:He(()=>{$(),a.dispose()})},this.d.add(l)}).finally(()=>{a?.dispose(),this.d.delete(l)})}o(e,t,r){const i=this.g++,s={id:i,type:102,channelName:e,name:t,arg:r};let o=null;const a=new K({onWillAddFirstListener:()=>{const c=()=>{this.d.add(a),this.p(s)};this.b===v1.Idle?c():(o=Ne(u=>this.u()),o.then(()=>{o=null,c()}))},onDidRemoveLastListener:()=>{o?(o.cancel(),o=null):(this.d.delete(a),this.p({id:i,type:103}))}}),l=c=>a.fire(c.data);return this.f.set(i,l),a.event}p(e){switch(e.type){case 100:case 102:{const t=this.q([e.type,e.id,e.channelName,e.name],e.arg);this.j?.logOutgoing(t,e.id,0,`${E1(e.type)}: ${e.channelName}.${e.name}`,e.arg);return}case 101:case 103:{const t=this.q([e.type,e.id]);this.j?.logOutgoing(t,e.id,0,E1(e.type));return}}}q(e,t=void 0){const r=new Yt;return U1(r,e),U1(r,t),this.r(r.buffer)}r(e){try{return this.l.send(e),e.byteLength}catch{return 0}}s(e){const t=new Gt(e),r=z1(t),i=z1(t),n=r[0];switch(n){case 200:return this.j?.logIncoming(e.byteLength,0,0,le(n)),this.t({type:r[0]});case 201:case 202:case 204:case 203:return this.j?.logIncoming(e.byteLength,r[1],0,le(n),i),this.t({type:r[0],id:r[1],data:i})}}t(e){if(e.type===200){this.b=v1.Idle,this.k.fire();return}this.f.get(e.id)?.(e)}get onDidInitializePromise(){return Z.toPromise(this.onDidInitialize)}u(){return this.b===v1.Idle?Promise.resolve():this.onDidInitializePromise}dispose(){this.a=!0,this.h&&(this.h.dispose(),this.h=null),k1(this.d.values()),this.d.clear()}};__decorate([qi],Bi.prototype,"onDidInitializePromise",null);var er;(function(e){function t(s,o,a){const l=s,c=a&&a.disableMarshalling,u=new Map;for(const f in l)i(f)&&u.set(f,Z.buffer(l[f],!0,void 0,o));return new class{listen(f,d,p){const $=u.get(d);if($)return $;const _=l[d];if(typeof _=="function"){if(n(d))return _.call(l,p);if(i(d))return u.set(d,Z.buffer(l[d],!0,void 0,o)),u.get(d)}throw new O1(`Event not found: ${d}`)}call(f,d,p){const $=l[d];if(typeof $=="function"){if(!c&&Array.isArray(p))for(let E=0;E<p.length;E++)p[E]=ce(p[E]);let _=$.apply(l,p);return _ instanceof Promise||(_=Promise.resolve(_)),_}throw new O1(`Method not found: ${d}`)}}}e.fromService=t;function r(s,o){const a=o&&o.disableMarshalling;return new Proxy({},{get(l,c){if(typeof c=="string")return o?.properties?.has(c)?o.properties.get(c):n(c)?function(u){return s.listen(c,u)}:i(c)?s.listen(c):async function(...u){let f;o&&!Ir(o.context)?f=[o.context,...u]:f=u;const d=await s.call(c,f);return a?d:ce(d)};throw new O1(`Property not found: ${String(c)}`)}})}e.toService=r;function i(s){return s[0]==="o"&&s[1]==="n"&&mt(s.charCodeAt(2))}function n(s){return/^onDynamic/.test(s)&&mt(s.charCodeAt(9))}})(er||(er={}));var Hi=class extends Vi{constructor(e){super({send:t=>{try{process.send?.(t.buffer.toString("base64"))}catch{}},onMessage:Z.fromNodeEventEmitter(process,"message",t=>Y.wrap(Buffer.from(t,"base64")))},e),process.once("disconnect",()=>this.dispose())}},Ki=class{constructor(e){this.b=e}listen(e,t){throw new Error(`Event not found: ${t}`)}call(e,t,{eventName:r,data:i}){return this.b.forEach(n=>n.log(r,i)),Promise.resolve(null)}};import*as Ji from"https";var Qi=function(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID.bind(crypto);const e=new Uint8Array(16),t=[];for(let r=0;r<256;r++)t.push(r.toString(16).padStart(2,"0"));return function(){crypto.getRandomValues(e),e[6]=e[6]&15|64,e[8]=e[8]&63|128;let i=0,n="";return n+=t[e[i++]],n+=t[e[i++]],n+=t[e[i++]],n+=t[e[i++]],n+="-",n+=t[e[i++]],n+=t[e[i++]],n+="-",n+=t[e[i++]],n+=t[e[i++]],n+="-",n+=t[e[i++]],n+=t[e[i++]],n+="-",n+=t[e[i++]],n+=t[e[i++]],n+=t[e[i++]],n+=t[e[i++]],n+=t[e[i++]],n+=t[e[i++]],n}}(),Zi=!1,Gi=class{constructor(e,t,r){this.id=e,this.dependencies=t,this.callback=r}},p1;(function(e){e[e.Uninitialized=1]="Uninitialized",e[e.InitializedInternal=2]="InitializedInternal",e[e.InitializedExternal=3]="InitializedExternal"})(p1||(p1={}));var Yi=class dr{static{this.INSTANCE=new dr}constructor(){this.a=typeof self=="object"&&self.constructor&&self.constructor.name==="DedicatedWorkerGlobalScope",this.b=typeof document=="object",this.c=[],this.d=p1.Uninitialized}g(){if(this.d===p1.Uninitialized){if(globalThis.define){this.d=p1.InitializedExternal;return}}else return;this.d=p1.InitializedInternal,globalThis.define=(t,r,i)=>{typeof t!="string"&&(i=r,r=t,t=null),(typeof r!="object"||!Array.isArray(r))&&(i=r,r=null),this.c.push(new Gi(t,r,i))},globalThis.define.amd=!0,this.b?this.f=globalThis._VSCODE_WEB_PACKAGE_TTP??window.trustedTypes?.createPolicy("amdLoader",{createScriptURL(t){if(t.startsWith(window.location.origin)||t.startsWith(`${N.vscodeFileResource}://${Lt}`))return t;throw new Error(`[trusted_script_src] Invalid script url: ${t}`)}}):this.a&&(this.f=globalThis._VSCODE_WEB_PACKAGE_TTP??globalThis.trustedTypes?.createPolicy("amdLoader",{createScriptURL(t){return t}}))}async load(t){if(this.g(),this.d===p1.InitializedExternal)return new Promise(o=>{const a=Qi();globalThis.define(a,[t],function(l){o(l)})});const r=await(this.a?this.i(t):this.b?this.h(t):this.j(t));if(!r){console.warn(`Did not receive a define call from script ${t}`);return}const i={},n=[],s=[];if(Array.isArray(r.dependencies))for(const o of r.dependencies)o==="exports"?n.push(i):s.push(o);if(s.length>0)throw new Error(`Cannot resolve dependencies for script ${t}. The dependencies are: ${s.join(", ")}`);return typeof r.callback=="function"?r.callback(...n)??i:r.callback}h(t){return new Promise((r,i)=>{const n=document.createElement("script");n.setAttribute("async","async"),n.setAttribute("type","text/javascript");const s=()=>{n.removeEventListener("load",o),n.removeEventListener("error",a)},o=l=>{s(),r(this.c.pop())},a=l=>{s(),i(l)};n.addEventListener("load",o),n.addEventListener("error",a),this.f&&(t=this.f.createScriptURL(t)),n.setAttribute("src",t),window.document.getElementsByTagName("head")[0].appendChild(n)})}async i(t){return this.f&&(t=this.f.createScriptURL(t)),await import(t),this.c.pop()}async j(t){try{const r=(await import("fs")).default,i=(await import("vm")).default,n=(await import("module")).default,s=B.parse(t).fsPath,o=r.readFileSync(s).toString(),a=n.wrap(o.replace(/^#!.*/,""));return new i.Script(a).runInThisContext().apply(),this.c.pop()}catch(r){throw r}}},ze=new Map;async function tr(e,t,r){r===void 0&&(r=!!(globalThis._VSCODE_PRODUCT_JSON??globalThis.vscode?.context?.configuration()?.product)?.commit);const i=t?`${e}/${t}`:e;if(ze.has(i))return ze.get(i);let n;if(/^\w[\w\d+.-]*:\/\//.test(i))n=i;else{const l=`${Zi&&r&&!re?ri:ti}/${i}`;n=ni.asBrowserUri(l).toString(!0)}const s=Yi.INSTANCE.load(n);return ze.set(i,s),s}var s1;(function(e){e.serviceIds=new Map,e.DI_TARGET="$di$target",e.DI_DEPENDENCIES="$di$dependencies";function t(r){return r[e.DI_DEPENDENCIES]||[]}e.getServiceDependencies=t})(s1||(s1={}));var h0=Fe("instantiationService");function Xi(e,t,r){t[s1.DI_TARGET]===t?t[s1.DI_DEPENDENCIES].push({id:e,index:r}):(t[s1.DI_DEPENDENCIES]=[{id:e,index:r}],t[s1.DI_TARGET]=t)}function Fe(e){if(s1.serviceIds.has(e))return s1.serviceIds.get(e);const t=function(r,i,n){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");Xi(t,r,n)};return t.toString=()=>e,s1.serviceIds.set(e,t),t}var d0=Fe("telemetryService"),g0=Fe("customEndpointTelemetryService"),rr;(function(e){e[e.NONE=0]="NONE",e[e.CRASH=1]="CRASH",e[e.ERROR=2]="ERROR",e[e.USAGE=3]="USAGE"})(rr||(rr={}));var ir;(function(e){e.OFF="off",e.CRASH="crash",e.ERROR="error",e.ON="all"})(ir||(ir={}));var en=class{constructor(){this.telemetryLevel=0,this.sessionId="someValue.sessionId",this.machineId="someValue.machineId",this.sqmId="someValue.sqmId",this.devDeviceId="someValue.devDeviceId",this.firstSessionDate="someValue.firstSessionDate",this.sendErrorTelemetry=!1}publicLog(){}publicLog2(){}publicLogError(){}publicLogError2(){}setExperimentProperty(){}},v0=new en,tn="telemetry",m0={id:tn,name:Xr(2293,null)};function rn(e){const t={},r={},i={};nr(e,i);for(let n in i){n=n.length>150?n.substr(n.length-149):n;const s=i[n];typeof s=="number"?r[n]=s:typeof s=="boolean"?r[n]=s?1:0:typeof s=="string"?(s.length>8192&&console.warn(`Telemetry property: ${n} has been trimmed to 8192, the original length is ${s.length}`),t[n]=s.substring(0,8191)):typeof s<"u"&&s!==null&&(t[n]=s)}return{properties:t,measurements:r}}function nr(e,t,r=0,i){if(e)for(const n of Object.getOwnPropertyNames(e)){const s=e[n],o=i?i+n:n;Array.isArray(s)?t[o]=Mt(s):s instanceof Date?t[o]=s.toISOString():y1(s)?r<2?nr(s,t,r+1,o+"."):t[o]=Mt(s):t[o]=s}}var sr="https://mobile.events.data.microsoft.com/OneCollector/1.0",nn="https://mobile.events.data.microsoft.com/ping";async function sn(e,t,r){const i=re?await tr("@microsoft/1ds-core-js","bundle/ms.core.min.js"):await import("@microsoft/1ds-core-js"),n=re?await tr("@microsoft/1ds-post-js","bundle/ms.post.min.js"):await import("@microsoft/1ds-post-js"),s=new i.AppInsightsCore,o=new n.PostChannel,a={instrumentationKey:e,endpointUrl:sr,loggingLevelTelemetry:0,loggingLevelConsole:0,disableCookiesUsage:!0,disableDbgExt:!0,disableInstrumentationKeyValidation:!0,channels:[[o]]};if(r){a.extensionConfig={};const l={alwaysUseXhrOverride:!0,ignoreMc1Ms0CookieProcessing:!0,httpXHROverride:r};a.extensionConfig[o.identifier]=l}return s.initialize(a,[]),s.addTelemetryInitializer(l=>{l.ext=l.ext??{},l.ext.web=l.ext.web??{},l.ext.web.consentDetails='{"GPC_DataSharingOptIn":false}',t&&(l.ext.utc=l.ext.utc??{},l.ext.utc.flags=8462029)}),s}var on=class{constructor(e,t,r,i,n){this.e=e,this.f=t,this.g=r,this.h=n,this.c=sr,this.d=nn,this.g||(this.g={}),typeof i=="function"?this.a=i():this.a=i,this.b=null}i(e){if(this.a){if(typeof this.a!="string"){e(this.a);return}this.b||(this.b=sn(this.a,this.e,this.h)),this.b.then(t=>{e(t)},t=>{L1(t),console.error(t)})}}log(e,t){if(!this.a)return;t=Tt(t,this.g),t=rn(t);const r=this.f+"/"+e;try{this.i(i=>{i.pluginVersionString=t?.properties.version??"Unknown",i.track({name:r,baseData:{name:r,properties:t?.properties,measurements:t?.measurements}})})}catch{}}flush(){return this.a?new Promise(e=>{this.i(t=>{t.unload(!0,()=>{this.a=void 0,e(void 0)})})}):Promise.resolve(void 0)}};async function an(e,t){const r=await t.request(e,A1.None),i=(await $i(r.stream)).toString(),n=r.res.statusCode??200;return{headers:r.res.headers,statusCode:n,responseData:i}}async function cn(e){const t={method:e.type,headers:e.headers};return new Promise((i,n)=>{const s=Ji.request(e.url??"",t,o=>{o.on("data",function(a){i({headers:o.headers,statusCode:o.statusCode??200,responseData:a.toString()})}),o.on("error",function(a){n(a)})});s.write(e.data,o=>{o&&n(o)}),s.end()})}async function ln(e,t,r){const i=typeof t.data=="string"?t.data:new TextDecoder().decode(t.data),n={type:"POST",headers:{...t.headers,"Content-Type":"application/json","Content-Length":Buffer.byteLength(t.data).toString()},url:t.urlString,data:i};try{const s=e?await an(n,e):await cn(n);r(s.statusCode,s.headers,s.responseData)}catch{r(0,{})}}var fn=class extends on{constructor(e,t,r,i,n){const s={sendPOST:(o,a)=>{ln(e,o,a)}};super(t,r,i,n,s)}},or=new fn(void 0,!1,process.argv[2],JSON.parse(process.argv[3]),process.argv[4]);process.once("exit",()=>or.flush());var un=new Ki([or]),hn=new Hi("telemetry");hn.registerChannel("telemetryAppender",un);

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6f17636121051a53c88d3e605c491d22af2ba755/core/vs/workbench/contrib/debug/node/telemetryApp.js.map
