"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const assert = __importStar(require("assert"));
const vscode = __importStar(require("vscode"));
const ollamaClient_1 = require("../../services/ollamaClient");
const configurationManager_1 = require("../../services/configurationManager");
suite('Extension Test Suite', () => {
    vscode.window.showInformationMessage('Start all tests.');
    test('Extension should be present', () => {
        // List all extensions to debug
        const allExtensions = vscode.extensions.all.map(ext => ext.id);
        console.log('All loaded extensions:', allExtensions);
        // Look for our extension
        const ourExtension = vscode.extensions.all.find(ext => ext.id.includes('ollama') || ext.packageJSON?.name === 'ollama-code-assistant');
        assert.ok(ourExtension, `Extension should be loaded. Available extensions: ${allExtensions.join(', ')}`);
    });
    test('Configuration Manager should work', () => {
        const configManager = new configurationManager_1.ConfigurationManager();
        const serverUrl = configManager.getServerUrl();
        assert.ok(serverUrl);
        assert.strictEqual(typeof serverUrl, 'string');
    });
    test('Ollama Client should initialize', () => {
        const configManager = new configurationManager_1.ConfigurationManager();
        const ollamaClient = new ollamaClient_1.OllamaClient(configManager);
        assert.ok(ollamaClient);
    });
    test('Commands should be registered', async () => {
        const commands = await vscode.commands.getCommands(true);
        const ollamaCommands = commands.filter(cmd => cmd.startsWith('ollama-assistant.'));
        assert.ok(ollamaCommands.includes('ollama-assistant.openChat'));
        assert.ok(ollamaCommands.includes('ollama-assistant.selectModel'));
        assert.ok(ollamaCommands.includes('ollama-assistant.refreshModels'));
    });
});
//# sourceMappingURL=extension.test.js.map