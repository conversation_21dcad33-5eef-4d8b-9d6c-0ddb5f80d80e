2025-08-26 05:47:09.929 [info] Extension host with pid 5032 started
2025-08-26 05:47:09.938 [info] ExtensionService#_doActivateExtension vscode.github-authentication, startup: false, activationEvent: 'onAuthenticationRequest:github'
2025-08-26 05:47:09.968 [info] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-08-26 05:47:09.971 [info] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-08-26 05:47:10.029 [info] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-08-26 05:47:10.075 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.openRepositoryInParentFolders', provide the URI of a resource or 'null' for any resource.
2025-08-26 05:47:10.075 [warning] [vscode.git] Accessing a resource scoped configuration without providing a resource is not expected. To get the effective value for 'git.showProgress', provide the URI of a resource or 'null' for any resource.
2025-08-26 05:47:10.102 [info] Eager extensions activated
2025-08-26 05:47:10.175 [info] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-08-26 05:47:10.177 [info] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-08-26 05:47:10.190 [info] ExtensionService#_doActivateExtension undefined_publisher.ollama-code-assistant, startup: false, activationEvent: 'onStartupFinished'
2025-08-26 05:47:10.246 [warning] [Deprecation Warning] 'PendingMigrationError' is deprecated. navigator is now a global in nodejs, please see https://aka.ms/vscode-extensions/navigator for additional info on this error.
 FROM: PendingMigrationError: navigator is now a global in nodejs, please see https://aka.ms/vscode-extensions/navigator for additional info on this error.
	at get (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:388:6355)
	at Object.<anonymous> (c:\Users\<USER>\Documents\AiCodingAssistant\node_modules\axios\dist\node\axios.cjs:1356:20)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:18013)
	at e._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:388:5728)
	at t._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:206:22695)
	at r._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:25694)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at Object.<anonymous> (c:\Users\<USER>\Documents\AiCodingAssistant\out\services\ollamaClient.js:7:33)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:18013)
	at e._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:388:5728)
	at t._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:206:22695)
	at r._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:25694)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at Object.<anonymous> (c:\Users\<USER>\Documents\AiCodingAssistant\out\extension.js:39:24)
	at Module._compile (node:internal/modules/cjs/loader:1738:14)
	at Module._extensions..js (node:internal/modules/cjs/loader:1904:10)
	at Module.load (node:internal/modules/cjs/loader:1472:32)
	at Module._load (node:internal/modules/cjs/loader:1289:12)
	at c._load (node:electron/js2c/node_init:2:18013)
	at e._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:388:5728)
	at t._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:206:22695)
	at r._load (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:198:25694)
	at TracingChannel.traceSync (node:diagnostics_channel:322:14)
	at wrapModuleLoad (node:internal/modules/cjs/loader:242:24)
	at Module.require (node:internal/modules/cjs/loader:1494:12)
	at require (node:internal/modules/helpers:135:16)
	at LZ.Cb (file:///c:/Users/<USER>/Documents/AiCodingAssistant/.vscode-test/vscode-win32-x64-archive-1.103.2/resources/app/out/vs/workbench/api/node/extensionHostProcess.js:237:1253)
2025-08-26 05:47:10.264 [info] Extension 'undefined_publisher.ollama-code-assistant' uses a document selector without scheme. Learn more about this: https://go.microsoft.com/fwlink/?linkid=872305
2025-08-26 05:47:10.316 [info] Extension host terminating: renderer closed the MessagePort
2025-08-26 05:47:10.331 [info] Extension host with pid 5032 exiting with code 0
