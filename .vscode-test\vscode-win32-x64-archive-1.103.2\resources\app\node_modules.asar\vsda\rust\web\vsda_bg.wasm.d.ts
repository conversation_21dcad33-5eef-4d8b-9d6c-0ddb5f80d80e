/* tslint:disable */
/* eslint-disable */
export const memory: WebAssembly.Memory;
export function createNewMessage(a: number, b: number, c: number): void;
export function sign(a: number, b: number, c: number): void;
export function validate(a: number, b: number, c: number, d: number): number;
export function __wbg_signer_free(a: number): void;
export function signer_new(): number;
export function signer_sign(a: number, b: number, c: number, d: number): void;
export function __wbg_validator_free(a: number): void;
export function validator_new(): number;
export function validator_createNewMessage(a: number, b: number, c: number, d: number): void;
export function validator_validate(a: number, b: number, c: number, d: number): void;
export function __wbindgen_add_to_stack_pointer(a: number): number;
export function __wbindgen_malloc(a: number): number;
export function __wbindgen_realloc(a: number, b: number, c: number): number;
export function __wbindgen_free(a: number, b: number): void;
export function __wbindgen_exn_store(a: number): void;
