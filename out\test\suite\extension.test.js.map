{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["../../../src/test/suite/extension.test.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,+CAAiC;AACjC,8DAA2D;AAC3D,8EAA2E;AAE3E,KAAK,CAAC,sBAAsB,EAAE,GAAG,EAAE;IAC/B,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kBAAkB,CAAC,CAAC;IAEzD,IAAI,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACrC,+BAA+B;QAC/B,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC/D,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,aAAa,CAAC,CAAC;QAErD,yBAAyB;QACzB,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAClD,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC,WAAW,EAAE,IAAI,KAAK,uBAAuB,CACjF,CAAC;QAEF,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,qDAAqD,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7G,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC3C,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,SAAS,GAAG,aAAa,CAAC,YAAY,EAAE,CAAC;QAC/C,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;QACrB,MAAM,CAAC,WAAW,CAAC,OAAO,SAAS,EAAE,QAAQ,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,iCAAiC,EAAE,GAAG,EAAE;QACzC,MAAM,aAAa,GAAG,IAAI,2CAAoB,EAAE,CAAC;QACjD,MAAM,YAAY,GAAG,IAAI,2BAAY,CAAC,aAAa,CAAC,CAAC;QACrD,MAAM,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,+BAA+B,EAAE,KAAK,IAAI,EAAE;QAC7C,MAAM,QAAQ,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACzD,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,CAAC;QAEnF,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,2BAA2B,CAAC,CAAC,CAAC;QAChE,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,8BAA8B,CAAC,CAAC,CAAC;QACnE,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,gCAAgC,CAAC,CAAC,CAAC;IACzE,CAAC,CAAC,CAAC;AACP,CAAC,CAAC,CAAC"}