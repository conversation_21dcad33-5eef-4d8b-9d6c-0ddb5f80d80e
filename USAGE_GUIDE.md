# Visual Usage Guide

## How to Access Ollama Code Assistant

### 1. Extension is Active
When you see "Ollama Code Assistant is now active!" in the terminal, the extension is loaded.

### 2. Access Methods

#### Method A: Command Palette
```
Ctrl+Shift+P → "Ollama Assistant: Open Chat" → Enter
```

#### Method B: Sidebar Panel
```
Explorer Sidebar → Look for "Ollama Assistant" section → Click to expand
```

### 3. What You Should See

#### Chat Interface
```
┌─────────────────────────────────────────┐
│ Ollama Assistant                        │
├─────────────────────────────────────────┤
│ Model: [Select a model...] [Refresh]    │
├─────────────────────────────────────────┤
│                                         │
│  Chat messages appear here...           │
│                                         │
├─────────────────────────────────────────┤
│ [✓] Include current file context        │
│ ┌─────────────────────────────────────┐ │
│ │ Ask me anything about your code...  │ │
│ └─────────────────────────────────────┘ │
│                                [Send]   │
└─────────────────────────────────────────┘
```

#### Sidebar Panel
```
EXPLORER
├── 📁 Your Project Files
└── 🤖 OLLAMA ASSISTANT
    ├── New Chat
    └── Clear History
```

### 4. First Steps

1. **Select Model**: Use the dropdown to choose your Ollama model
2. **Test Chat**: Type "Hello!" and press Send
3. **Test Completions**: Open a code file and start typing

### 5. Common Issues

#### "No models available"
- Run: `ollama pull codellama:7b`
- Restart VS Code
- Try "Refresh Models" button

#### "Cannot connect to Ollama"
- Run: `ollama serve` in terminal
- Check if http://localhost:11434 is accessible
- Verify Ollama installation

#### "Extension not visible"
- Check View → Output → "Ollama Code Assistant"
- Try reloading the window: Ctrl+Shift+P → "Developer: Reload Window"

### 6. Example Workflow

1. **Open a code file** (e.g., `example.js`)
2. **Open chat**: Ctrl+Shift+P → "Ollama Assistant: Open Chat"
3. **Select model**: Choose from dropdown
4. **Enable context**: Check "Include current file context"
5. **Ask question**: "Explain this code" or "Add error handling"
6. **Get completions**: Start typing in your code file

### 7. Features to Try

#### Chat Examples:
- "Explain this function"
- "Add error handling to this code"
- "Write unit tests for this class"
- "Optimize this algorithm"

#### Completion Examples:
- Start typing a function and wait for suggestions
- Type comments and let AI complete the implementation
- Begin variable declarations and see smart suggestions

### 8. Settings

Access via: File → Preferences → Settings → Search "Ollama"

Key settings:
- Server URL (default: http://localhost:11434)
- Default Model
- Enable Completions
- Completion Delay

---

**Need Help?** Check the console output or create an issue on GitHub!
