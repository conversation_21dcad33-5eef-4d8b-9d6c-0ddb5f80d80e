/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var wn=function(e,t){return wn=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,r){n.__proto__=r}||function(n,r){for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(n[s]=r[s])},wn(e,t)};export function __extends(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");wn(e,t);function n(){this.constructor=e}e.prototype=t===null?Object.create(t):(n.prototype=t.prototype,new n)}export var __assign=function(){return __assign=Object.assign||function(t){for(var n,r=1,s=arguments.length;r<s;r++){n=arguments[r];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t},__assign.apply(this,arguments)};export function __rest(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var s=0,r=Object.getOwnPropertySymbols(e);s<r.length;s++)t.indexOf(r[s])<0&&Object.prototype.propertyIsEnumerable.call(e,r[s])&&(n[r[s]]=e[r[s]]);return n}export function __decorate(e,t,n,r){var s=arguments.length,i=s<3?t:r===null?r=Object.getOwnPropertyDescriptor(t,n):r,a;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,t,n,r);else for(var u=e.length-1;u>=0;u--)(a=e[u])&&(i=(s<3?a(i):s>3?a(t,n,i):a(t,n))||i);return s>3&&i&&Object.defineProperty(t,n,i),i}export function __param(e,t){return function(n,r){t(n,r,e)}}export function __esDecorate(e,t,n,r,s,i){function a(w){if(w!==void 0&&typeof w!="function")throw new TypeError("Function expected");return w}for(var u=r.kind,o=u==="getter"?"get":u==="setter"?"set":"value",c=!t&&e?r.static?e:e.prototype:null,f=t||(c?Object.getOwnPropertyDescriptor(c,r.name):{}),h,g=!1,m=n.length-1;m>=0;m--){var d={};for(var p in r)d[p]=p==="access"?{}:r[p];for(var p in r.access)d.access[p]=r.access[p];d.addInitializer=function(w){if(g)throw new TypeError("Cannot add initializers after decoration has completed");i.push(a(w||null))};var v=(0,n[m])(u==="accessor"?{get:f.get,set:f.set}:f[o],d);if(u==="accessor"){if(v===void 0)continue;if(v===null||typeof v!="object")throw new TypeError("Object expected");(h=a(v.get))&&(f.get=h),(h=a(v.set))&&(f.set=h),(h=a(v.init))&&s.unshift(h)}else(h=a(v))&&(u==="field"?s.unshift(h):f[o]=h)}c&&Object.defineProperty(c,r.name,f),g=!0}export function __runInitializers(e,t,n){for(var r=arguments.length>2,s=0;s<t.length;s++)n=r?t[s].call(e,n):t[s].call(e);return r?n:void 0}export function __propKey(e){return typeof e=="symbol"?e:"".concat(e)}export function __setFunctionName(e,t,n){return typeof t=="symbol"&&(t=t.description?"[".concat(t.description,"]"):""),Object.defineProperty(e,"name",{configurable:!0,value:n?"".concat(n," ",t):t})}export function __metadata(e,t){if(typeof Reflect=="object"&&typeof Reflect.metadata=="function")return Reflect.metadata(e,t)}export function __awaiter(e,t,n,r){function s(i){return i instanceof n?i:new n(function(a){a(i)})}return new(n||(n=Promise))(function(i,a){function u(f){try{c(r.next(f))}catch(h){a(h)}}function o(f){try{c(r.throw(f))}catch(h){a(h)}}function c(f){f.done?i(f.value):s(f.value).then(u,o)}c((r=r.apply(e,t||[])).next())})}export function __generator(e,t){var n={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,s,i,a;return a={next:u(0),throw:u(1),return:u(2)},typeof Symbol=="function"&&(a[Symbol.iterator]=function(){return this}),a;function u(c){return function(f){return o([c,f])}}function o(c){if(r)throw new TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(n=0)),n;)try{if(r=1,s&&(i=c[0]&2?s.return:c[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,c[1])).done)return i;switch(s=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return n.label++,{value:c[1],done:!1};case 5:n.label++,s=c[1],c=[0];continue;case 7:c=n.ops.pop(),n.trys.pop();continue;default:if(i=n.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){n=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){n.label=c[1];break}if(c[0]===6&&n.label<i[1]){n.label=i[1],i=c;break}if(i&&n.label<i[2]){n.label=i[2],n.ops.push(c);break}i[2]&&n.ops.pop(),n.trys.pop();continue}c=t.call(e,n)}catch(f){c=[6,f],s=0}finally{r=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}export var __createBinding=Object.create?function(e,t,n,r){r===void 0&&(r=n);var s=Object.getOwnPropertyDescriptor(t,n);(!s||("get"in s?!t.__esModule:s.writable||s.configurable))&&(s={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,s)}:function(e,t,n,r){r===void 0&&(r=n),e[r]=t[n]};export function __exportStar(e,t){for(var n in e)n!=="default"&&!Object.prototype.hasOwnProperty.call(t,n)&&__createBinding(t,e,n)}export function __values(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}export function __read(e,t){var n=typeof Symbol=="function"&&e[Symbol.iterator];if(!n)return e;var r=n.call(e),s,i=[],a;try{for(;(t===void 0||t-- >0)&&!(s=r.next()).done;)i.push(s.value)}catch(u){a={error:u}}finally{try{s&&!s.done&&(n=r.return)&&n.call(r)}finally{if(a)throw a.error}}return i}export function __spread(){for(var e=[],t=0;t<arguments.length;t++)e=e.concat(__read(arguments[t]));return e}export function __spreadArrays(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;for(var r=Array(e),s=0,t=0;t<n;t++)for(var i=arguments[t],a=0,u=i.length;a<u;a++,s++)r[s]=i[a];return r}export function __spreadArray(e,t,n){if(n||arguments.length===2)for(var r=0,s=t.length,i;r<s;r++)(i||!(r in t))&&(i||(i=Array.prototype.slice.call(t,0,r)),i[r]=t[r]);return e.concat(i||Array.prototype.slice.call(t))}export function __await(e){return this instanceof __await?(this.v=e,this):new __await(e)}export function __asyncGenerator(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),s,i=[];return s={},u("next"),u("throw"),u("return",a),s[Symbol.asyncIterator]=function(){return this},s;function a(m){return function(d){return Promise.resolve(d).then(m,h)}}function u(m,d){r[m]&&(s[m]=function(p){return new Promise(function(v,w){i.push([m,p,v,w])>1||o(m,p)})},d&&(s[m]=d(s[m])))}function o(m,d){try{c(r[m](d))}catch(p){g(i[0][3],p)}}function c(m){m.value instanceof __await?Promise.resolve(m.value.v).then(f,h):g(i[0][2],m)}function f(m){o("next",m)}function h(m){o("throw",m)}function g(m,d){m(d),i.shift(),i.length&&o(i[0][0],i[0][1])}}export function __asyncDelegator(e){var t,n;return t={},r("next"),r("throw",function(s){throw s}),r("return"),t[Symbol.iterator]=function(){return this},t;function r(s,i){t[s]=e[s]?function(a){return(n=!n)?{value:__await(e[s](a)),done:!1}:i?i(a):a}:i}}export function __asyncValues(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof __values=="function"?__values(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(a){return new Promise(function(u,o){a=e[i](a),s(u,o,a.done,a.value)})}}function s(i,a,u,o){Promise.resolve(o).then(function(c){i({value:c,done:u})},a)}}export function __makeTemplateObject(e,t){return Object.defineProperty?Object.defineProperty(e,"raw",{value:t}):e.raw=t,e}var sa=Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t};export function __importStar(e){if(e&&e.__esModule)return e;var t={};if(e!=null)for(var n in e)n!=="default"&&Object.prototype.hasOwnProperty.call(e,n)&&__createBinding(t,e,n);return sa(t,e),t}export function __importDefault(e){return e&&e.__esModule?e:{default:e}}export function __classPrivateFieldGet(e,t,n,r){if(n==="a"&&!r)throw new TypeError("Private accessor was defined without a getter");if(typeof t=="function"?e!==t||!r:!t.has(e))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?r:n==="a"?r.call(e):r?r.value:t.get(e)}export function __classPrivateFieldSet(e,t,n,r,s){if(r==="m")throw new TypeError("Private method is not writable");if(r==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof t=="function"?e!==t||!s:!t.has(e))throw new TypeError("Cannot write private member to an object whose class did not declare it");return r==="a"?s.call(e,n):s?s.value=n:t.set(e,n),n}export function __classPrivateFieldIn(e,t){if(t===null||typeof t!="object"&&typeof t!="function")throw new TypeError("Cannot use 'in' operator on non-object");return typeof e=="function"?t===e:e.has(t)}export function __addDisposableResource(e,t,n){if(t!=null){if(typeof t!="object"&&typeof t!="function")throw new TypeError("Object expected.");var r,s;if(n){if(!Symbol.asyncDispose)throw new TypeError("Symbol.asyncDispose is not defined.");r=t[Symbol.asyncDispose]}if(r===void 0){if(!Symbol.dispose)throw new TypeError("Symbol.dispose is not defined.");r=t[Symbol.dispose],n&&(s=r)}if(typeof r!="function")throw new TypeError("Object not disposable.");s&&(r=function(){try{s.call(this)}catch(i){return Promise.reject(i)}}),e.stack.push({value:t,dispose:r,async:n})}else n&&e.stack.push({async:!0});return t}var ia=typeof SuppressedError=="function"?SuppressedError:function(e,t,n){var r=new Error(n);return r.name="SuppressedError",r.error=e,r.suppressed=t,r};export function __disposeResources(e){function t(r){e.error=e.hasError?new ia(r,e.error,"An error was suppressed during disposal."):r,e.hasError=!0}function n(){for(;e.stack.length;){var r=e.stack.pop();try{var s=r.dispose&&r.dispose.call(r.value);if(r.async)return Promise.resolve(s).then(n,function(i){return t(i),n()})}catch(i){t(i)}}if(e.hasError)throw e.error}return n()}export default{__extends,__assign,__rest,__decorate,__param,__metadata,__awaiter,__generator,__createBinding,__exportStar,__values,__read,__spread,__spreadArrays,__spreadArray,__await,__asyncGenerator,__asyncDelegator,__asyncValues,__makeTemplateObject,__importStar,__importDefault,__classPrivateFieldGet,__classPrivateFieldSet,__classPrivateFieldIn,__addDisposableResource,__disposeResources};var aa=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?Ln.isErrorNoTelemetry(e)?new Ln(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},la=new aa;function bt(e){ua(e)||la.onUnexpectedError(e)}function vn(e){if(e instanceof Error){const{name:t,message:n,cause:r}=e,s=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:s,noTelemetry:Ln.isErrorNoTelemetry(e),cause:r?vn(r):void 0,code:e.code}}return e}var pn="Canceled";function ua(e){return e instanceof oa?!0:e instanceof Error&&e.name===pn&&e.message===pn}var oa=class extends Error{constructor(){super(pn),this.name=this.message}},Qu=class cn extends Error{static{this.a="PendingMigrationError"}static is(t){return t instanceof cn||t instanceof Error&&t.name===cn.a}constructor(t){super(t),this.name=cn.a}},Ln=class cr extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof cr)return t;const n=new cr;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},K=class ji extends Error{constructor(t){super(t||"An unexpected bug occurred."),Object.setPrototypeOf(this,ji.prototype)}},br;function ca(e,t){const n=Object.create(null);for(const r of e){const s=t(r);let i=n[s];i||(i=n[s]=[]),i.push(r)}return n}var Ku=class{static{br=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[br]="SetWithKey";for(const n of e)this.add(n)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(n=>e.call(t,n,n,this))}[Symbol.iterator](){return this.values()}};function ha(e,t){const n=this;let r=!1,s;return function(){if(r)return s;if(r=!0,t)try{s=e.apply(n,arguments)}finally{t()}else s=e.apply(n,arguments);return s}}function Ge(e,t){const n=Xe(e,t);return n===-1?void 0:e[n]}function Xe(e,t,n=0,r=e.length){let s=n,i=r;for(;s<i;){const a=Math.floor((s+i)/2);t(e[a])?s=a+1:i=a}return s-1}function fa(e,t){const n=Nn(e,t);return n===e.length?void 0:e[n]}function Nn(e,t,n=0,r=e.length){let s=n,i=r;for(;s<i;){const a=Math.floor((s+i)/2);t(e[a])?i=a:s=a+1}return s}var wr=class Oi{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(Oi.assertInvariants){if(this.d){for(const r of this.e)if(this.d(r)&&!t(r))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const n=Xe(this.e,t,this.c);return this.c=n+1,n===-1?void 0:this.e[n]}};function vr(e,t,n=(r,s)=>r===s){if(e===t)return!0;if(!e||!t||e.length!==t.length)return!1;for(let r=0,s=e.length;r<s;r++)if(!n(e[r],t[r]))return!1;return!0}function*ga(e,t){let n,r;for(const s of e)r!==void 0&&t(r,s)?n.push(s):(n&&(yield n),n=[s]),r=s;n&&(yield n)}function ma(e,t){for(let n=0;n<=e.length;n++)t(n===0?void 0:e[n-1],n===e.length?void 0:e[n])}function da(e,t){for(let n=0;n<e.length;n++)t(n===0?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])}function ba(e,t){for(const n of t)e.push(n)}var Rn;(function(e){function t(i){return i<0}e.isLessThan=t;function n(i){return i<=0}e.isLessThanOrEqual=n;function r(i){return i>0}e.isGreaterThan=r;function s(i){return i===0}e.isNeitherLessOrGreaterThan=s,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(Rn||(Rn={}));function Ie(e,t){return(n,r)=>t(e(n),e(r))}var Je=(e,t)=>e-t;function wa(e){return(t,n)=>-e(t,n)}var eo=class hn{static{this.empty=new hn(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(n=>(t(n),!0))}toArray(){const t=[];return this.iterate(n=>(t.push(n),!0)),t}filter(t){return new hn(n=>this.iterate(r=>t(r)?n(r):!0))}map(t){return new hn(n=>this.iterate(r=>n(t(r))))}some(t){let n=!1;return this.iterate(r=>(n=t(r),!n)),n}findFirst(t){let n;return this.iterate(r=>t(r)?(n=r,!1):!0),n}findLast(t){let n;return this.iterate(r=>(t(r)&&(n=r),!0)),n}findLastMaxBy(t){let n,r=!0;return this.iterate(s=>((r||Rn.isGreaterThan(t(s,n)))&&(r=!1,n=s),!0)),n}};function va(e,t){return e.reduce((n,r)=>n+t(r),0)}var pr,Lr,Nr,pa=class{constructor(e,t){this.uri=e,this.value=t}};function La(e){return Array.isArray(e)}var Rr=class Mt{static{this.c=t=>t.toString()}constructor(t,n){if(this[pr]="ResourceMap",t instanceof Mt)this.d=new Map(t.d),this.e=n??Mt.c;else if(La(t)){this.d=new Map,this.e=n??Mt.c;for(const[r,s]of t)this.set(r,s)}else this.d=new Map,this.e=t??Mt.c}set(t,n){return this.d.set(this.e(t),new pa(t,n)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,n){typeof n<"u"&&(t=t.bind(n));for(const[r,s]of this.d)t(s.value,s.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(pr=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},to=class{constructor(e,t){this[Lr]="ResourceSet",!e||typeof e=="function"?this.c=new Rr(e):(this.c=new Rr(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((n,r)=>e.call(t,r,r,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(Lr=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},Ar;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(Ar||(Ar={}));var Na=class{constructor(){this[Nr]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const n=this.c.get(e);if(n)return t!==0&&this.n(n,t),n.value}set(e,t,n=0){let r=this.c.get(e);if(r)r.value=t,n!==0&&this.n(r,n);else{switch(r={key:e,value:t,next:void 0,previous:void 0},n){case 0:this.l(r);break;case 1:this.k(r);break;case 2:this.l(r);break;default:this.l(r);break}this.c.set(e,r),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const n=this.g;let r=this.d;for(;r;){if(t?e.bind(t)(r.value,r.key,this):e(r.value,r.key,this),this.g!==n)throw new Error("LinkedMap got modified during iteration.");r=r.next}}keys(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const s={value:n.key,done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return r}values(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const s={value:n.value,done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return r}entries(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const s={value:[n.key,n.value],done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return r}[(Nr=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.next,n--;this.d=t,this.f=n,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.previous,n--;this.e=t,this.f=n,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const n=e.next,r=e.previous;e===this.e?(r.next=void 0,this.e=r):(n.previous=r,r.next=n),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const n=e.next,r=e.previous;e===this.d?(n.previous=void 0,this.d=n):(n.previous=r,r.next=n),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,n)=>{e.push([n,t])}),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}},Ra=class extends Na{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},Aa=class extends Ra{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},xr=class{constructor(){this.c=new Map}add(e,t){let n=this.c.get(e);n||(n=new Set,this.c.set(e,n)),n.add(t)}delete(e,t){const n=this.c.get(e);n&&(n.delete(t),n.size===0&&this.c.delete(e))}forEach(e,t){const n=this.c.get(e);n&&n.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function xa(e,t="Unreachable"){throw new Error(t)}function Ea(e,t="unexpected state"){if(!e)throw typeof t=="string"?new K(`Assertion Failed: ${t}`):t}function wt(e){if(!e()){debugger;e(),bt(new K("Assertion Failed"))}}function An(e,t){let n=0;for(;n<e.length-1;){const r=e[n],s=e[n+1];if(!t(r,s))return!1;n++}return!0}function Ma(e){return typeof e=="string"}function ya(e){return!!e&&typeof e[Symbol.iterator]=="function"}var $t;(function(e){function t(L){return!!L&&typeof L=="object"&&typeof L[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function r(){return n}e.empty=r;function*s(L){yield L}e.single=s;function i(L){return t(L)?L:s(L)}e.wrap=i;function a(L){return L||n}e.from=a;function*u(L){for(let R=L.length-1;R>=0;R--)yield L[R]}e.reverse=u;function o(L){return!L||L[Symbol.iterator]().next().done===!0}e.isEmpty=o;function c(L){return L[Symbol.iterator]().next().value}e.first=c;function f(L,R){let F=0;for(const U of L)if(R(U,F++))return!0;return!1}e.some=f;function h(L,R){let F=0;for(const U of L)if(!R(U,F++))return!1;return!0}e.every=h;function g(L,R){for(const F of L)if(R(F))return F}e.find=g;function*m(L,R){for(const F of L)R(F)&&(yield F)}e.filter=m;function*d(L,R){let F=0;for(const U of L)yield R(U,F++)}e.map=d;function*p(L,R){let F=0;for(const U of L)yield*R(U,F++)}e.flatMap=p;function*v(...L){for(const R of L)ya(R)?yield*R:yield R}e.concat=v;function w(L,R,F){let U=F;for(const j of L)U=R(U,j);return U}e.reduce=w;function A(L){let R=0;for(const F of L)R++;return R}e.length=A;function*N(L,R,F=L.length){for(R<-L.length&&(R=0),R<0&&(R+=L.length),F<0?F+=L.length:F>L.length&&(F=L.length);R<F;R++)yield L[R]}e.slice=N;function x(L,R=Number.POSITIVE_INFINITY){const F=[];if(R===0)return[F,L];const U=L[Symbol.iterator]();for(let j=0;j<R;j++){const B=U.next();if(B.done)return[F,e.empty()];F.push(B.value)}return[F,{[Symbol.iterator](){return U}}]}e.consume=x;async function k(L){const R=[];for await(const F of L)R.push(F);return R}e.asyncToArray=k;async function M(L){let R=[];for await(const F of L)R=R.concat(F);return R}e.asyncToArrayFlat=M})($t||($t={}));var ka=!1,Ye=null,no=class Wi{constructor(){this.b=new Map}static{this.a=0}c(t){let n=this.b.get(t);return n||(n={parent:null,source:null,isSingleton:!1,value:t,idx:Wi.a++},this.b.set(t,n)),n}trackDisposable(t){const n=this.c(t);n.source||(n.source=new Error().stack)}setParent(t,n){const r=this.c(t);r.parent=n}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,n){const r=n.get(t);if(r)return r;const s=t.parent?this.f(this.c(t.parent),n):t;return n.set(t,s),s}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,r])=>r.source!==null&&!this.f(r,t).isSingleton).flatMap(([r])=>r)}computeLeakingDisposables(t=10,n){let r;if(n)r=n;else{const o=new Map,c=[...this.b.values()].filter(h=>h.source!==null&&!this.f(h,o).isSingleton);if(c.length===0)return;const f=new Set(c.map(h=>h.value));if(r=c.filter(h=>!(h.parent&&f.has(h.parent))),r.length===0)throw new Error("There are cyclic diposable chains!")}if(!r)return;function s(o){function c(h,g){for(;h.length>0&&g.some(m=>typeof m=="string"?m===h[0]:h[0].match(m));)h.shift()}const f=o.source.split(`
`).map(h=>h.trim().replace("at ","")).filter(h=>h!=="");return c(f,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),f.reverse()}const i=new xr;for(const o of r){const c=s(o);for(let f=0;f<=c.length;f++)i.add(c.slice(0,f).join(`
`),o)}r.sort(Ie(o=>o.idx,Je));let a="",u=0;for(const o of r.slice(0,t)){u++;const c=s(o),f=[];for(let h=0;h<c.length;h++){let g=c[h];g=`(shared with ${i.get(c.slice(0,h+1).join(`
`)).size}/${r.length} leaks) at ${g}`;const d=i.get(c.slice(0,h).join(`
`)),p=ca([...d].map(v=>s(v)[h]),v=>v);delete p[c[h]];for(const[v,w]of Object.entries(p))f.unshift(`    - stacktraces of ${w.length} other leaks continue with ${v}`);f.unshift(g)}a+=`


==================== Leaking disposable ${u}/${r.length}: ${o.value.constructor.name} ====================
${f.join(`
`)}
============================================================

`}return r.length>t&&(a+=`


... and ${r.length-t} more leaking disposables

`),{leaks:r,details:a}}};function Fa(e){Ye=e}if(ka){const e="__is_disposable_tracked__";Fa(new class{trackDisposable(t){const n=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(n)},3e3)}setParent(t,n){if(t&&t!==Ze.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==Ze.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function xn(e){return Ye?.trackDisposable(e),e}function En(e){Ye?.markAsDisposed(e)}function Mn(e,t){Ye?.setParent(e,t)}function Pa(e,t){if(Ye)for(const n of e)Ye.setParent(n,t)}function Er(e){if($t.is(e)){const t=[];for(const n of e)if(n)try{n.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function Ta(...e){const t=Bt(()=>Er(e));return Pa(e,t),t}function Bt(e){const t=xn({dispose:ha(()=>{En(t),e()})});return t}var yn=class Hi{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,xn(this)}dispose(){this.g||(En(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{Er(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return Mn(t,this),this.g?Hi.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),Mn(t,null))}},Ze=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new yn,xn(this),Mn(this.q,this)}dispose(){En(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},J=class fn{static{this.Undefined=new fn(void 0)}constructor(t){this.element=t,this.next=fn.Undefined,this.prev=fn.Undefined}},_a=class{constructor(){this.a=J.Undefined,this.b=J.Undefined,this.c=0}get size(){return this.c}isEmpty(){return this.a===J.Undefined}clear(){let e=this.a;for(;e!==J.Undefined;){const t=e.next;e.prev=J.Undefined,e.next=J.Undefined,e=t}this.a=J.Undefined,this.b=J.Undefined,this.c=0}unshift(e){return this.d(e,!1)}push(e){return this.d(e,!0)}d(e,t){const n=new J(e);if(this.a===J.Undefined)this.a=n,this.b=n;else if(t){const s=this.b;this.b=n,n.prev=s,s.next=n}else{const s=this.a;this.a=n,n.next=s,s.prev=n}this.c+=1;let r=!1;return()=>{r||(r=!0,this.e(n))}}shift(){if(this.a!==J.Undefined){const e=this.a.element;return this.e(this.a),e}}pop(){if(this.b!==J.Undefined){const e=this.b.element;return this.e(this.b),e}}e(e){if(e.prev!==J.Undefined&&e.next!==J.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===J.Undefined&&e.next===J.Undefined?(this.a=J.Undefined,this.b=J.Undefined):e.next===J.Undefined?(this.b=this.b.prev,this.b.next=J.Undefined):e.prev===J.Undefined&&(this.a=this.a.next,this.a.prev=J.Undefined);this.c-=1}*[Symbol.iterator](){let e=this.a;for(;e!==J.Undefined;)yield e.element,e=e.next}},Da=globalThis.performance.now.bind(globalThis.performance),Mr=class zi{static create(t){return new zi(t)}constructor(t){this.c=t===!1?Date.now:Da,this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},yr=!1,Sa=!1,Vt;(function(e){e.None=()=>Ze.None;function t(T){if(Sa){const{onDidAddListener:E}=T,P=kn.create();let y=0;T.onDidAddListener=()=>{++y===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),P.print()),E?.()}}}function n(T,E){return m(T,()=>{},0,void 0,!0,void 0,E)}e.defer=n;function r(T){return(E,P=null,y)=>{let $=!1,I;return I=T(H=>{if(!$)return I?I.dispose():$=!0,E.call(P,H)},null,y),$&&I.dispose(),I}}e.once=r;function s(T,E){return e.once(e.filter(T,E))}e.onceIf=s;function i(T,E,P){return h((y,$=null,I)=>T(H=>y.call($,E(H)),null,I),P)}e.map=i;function a(T,E,P){return h((y,$=null,I)=>T(H=>{E(H),y.call($,H)},null,I),P)}e.forEach=a;function u(T,E,P){return h((y,$=null,I)=>T(H=>E(H)&&y.call($,H),null,I),P)}e.filter=u;function o(T){return T}e.signal=o;function c(...T){return(E,P=null,y)=>{const $=Ta(...T.map(I=>I(H=>E.call(P,H))));return g($,y)}}e.any=c;function f(T,E,P,y){let $=P;return i(T,I=>($=E($,I),$),y)}e.reduce=f;function h(T,E){let P;const y={onWillAddFirstListener(){P=T($.fire,$)},onDidRemoveLastListener(){P?.dispose()}};E||t(y);const $=new ge(y);return E?.add($),$.event}function g(T,E){return E instanceof Array?E.push(T):E&&E.add(T),T}function m(T,E,P=100,y=!1,$=!1,I,H){let te,se,qe,Dt=0,dt;const dr={leakWarningThreshold:I,onWillAddFirstListener(){te=T(na=>{Dt++,se=E(se,na),y&&!qe&&(St.fire(se),se=void 0),dt=()=>{const ra=se;se=void 0,qe=void 0,(!y||Dt>1)&&St.fire(ra),Dt=0},typeof P=="number"?(qe&&clearTimeout(qe),qe=setTimeout(dt,P)):qe===void 0&&(qe=null,queueMicrotask(dt))})},onWillRemoveListener(){$&&Dt>0&&dt?.()},onDidRemoveLastListener(){dt=void 0,te.dispose()}};H||t(dr);const St=new ge(dr);return H?.add(St),St.event}e.debounce=m;function d(T,E=0,P){return e.debounce(T,(y,$)=>y?(y.push($),y):[$],E,void 0,!0,void 0,P)}e.accumulate=d;function p(T,E=(y,$)=>y===$,P){let y=!0,$;return u(T,I=>{const H=y||!E(I,$);return y=!1,$=I,H},P)}e.latch=p;function v(T,E,P){return[e.filter(T,E,P),e.filter(T,y=>!E(y),P)]}e.split=v;function w(T,E=!1,P=[],y){let $=P.slice(),I=T(se=>{$?$.push(se):te.fire(se)});y&&y.add(I);const H=()=>{$?.forEach(se=>te.fire(se)),$=null},te=new ge({onWillAddFirstListener(){I||(I=T(se=>te.fire(se)),y&&y.add(I))},onDidAddFirstListener(){$&&(E?setTimeout(H):H())},onDidRemoveLastListener(){I&&I.dispose(),I=null}});return y&&y.add(te),te.event}e.buffer=w;function A(T,E){return(y,$,I)=>{const H=E(new x);return T(function(te){const se=H.evaluate(te);se!==N&&y.call($,se)},void 0,I)}}e.chain=A;const N=Symbol("HaltChainable");class x{constructor(){this.f=[]}map(E){return this.f.push(E),this}forEach(E){return this.f.push(P=>(E(P),P)),this}filter(E){return this.f.push(P=>E(P)?P:N),this}reduce(E,P){let y=P;return this.f.push($=>(y=E(y,$),y)),this}latch(E=(P,y)=>P===y){let P=!0,y;return this.f.push($=>{const I=P||!E($,y);return P=!1,y=$,I?$:N}),this}evaluate(E){for(const P of this.f)if(E=P(E),E===N)break;return E}}function k(T,E,P=y=>y){const y=(...te)=>H.fire(P(...te)),$=()=>T.on(E,y),I=()=>T.removeListener(E,y),H=new ge({onWillAddFirstListener:$,onDidRemoveLastListener:I});return H.event}e.fromNodeEventEmitter=k;function M(T,E,P=y=>y){const y=(...te)=>H.fire(P(...te)),$=()=>T.addEventListener(E,y),I=()=>T.removeEventListener(E,y),H=new ge({onWillAddFirstListener:$,onDidRemoveLastListener:I});return H.event}e.fromDOMEventEmitter=M;function L(T,E){let P;const y=new Promise(($,I)=>{const H=r(T)($,null,E);P=()=>H.dispose()});return y.cancel=P,y}e.toPromise=L;function R(T){const E=new ge;return T.then(P=>{E.fire(P)},()=>{E.fire(void 0)}).finally(()=>{E.dispose()}),E.event}e.fromPromise=R;function F(T,E){return T(P=>E.fire(P))}e.forward=F;function U(T,E,P){return E(P),T(y=>E(y))}e.runAndSubscribe=U;class j{constructor(E,P){this._observable=E,this.f=0,this.g=!1;const y={onWillAddFirstListener:()=>{E.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{E.removeObserver(this)}};P||t(y),this.emitter=new ge(y),P&&P.add(this.emitter)}beginUpdate(E){this.f++}handlePossibleChange(E){}handleChange(E,P){this.g=!0}endUpdate(E){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function B(T,E){return new j(T,E).emitter.event}e.fromObservable=B;function pe(T){return(E,P,y)=>{let $=0,I=!1;const H={beginUpdate(){$++},endUpdate(){$--,$===0&&(T.reportChanges(),I&&(I=!1,E.call(P)))},handlePossibleChange(){},handleChange(){I=!0}};T.addObserver(H),T.reportChanges();const te={dispose(){T.removeObserver(H)}};return y instanceof yn?y.add(te):Array.isArray(y)&&y.push(te),te}}e.fromObservableLight=pe})(Vt||(Vt={}));var $a=class hr{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${hr.f++}`,hr.all.add(this)}start(t){this.g=new Mr,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},kr=-1,Ba=class Ci{static{this.f=1}constructor(t,n,r=(Ci.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=n,this.name=r,this.h=0}dispose(){this.g?.clear()}check(t,n){const r=this.threshold;if(r<=0||n<r)return;this.g||(this.g=new Map);const s=this.g.get(t.value)||0;if(this.g.set(t.value,s+1),this.h-=1,this.h<=0){this.h=r*.5;const[i,a]=this.getMostFrequentStack(),u=`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${a}):`;console.warn(u),console.warn(i);const o=new Va(u,i);this.j(o)}return()=>{const i=this.g.get(t.value)||0;this.g.set(t.value,i-1)}}getMostFrequentStack(){if(!this.g)return;let t,n=0;for(const[r,s]of this.g)(!t||n<s)&&(t=[r,s],n=s);return t}},kn=class Gi{static create(){const t=new Error;return new Gi(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},Va=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},qa=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},Ia=0,qt=class{constructor(e){this.value=e,this.id=Ia++}},Ua=2,ja=(e,t)=>{if(e instanceof qt)t(e);else for(let n=0;n<e.length;n++){const r=e[n];r&&t(r)}},ge=class{constructor(e){this.A=0,this.g=e,this.j=kr>0||this.g?.leakWarningThreshold?new Ba(e?.onListenerError??bt,this.g?.leakWarningThreshold??kr):void 0,this.m=this.g?._profName?new $a(this.g._profName):void 0,this.z=this.g?.deliveryQueue}dispose(){if(!this.q){if(this.q=!0,this.z?.current===this&&this.z.reset(),this.w){if(yr){const e=this.w;queueMicrotask(()=>{ja(e,t=>t.stack?.print())})}this.w=void 0,this.A=0}this.g?.onDidRemoveLastListener?.(),this.j?.dispose()}}get event(){return this.u??=(e,t,n)=>{if(this.j&&this.A>this.j.threshold**2){const u=`[${this.j.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.A} vs ${this.j.threshold})`;console.warn(u);const o=this.j.getMostFrequentStack()??["UNKNOWN stack",-1],c=new qa(`${u}. HINT: Stack shows most frequent listener (${o[1]}-times)`,o[0]);return(this.g?.onListenerError||bt)(c),Ze.None}if(this.q)return Ze.None;t&&(e=e.bind(t));const r=new qt(e);let s,i;this.j&&this.A>=Math.ceil(this.j.threshold*.2)&&(r.stack=kn.create(),s=this.j.check(r.stack,this.A+1)),yr&&(r.stack=i??kn.create()),this.w?this.w instanceof qt?(this.z??=new Oa,this.w=[this.w,r]):this.w.push(r):(this.g?.onWillAddFirstListener?.(this),this.w=r,this.g?.onDidAddFirstListener?.(this)),this.g?.onDidAddListener?.(this),this.A++;const a=Bt(()=>{s?.(),this.B(r)});return n instanceof yn?n.add(a):Array.isArray(n)&&n.push(a),a},this.u}B(e){if(this.g?.onWillRemoveListener?.(this),!this.w)return;if(this.A===1){this.w=void 0,this.g?.onDidRemoveLastListener?.(this),this.A=0;return}const t=this.w,n=t.indexOf(e);if(n===-1)throw console.log("disposed?",this.q),console.log("size?",this.A),console.log("arr?",JSON.stringify(this.w)),new Error("Attempted to dispose unknown listener");this.A--,t[n]=void 0;const r=this.z.current===this;if(this.A*Ua<=t.length){let s=0;for(let i=0;i<t.length;i++)t[i]?t[s++]=t[i]:r&&s<this.z.end&&(this.z.end--,s<this.z.i&&this.z.i--);t.length=s}}C(e,t){if(!e)return;const n=this.g?.onListenerError||bt;if(!n){e.value(t);return}try{e.value(t)}catch(r){n(r)}}D(e){const t=e.current.w;for(;e.i<e.end;)this.C(t[e.i++],e.value);e.reset()}fire(e){if(this.z?.current&&(this.D(this.z),this.m?.stop()),this.m?.start(this.A),this.w)if(this.w instanceof qt)this.C(this.w,e);else{const t=this.z;t.enqueue(this,e,this.w.length),this.D(t)}this.m?.stop()}hasListeners(){return this.A>0}},Oa=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function Wa(){return globalThis._VSCODE_NLS_MESSAGES}function Fr(){return globalThis._VSCODE_NLS_LANGUAGE}var Ha=Fr()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function Pr(e,t){let n;return t.length===0?n=e:n=e.replace(/\{(\d+)\}/g,(r,s)=>{const i=s[0],a=t[i];let u=r;return typeof a=="string"?u=a:(typeof a=="number"||typeof a=="boolean"||a===void 0||a===null)&&(u=String(a)),u}),Ha&&(n="\uFF3B"+n.replace(/[aouei]/g,"$&$&")+"\uFF3D"),n}function S(e,t,...n){return Pr(typeof e=="number"?za(e,t):t,n)}function za(e,t){const n=Wa()?.[e];if(typeof n!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return n}var Qe="en",It=!1,Ut=!1,vt=!1,Ca=!1,Tr=!1,Fn=!1,Ga=!1,Xa=!1,Ja=!1,Ya=!1,jt=void 0,Ot=Qe,_r=Qe,Za=void 0,Re=void 0,Ae=globalThis,ae=void 0;typeof Ae.vscode<"u"&&typeof Ae.vscode.process<"u"?ae=Ae.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(ae=process);var Dr=typeof ae?.versions?.electron=="string",Qa=Dr&&ae?.type==="renderer";if(typeof ae=="object"){It=ae.platform==="win32",Ut=ae.platform==="darwin",vt=ae.platform==="linux",Ca=vt&&!!ae.env.SNAP&&!!ae.env.SNAP_REVISION,Ga=Dr,Ja=!!ae.env.CI||!!ae.env.BUILD_ARTIFACTSTAGINGDIRECTORY||!!ae.env.GITHUB_WORKSPACE,jt=Qe,Ot=Qe;const e=ae.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);jt=t.userLocale,_r=t.osLocale,Ot=t.resolvedLanguage||Qe,Za=t.languagePack?.translationsConfigFile}catch{}Tr=!0}else typeof navigator=="object"&&!Qa?(Re=navigator.userAgent,It=Re.indexOf("Windows")>=0,Ut=Re.indexOf("Macintosh")>=0,Xa=(Re.indexOf("Macintosh")>=0||Re.indexOf("iPad")>=0||Re.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,vt=Re.indexOf("Linux")>=0,Ya=Re?.indexOf("Mobi")>=0,Fn=!0,Ot=Fr()||Qe,jt=navigator.language.toLowerCase(),_r=jt):console.error("Unable to resolve platform.");var Sr;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(Sr||(Sr={}));var Pn=0;Ut?Pn=1:It?Pn=3:vt&&(Pn=2);var Ke=It,Ka=Ut,el=vt,tl=Tr,nl=Fn,rl=Fn&&typeof Ae.importScripts=="function",sl=rl?Ae.origin:void 0,Le=Re,Pe=Ot,$r;(function(e){function t(){return Pe}e.value=t;function n(){return Pe.length===2?Pe==="en":Pe.length>=3?Pe[0]==="e"&&Pe[1]==="n"&&Pe[2]==="-":!1}e.isDefaultVariant=n;function r(){return Pe==="en"}e.isDefault=r})($r||($r={}));var il=typeof Ae.postMessage=="function"&&!Ae.importScripts,al=(()=>{if(il){const e=[];Ae.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let r=0,s=e.length;r<s;r++){const i=e[r];if(i.id===n.data.vscodeScheduleAsyncWork){e.splice(r,1),i.callback();return}}});let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),Ae.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})(),Br;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(Br||(Br={}));var ll=!!(Le&&Le.indexOf("Chrome")>=0),ro=!!(Le&&Le.indexOf("Firefox")>=0),so=!!(!ll&&Le&&Le.indexOf("Safari")>=0),io=!!(Le&&Le.indexOf("Edg/")>=0),ao=!!(Le&&Le.indexOf("Android")>=0),Vr=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}),Wt;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof Ht?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Vt.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:Vr})})(Wt||(Wt={}));var Ht=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?Vr:(this.b||(this.b=new ge),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},ul=class{constructor(e){this.f=void 0,this.g=void 0,this.g=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new Ht),this.f}cancel(){this.f?this.f instanceof Ht&&this.f.cancel():this.f=Wt.Cancelled}dispose(e=!1){e&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof Ht&&this.f.dispose():this.f=Wt.None}};function ol(e){return e}var cl=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=ol):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}},Tn=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function hl(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function fl(e){return e.source==="^"||e.source==="^$"||e.source==="$"||e.source==="^\\s*$"?!1:!!(e.exec("")&&e.lastIndex===0)}function qr(e){return e.split(/\r\n|\r|\n/)}function gl(e){for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}return-1}function ml(e,t=e.length-1){for(let n=t;n>=0;n--){const r=e.charCodeAt(n);if(r!==32&&r!==9)return n}return-1}function dl(e,t){return e<t?-1:e>t?1:0}function bl(e,t,n=0,r=e.length,s=0,i=t.length){for(;n<r&&s<i;n++,s++){const o=e.charCodeAt(n),c=t.charCodeAt(s);if(o<c)return-1;if(o>c)return 1}const a=r-n,u=i-s;return a<u?-1:a>u?1:0}function Ir(e,t,n=0,r=e.length,s=0,i=t.length){for(;n<r&&s<i;n++,s++){let o=e.charCodeAt(n),c=t.charCodeAt(s);if(o===c)continue;if(o>=128||c>=128)return bl(e.toLowerCase(),t.toLowerCase(),n,r,s,i);Ur(o)&&(o-=32),Ur(c)&&(c-=32);const f=o-c;if(f!==0)return f}const a=r-n,u=i-s;return a<u?-1:a>u?1:0}function Ur(e){return e>=97&&e<=122}function jr(e){return e>=65&&e<=90}function wl(e,t){return e.length===t.length&&Ir(e,t)===0}function vl(e,t){const n=t.length;return t.length>e.length?!1:Ir(e,t,0,n)===0}function zt(e,t){const n=Math.min(e.length,t.length);let r;for(r=0;r<n;r++)if(e.charCodeAt(r)!==t.charCodeAt(r))return r;return n}function Ct(e,t){const n=Math.min(e.length,t.length);let r;const s=e.length-1,i=t.length-1;for(r=0;r<n;r++)if(e.charCodeAt(s-r)!==t.charCodeAt(i-r))return r;return n}function Gt(e){return 55296<=e&&e<=56319}function _n(e){return 56320<=e&&e<=57343}function Or(e,t){return(e-55296<<10)+(t-56320)+65536}function pl(e,t,n){const r=e.charCodeAt(n);if(Gt(r)&&n+1<t){const s=e.charCodeAt(n+1);if(_n(s))return Or(r,s)}return r}var Ll=/^[\t\n\r\x20-\x7E]*$/;function Nl(e){return Ll.test(e)}var Rl=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,Al=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,xl=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,lo=new RegExp("(?:"+[Rl.source,Al.source,xl.source].join("|")+")","g"),uo="\uFEFF",Wr;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(Wr||(Wr={}));var oo=class yt{static{this.c=null}static getInstance(){return yt.c||(yt.c=new yt),yt.c}constructor(){this.d=El()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const n=this.d,r=n.length/3;let s=1;for(;s<=r;)if(t<n[3*s])s=2*s;else if(t>n[3*s+1])s=2*s+1;else return n[3*s+2];return 0}};function El(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var Hr;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(Hr||(Hr={}));var Dn=class kt{static{this.c=new Tn(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new cl({getCacheKey:JSON.stringify},t=>{function n(f){const h=new Map;for(let g=0;g<f.length;g+=2)h.set(f[g],f[g+1]);return h}function r(f,h){const g=new Map(f);for(const[m,d]of h)g.set(m,d);return g}function s(f,h){if(!f)return h;const g=new Map;for(const[m,d]of f)h.has(m)&&g.set(m,d);return g}const i=this.c.value;let a=t.filter(f=>!f.startsWith("_")&&f in i);a.length===0&&(a=["_default"]);let u;for(const f of a){const h=n(i[f]);u=s(u,h)}const o=n(i._common),c=r(o,u);return new kt(c)})}static getInstance(t){return kt.d.get(Array.from(t))}static{this.e=new Tn(()=>Object.keys(kt.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return kt.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let n=0;n<t.length;n++){const r=t.codePointAt(n);if(typeof r=="number"&&this.isAmbiguous(r))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},Sn=class Ft{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(Ft.c())].flat())),this.d}static isInvisibleCharacter(t){return Ft.e().has(t)}static containsInvisibleCharacter(t){for(let n=0;n<t.length;n++){const r=t.codePointAt(n);if(typeof r=="number"&&(Ft.isInvisibleCharacter(r)||r===32))return!0}return!1}static get codePoints(){return Ft.e()}},$n="default",Ml="$initialize",zr;(function(e){e[e.Request=0]="Request",e[e.Reply=1]="Reply",e[e.SubscribeEvent=2]="SubscribeEvent",e[e.Event=3]="Event",e[e.UnsubscribeEvent=4]="UnsubscribeEvent"})(zr||(zr={}));var yl=class{constructor(e,t,n,r,s){this.vsWorker=e,this.req=t,this.channel=n,this.method=r,this.args=s,this.type=0}},Cr=class{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}},kl=class{constructor(e,t,n,r,s){this.vsWorker=e,this.req=t,this.channel=n,this.eventName=r,this.arg=s,this.type=2}},Fl=class{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}},Pl=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},Tl=class{constructor(e){this.a=-1,this.g=e,this.b=0,this.c=Object.create(null),this.d=new Map,this.f=new Map}setWorkerId(e){this.a=e}sendMessage(e,t,n){const r=String(++this.b);return new Promise((s,i)=>{this.c[r]={resolve:s,reject:i},this.o(new yl(this.a,r,e,t,n))})}listen(e,t,n){let r=null;const s=new ge({onWillAddFirstListener:()=>{r=String(++this.b),this.d.set(r,s),this.o(new kl(this.a,r,e,t,n))},onDidRemoveLastListener:()=>{this.d.delete(r),this.o(new Pl(this.a,r)),r=null}});return s.event}handleMessage(e){!e||!e.vsWorker||this.a!==-1&&e.vsWorker!==this.a||this.h(e)}createProxyToRemoteChannel(e,t){const n={get:(r,s)=>(typeof s=="string"&&!r[s]&&(Xr(s)?r[s]=i=>this.listen(e,s,i):Gr(s)?r[s]=this.listen(e,s,void 0):s.charCodeAt(0)===36&&(r[s]=async(...i)=>(await t?.(),this.sendMessage(e,s,i)))),r[s])};return new Proxy(Object.create(null),n)}h(e){switch(e.type){case 1:return this.j(e);case 0:return this.k(e);case 2:return this.l(e);case 3:return this.m(e);case 4:return this.n(e)}}j(e){if(!this.c[e.seq]){console.warn("Got reply to unknown seq");return}const t=this.c[e.seq];if(delete this.c[e.seq],e.err){let n=e.err;e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),t.reject(n);return}t.resolve(e.res)}k(e){const t=e.req;this.g.handleMessage(e.channel,e.method,e.args).then(r=>{this.o(new Cr(this.a,t,r,void 0))},r=>{r.detail instanceof Error&&(r.detail=vn(r.detail)),this.o(new Cr(this.a,t,void 0,vn(r)))})}l(e){const t=e.req,n=this.g.handleEvent(e.channel,e.eventName,e.arg)(r=>{this.o(new Fl(this.a,t,r))});this.f.set(t,n)}m(e){if(!this.d.has(e.req)){console.warn("Got event for unknown req");return}this.d.get(e.req).fire(e.event)}n(e){if(!this.f.has(e.req)){console.warn("Got unsubscribe for unknown req");return}this.f.get(e.req).dispose(),this.f.delete(e.req)}o(e){const t=[];if(e.type===0)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this.g.sendMessage(e,t)}};function Gr(e){return e[0]==="o"&&e[1]==="n"&&jr(e.charCodeAt(2))}function Xr(e){return/^onDynamic/.test(e)&&jr(e.charCodeAt(9))}var _l=class{constructor(e,t){this.b=new Map,this.c=new Map,this.a=new Tl({sendMessage:(n,r)=>{e(n,r)},handleMessage:(n,r,s)=>this.d(n,r,s),handleEvent:(n,r,s)=>this.f(n,r,s)}),this.requestHandler=t(this)}onmessage(e){this.a.handleMessage(e)}d(e,t,n){if(e===$n&&t===Ml)return this.g(n[0]);const r=e===$n?this.requestHandler:this.b.get(e);if(!r)return Promise.reject(new Error(`Missing channel ${e} on worker thread`));if(typeof r[t]!="function")return Promise.reject(new Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(r[t].apply(r,n))}catch(s){return Promise.reject(s)}}f(e,t,n){const r=e===$n?this.requestHandler:this.b.get(e);if(!r)throw new Error(`Missing channel ${e} on worker thread`);if(Xr(t)){const s=r[t].call(r,n);if(typeof s!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return s}if(Gr(t)){const s=r[t];if(typeof s!="function")throw new Error(`Missing event ${t} on request handler.`);return s}throw new Error(`Malformed event name ${t}`)}setChannel(e,t){this.b.set(e,t)}getChannel(e){if(!this.c.has(e)){const t=this.a.createProxyToRemoteChannel(e);this.c.set(e,t)}return this.c.get(e)}async g(e){this.a.setWorkerId(e)}},Bn=!1;function Dl(e){if(Bn)throw new Error("WebWorker already initialized!");Bn=!0;const t=new _l(n=>globalThis.postMessage(n),n=>e(n));return globalThis.onmessage=n=>{t.onmessage(n.data)},t}function Sl(e){globalThis.onmessage=t=>{Bn||Dl(e)}}var Te=class{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}},pt=typeof Buffer<"u",$l=new Tn(()=>new Uint8Array(256)),Vn,qn,Bl=class de{static alloc(t){return pt?new de(Buffer.allocUnsafe(t)):new de(new Uint8Array(t))}static wrap(t){return pt&&!Buffer.isBuffer(t)&&(t=Buffer.from(t.buffer,t.byteOffset,t.byteLength)),new de(t)}static fromString(t,n){return!(n?.dontUseNodeBuffer||!1)&&pt?new de(Buffer.from(t)):(Vn||(Vn=new TextEncoder),new de(Vn.encode(t)))}static fromByteArray(t){const n=de.alloc(t.length);for(let r=0,s=t.length;r<s;r++)n.buffer[r]=t[r];return n}static concat(t,n){if(typeof n>"u"){n=0;for(let i=0,a=t.length;i<a;i++)n+=t[i].byteLength}const r=de.alloc(n);let s=0;for(let i=0,a=t.length;i<a;i++){const u=t[i];r.set(u,s),s+=u.byteLength}return r}static isNativeBuffer(t){return pt&&Buffer.isBuffer(t)}constructor(t){this.buffer=t,this.byteLength=this.buffer.byteLength}clone(){const t=de.alloc(this.byteLength);return t.set(this),t}toString(){return pt?this.buffer.toString():(qn||(qn=new TextDecoder),qn.decode(this.buffer))}slice(t,n){return new de(this.buffer.subarray(t,n))}set(t,n){if(t instanceof de)this.buffer.set(t.buffer,n);else if(t instanceof Uint8Array)this.buffer.set(t,n);else if(t instanceof ArrayBuffer)this.buffer.set(new Uint8Array(t),n);else if(ArrayBuffer.isView(t))this.buffer.set(new Uint8Array(t.buffer,t.byteOffset,t.byteLength),n);else throw new Error("Unknown argument 'array'")}readUInt32BE(t){return ql(this.buffer,t)}writeUInt32BE(t,n){Il(this.buffer,t,n)}readUInt32LE(t){return Ul(this.buffer,t)}writeUInt32LE(t,n){jl(this.buffer,t,n)}readUInt8(t){return Ol(this.buffer,t)}writeUInt8(t,n){Wl(this.buffer,t,n)}indexOf(t,n=0){return Vl(this.buffer,t instanceof de?t.buffer:t,n)}equals(t){return this===t?!0:this.byteLength!==t.byteLength?!1:this.buffer.every((n,r)=>n===t.buffer[r])}};function Vl(e,t,n=0){const r=t.byteLength,s=e.byteLength;if(r===0)return 0;if(r===1)return e.indexOf(t[0]);if(r>s-n)return-1;const i=$l.value;i.fill(t.length);for(let c=0;c<t.length;c++)i[t[c]]=t.length-c-1;let a=n+t.length-1,u=a,o=-1;for(;a<s;)if(e[a]===t[u]){if(u===0){o=a;break}a--,u--}else a+=Math.max(t.length-u,i[e[a]]),u=t.length-1;return o}function ql(e,t){return e[t]*2**24+e[t+1]*2**16+e[t+2]*2**8+e[t+3]}function Il(e,t,n){e[n+3]=t,t=t>>>8,e[n+2]=t,t=t>>>8,e[n+1]=t,t=t>>>8,e[n]=t}function Ul(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0|e[t+2]<<16>>>0|e[t+3]<<24>>>0}function jl(e,t,n){e[n+0]=t&255,t=t>>>8,e[n+1]=t&255,t=t>>>8,e[n+2]=t&255,t=t>>>8,e[n+3]=t&255}function Ol(e,t){return e[t]}function Wl(e,t,n){e[n]=t}var Jr="0123456789abcdef";function Hl({buffer:e}){let t="";for(let n=0;n<e.length;n++){const r=e[n];t+=Jr[r>>>4],t+=Jr[r&15]}return t}function Yr(e,t){return(t<<5)-t+e|0}function zl(e,t){t=Yr(149417,t);for(let n=0,r=e.length;n<r;n++)t=Yr(e.charCodeAt(n),t);return t}var Zr;(function(e){e[e.BLOCK_SIZE=64]="BLOCK_SIZE",e[e.UNICODE_REPLACEMENT=65533]="UNICODE_REPLACEMENT"})(Zr||(Zr={}));function In(e,t,n=32){const r=n-t,s=~((1<<r)-1);return(e<<t|(s&e)>>>r)>>>0}function Lt(e,t=32){return e instanceof ArrayBuffer?Hl(Bl.wrap(new Uint8Array(e))):(e>>>0).toString(16).padStart(t/4,"0")}var co=class Xi{static{this.g=new DataView(new ArrayBuffer(320))}constructor(){this.h=1732584193,this.l=4023233417,this.m=2562383102,this.n=271733878,this.o=3285377520,this.p=new Uint8Array(67),this.q=new DataView(this.p.buffer),this.r=0,this.t=0,this.u=0,this.v=!1}update(t){const n=t.length;if(n===0)return;const r=this.p;let s=this.r,i=this.u,a,u;for(i!==0?(a=i,u=-1,i=0):(a=t.charCodeAt(0),u=0);;){let o=a;if(Gt(a))if(u+1<n){const c=t.charCodeAt(u+1);_n(c)?(u++,o=Or(a,c)):o=65533}else{i=a;break}else _n(a)&&(o=65533);if(s=this.w(r,s,o),u++,u<n)a=t.charCodeAt(u);else break}this.r=s,this.u=i}w(t,n,r){return r<128?t[n++]=r:r<2048?(t[n++]=192|(r&1984)>>>6,t[n++]=128|(r&63)>>>0):r<65536?(t[n++]=224|(r&61440)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0):(t[n++]=240|(r&1835008)>>>18,t[n++]=128|(r&258048)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0),n>=64&&(this.y(),n-=64,this.t+=64,t[0]=t[64],t[1]=t[65],t[2]=t[66]),n}digest(){return this.v||(this.v=!0,this.u&&(this.u=0,this.r=this.w(this.p,this.r,65533)),this.t+=this.r,this.x()),Lt(this.h)+Lt(this.l)+Lt(this.m)+Lt(this.n)+Lt(this.o)}x(){this.p[this.r++]=128,this.p.subarray(this.r).fill(0),this.r>56&&(this.y(),this.p.fill(0));const t=8*this.t;this.q.setUint32(56,Math.floor(t/4294967296),!1),this.q.setUint32(60,t%4294967296,!1),this.y()}y(){const t=Xi.g,n=this.q;for(let h=0;h<64;h+=4)t.setUint32(h,n.getUint32(h,!1),!1);for(let h=64;h<320;h+=4)t.setUint32(h,In(t.getUint32(h-12,!1)^t.getUint32(h-32,!1)^t.getUint32(h-56,!1)^t.getUint32(h-64,!1),1),!1);let r=this.h,s=this.l,i=this.m,a=this.n,u=this.o,o,c,f;for(let h=0;h<80;h++)h<20?(o=s&i|~s&a,c=1518500249):h<40?(o=s^i^a,c=1859775393):h<60?(o=s&i|s&a|i&a,c=2400959708):(o=s^i^a,c=3395469782),f=In(r,5)+o+u+c+t.getUint32(h*4,!1)&4294967295,u=a,a=i,i=In(s,30),s=r,r=f;this.h=this.h+r&4294967295,this.l=this.l+s&4294967295,this.m=this.m+i&4294967295,this.n=this.n+a&4294967295,this.o=this.o+u&4294967295}},Qr=class{constructor(e){this.a=e}getElements(){const e=this.a,t=new Int32Array(e.length);for(let n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}};function Cl(e,t,n){return new t1(new Qr(e),new Qr(t)).ComputeDiff(n).changes}var et=class{static Assert(e,t){if(!e)throw new Error(t)}},tt=class{static Copy(e,t,n,r,s){for(let i=0;i<s;i++)n[r+i]=e[t+i]}static Copy2(e,t,n,r,s){for(let i=0;i<s;i++)n[r+i]=e[t+i]}},Kr;(function(e){e[e.MaxDifferencesHistory=1447]="MaxDifferencesHistory"})(Kr||(Kr={}));var e1=class{constructor(){this.a=[],this.b=1073741824,this.c=1073741824,this.d=0,this.e=0}MarkNextChange(){(this.d>0||this.e>0)&&this.a.push(new Te(this.b,this.d,this.c,this.e)),this.d=0,this.e=0,this.b=1073741824,this.c=1073741824}AddOriginalElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.d++}AddModifiedElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.e++}getChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a}getReverseChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a.reverse(),this.a}},t1=class ct{constructor(t,n,r=null){this.a=r,this.b=t,this.c=n;const[s,i,a]=ct.p(t),[u,o,c]=ct.p(n);this.d=a&&c,this.e=s,this.f=i,this.g=u,this.h=o,this.m=[],this.n=[]}static o(t){return t.length>0&&typeof t[0]=="string"}static p(t){const n=t.getElements();if(ct.o(n)){const r=new Int32Array(n.length);for(let s=0,i=n.length;s<i;s++)r[s]=zl(n[s],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}q(t,n){return this.f[t]!==this.h[n]?!1:this.d?this.e[t]===this.g[n]:!0}r(t,n){if(!this.q(t,n))return!1;const r=ct.s(this.b,t),s=ct.s(this.c,n);return r===s}static s(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}u(t,n){return this.f[t]!==this.f[n]?!1:this.d?this.e[t]===this.e[n]:!0}v(t,n){return this.h[t]!==this.h[n]?!1:this.d?this.g[t]===this.g[n]:!0}ComputeDiff(t){return this.w(0,this.f.length-1,0,this.h.length-1,t)}w(t,n,r,s,i){const a=[!1];let u=this.x(t,n,r,s,a);return i&&(u=this.A(u)),{quitEarly:a[0],changes:u}}x(t,n,r,s,i){for(i[0]=!1;t<=n&&r<=s&&this.q(t,r);)t++,r++;for(;n>=t&&s>=r&&this.q(n,s);)n--,s--;if(t>n||r>s){let h;return r<=s?(et.Assert(t===n+1,"originalStart should only be one more than originalEnd"),h=[new Te(t,0,r,s-r+1)]):t<=n?(et.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),h=[new Te(t,n-t+1,r,0)]):(et.Assert(t===n+1,"originalStart should only be one more than originalEnd"),et.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),h=[]),h}const a=[0],u=[0],o=this.z(t,n,r,s,a,u,i),c=a[0],f=u[0];if(o!==null)return o;if(!i[0]){const h=this.x(t,c,r,f,i);let g=[];return i[0]?g=[new Te(c+1,n-(c+1)+1,f+1,s-(f+1)+1)]:g=this.x(c+1,n,f+1,s,i),this.I(h,g)}return[new Te(t,n-t+1,r,s-r+1)]}y(t,n,r,s,i,a,u,o,c,f,h,g,m,d,p,v,w,A){let N=null,x=null,k=new e1,M=n,L=r,R=m[0]-v[0]-s,F=-1073741824,U=this.m.length-1;do{const j=R+t;j===M||j<L&&c[j-1]<c[j+1]?(h=c[j+1],d=h-R-s,h<F&&k.MarkNextChange(),F=h,k.AddModifiedElement(h+1,d),R=j+1-t):(h=c[j-1]+1,d=h-R-s,h<F&&k.MarkNextChange(),F=h-1,k.AddOriginalElement(h,d+1),R=j-1-t),U>=0&&(c=this.m[U],t=c[0],M=1,L=c.length-1)}while(--U>=-1);if(N=k.getReverseChanges(),A[0]){let j=m[0]+1,B=v[0]+1;if(N!==null&&N.length>0){const pe=N[N.length-1];j=Math.max(j,pe.getOriginalEnd()),B=Math.max(B,pe.getModifiedEnd())}x=[new Te(j,g-j+1,B,p-B+1)]}else{k=new e1,M=a,L=u,R=m[0]-v[0]-o,F=1073741824,U=w?this.n.length-1:this.n.length-2;do{const j=R+i;j===M||j<L&&f[j-1]>=f[j+1]?(h=f[j+1]-1,d=h-R-o,h>F&&k.MarkNextChange(),F=h+1,k.AddOriginalElement(h+1,d+1),R=j+1-i):(h=f[j-1],d=h-R-o,h>F&&k.MarkNextChange(),F=h,k.AddModifiedElement(h+1,d+1),R=j-1-i),U>=0&&(f=this.n[U],i=f[0],M=1,L=f.length-1)}while(--U>=-1);x=k.getChanges()}return this.I(N,x)}z(t,n,r,s,i,a,u){let o=0,c=0,f=0,h=0,g=0,m=0;t--,r--,i[0]=0,a[0]=0,this.m=[],this.n=[];const d=n-t+(s-r),p=d+1,v=new Int32Array(p),w=new Int32Array(p),A=s-r,N=n-t,x=t-r,k=n-s,L=(N-A)%2===0;v[A]=t,w[N]=n,u[0]=!1;for(let R=1;R<=d/2+1;R++){let F=0,U=0;f=this.K(A-R,R,A,p),h=this.K(A+R,R,A,p);for(let B=f;B<=h;B+=2){B===f||B<h&&v[B-1]<v[B+1]?o=v[B+1]:o=v[B-1]+1,c=o-(B-A)-x;const pe=o;for(;o<n&&c<s&&this.q(o+1,c+1);)o++,c++;if(v[B]=o,o+c>F+U&&(F=o,U=c),!L&&Math.abs(B-N)<=R-1&&o>=w[B])return i[0]=o,a[0]=c,pe<=w[B]&&R<=1448?this.y(A,f,h,x,N,g,m,k,v,w,o,n,i,c,s,a,L,u):null}const j=(F-t+(U-r)-R)/2;if(this.a!==null&&!this.a(F,j))return u[0]=!0,i[0]=F,a[0]=U,j>0&&R<=1448?this.y(A,f,h,x,N,g,m,k,v,w,o,n,i,c,s,a,L,u):(t++,r++,[new Te(t,n-t+1,r,s-r+1)]);g=this.K(N-R,R,N,p),m=this.K(N+R,R,N,p);for(let B=g;B<=m;B+=2){B===g||B<m&&w[B-1]>=w[B+1]?o=w[B+1]-1:o=w[B-1],c=o-(B-N)-k;const pe=o;for(;o>t&&c>r&&this.q(o,c);)o--,c--;if(w[B]=o,L&&Math.abs(B-A)<=R&&o<=v[B])return i[0]=o,a[0]=c,pe>=v[B]&&R<=1448?this.y(A,f,h,x,N,g,m,k,v,w,o,n,i,c,s,a,L,u):null}if(R<=1447){let B=new Int32Array(h-f+2);B[0]=A-f+1,tt.Copy2(v,f,B,1,h-f+1),this.m.push(B),B=new Int32Array(m-g+2),B[0]=N-g+1,tt.Copy2(w,g,B,1,m-g+1),this.n.push(B)}}return this.y(A,f,h,x,N,g,m,k,v,w,o,n,i,c,s,a,L,u)}A(t){for(let n=0;n<t.length;n++){const r=t[n],s=n<t.length-1?t[n+1].originalStart:this.f.length,i=n<t.length-1?t[n+1].modifiedStart:this.h.length,a=r.originalLength>0,u=r.modifiedLength>0;for(;r.originalStart+r.originalLength<s&&r.modifiedStart+r.modifiedLength<i&&(!a||this.u(r.originalStart,r.originalStart+r.originalLength))&&(!u||this.v(r.modifiedStart,r.modifiedStart+r.modifiedLength));){const c=this.r(r.originalStart,r.modifiedStart);if(this.r(r.originalStart+r.originalLength,r.modifiedStart+r.modifiedLength)&&!c)break;r.originalStart++,r.modifiedStart++}const o=[null];if(n<t.length-1&&this.J(t[n],t[n+1],o)){t[n]=o[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const r=t[n];let s=0,i=0;if(n>0){const h=t[n-1];s=h.originalStart+h.originalLength,i=h.modifiedStart+h.modifiedLength}const a=r.originalLength>0,u=r.modifiedLength>0;let o=0,c=this.H(r.originalStart,r.originalLength,r.modifiedStart,r.modifiedLength);for(let h=1;;h++){const g=r.originalStart-h,m=r.modifiedStart-h;if(g<s||m<i||a&&!this.u(g,g+r.originalLength)||u&&!this.v(m,m+r.modifiedLength))break;const p=(g===s&&m===i?5:0)+this.H(g,r.originalLength,m,r.modifiedLength);p>c&&(c=p,o=h)}r.originalStart-=o,r.modifiedStart-=o;const f=[null];if(n>0&&this.J(t[n-1],t[n],f)){t[n-1]=f[0],t.splice(n,1),n++;continue}}if(this.d)for(let n=1,r=t.length;n<r;n++){const s=t[n-1],i=t[n],a=i.originalStart-s.originalStart-s.originalLength,u=s.originalStart,o=i.originalStart+i.originalLength,c=o-u,f=s.modifiedStart,h=i.modifiedStart+i.modifiedLength,g=h-f;if(a<5&&c<20&&g<20){const m=this.B(u,c,f,g,a);if(m){const[d,p]=m;(d!==s.originalStart+s.originalLength||p!==s.modifiedStart+s.modifiedLength)&&(s.originalLength=d-s.originalStart,s.modifiedLength=p-s.modifiedStart,i.originalStart=d+a,i.modifiedStart=p+a,i.originalLength=o-i.originalStart,i.modifiedLength=h-i.modifiedStart)}}}return t}B(t,n,r,s,i){if(n<i||s<i)return null;const a=t+n-i+1,u=r+s-i+1;let o=0,c=0,f=0;for(let h=t;h<a;h++)for(let g=r;g<u;g++){const m=this.C(h,g,i);m>0&&m>o&&(o=m,c=h,f=g)}return o>0?[c,f]:null}C(t,n,r){let s=0;for(let i=0;i<r;i++){if(!this.q(t+i,n+i))return 0;s+=this.e[t+i].length}return s}D(t){return t<=0||t>=this.f.length-1?!0:this.d&&/^\s*$/.test(this.e[t])}E(t,n){if(this.D(t)||this.D(t-1))return!0;if(n>0){const r=t+n;if(this.D(r-1)||this.D(r))return!0}return!1}F(t){return t<=0||t>=this.h.length-1?!0:this.d&&/^\s*$/.test(this.g[t])}G(t,n){if(this.F(t)||this.F(t-1))return!0;if(n>0){const r=t+n;if(this.F(r-1)||this.F(r))return!0}return!1}H(t,n,r,s){const i=this.E(t,n)?1:0,a=this.G(r,s)?1:0;return i+a}I(t,n){const r=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.J(t[t.length-1],n[0],r)){const s=new Array(t.length+n.length-1);return tt.Copy(t,0,s,0,t.length-1),s[t.length-1]=r[0],tt.Copy(n,1,s,t.length,n.length-1),s}else{const s=new Array(t.length+n.length);return tt.Copy(t,0,s,0,t.length),tt.Copy(n,0,s,t.length,n.length),s}}J(t,n,r){if(et.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),et.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const s=t.originalStart;let i=t.originalLength;const a=t.modifiedStart;let u=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(i=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(u=n.modifiedStart+n.modifiedLength-t.modifiedStart),r[0]=new Te(s,i,a,u),!0}else return r[0]=null,!1}K(t,n,r,s){if(t>=0&&t<s)return t;const i=r,a=s-r-1,u=n%2===0;if(t<0){const o=i%2===0;return u===o?0:1}else{const o=a%2===0;return u===o?s-1:s-2}}},ho=new Uint32Array(65536),O=class ze{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new ze(t,n)}delta(t=0,n=0){return this.with(Math.max(1,this.lineNumber+t),Math.max(1,this.column+n))}equals(t){return ze.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return ze.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return ze.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const r=t.lineNumber|0,s=n.lineNumber|0;if(r===s){const i=t.column|0,a=n.column|0;return i-a}return r-s}clone(){return new ze(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new ze(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},_=class Q{constructor(t,n,r,s){t>r||t===r&&n>s?(this.startLineNumber=r,this.startColumn=s,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=r,this.endColumn=s)}isEmpty(){return Q.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return Q.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return Q.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return Q.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return Q.plusRange(this,t)}static plusRange(t,n){let r,s,i,a;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,s=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,s=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,s=t.startColumn),n.endLineNumber>t.endLineNumber?(i=n.endLineNumber,a=n.endColumn):n.endLineNumber===t.endLineNumber?(i=n.endLineNumber,a=Math.max(n.endColumn,t.endColumn)):(i=t.endLineNumber,a=t.endColumn),new Q(r,s,i,a)}intersectRanges(t){return Q.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,s=t.startColumn,i=t.endLineNumber,a=t.endColumn;const u=n.startLineNumber,o=n.startColumn,c=n.endLineNumber,f=n.endColumn;return r<u?(r=u,s=o):r===u&&(s=Math.max(s,o)),i>c?(i=c,a=f):i===c&&(a=Math.min(a,f)),r>i||r===i&&s>a?null:new Q(r,s,i,a)}equalsRange(t){return Q.equalsRange(this,t)}static equalsRange(t,n){return!t&&!n?!0:!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return Q.getEndPosition(this)}static getEndPosition(t){return new O(t.endLineNumber,t.endColumn)}getStartPosition(){return Q.getStartPosition(this)}static getStartPosition(t){return new O(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new Q(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new Q(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return Q.collapseToStart(this)}static collapseToStart(t){return new Q(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return Q.collapseToEnd(this)}static collapseToEnd(t){return new Q(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new Q(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}isSingleLine(){return this.startLineNumber===this.endLineNumber}static fromPositions(t,n=t){return new Q(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new Q(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static areOnlyIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber-1||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn-1||n.endLineNumber<t.startLineNumber-1||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn-1)}static compareRangesUsingStarts(t,n){if(t&&n){const i=t.startLineNumber|0,a=n.startLineNumber|0;if(i===a){const u=t.startColumn|0,o=n.startColumn|0;if(u===o){const c=t.endLineNumber|0,f=n.endLineNumber|0;if(c===f){const h=t.endColumn|0,g=n.endColumn|0;return h-g}return c-f}return u-o}return i-a}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}},n1;(function(e){e[e.MAX_SAFE_SMALL_INTEGER=1073741824]="MAX_SAFE_SMALL_INTEGER",e[e.MIN_SAFE_SMALL_INTEGER=-1073741824]="MIN_SAFE_SMALL_INTEGER",e[e.MAX_UINT_8=255]="MAX_UINT_8",e[e.MAX_UINT_16=65535]="MAX_UINT_16",e[e.MAX_UINT_32=4294967295]="MAX_UINT_32",e[e.UNICODE_SUPPLEMENTARY_PLANE_BEGIN=65536]="UNICODE_SUPPLEMENTARY_PLANE_BEGIN"})(n1||(n1={}));function r1(e){return e<0?0:e>255?255:e|0}function nt(e){return e<0?0:e>4294967295?4294967295:e|0}var Gl=class Ji{constructor(t){const n=r1(t);this.c=n,this.a=Ji.d(n),this.b=new Map}static d(t){const n=new Uint8Array(256);return n.fill(t),n}set(t,n){const r=r1(n);t>=0&&t<256?this.a[t]=r:this.b.set(t,r)}get(t){return t>=0&&t<256?this.a[t]:this.b.get(t)||this.c}clear(){this.a.fill(this.c),this.b.clear()}},s1;(function(e){e[e.False=0]="False",e[e.True=1]="True"})(s1||(s1={}));var i1;(function(e){e[e.Invalid=0]="Invalid",e[e.Start=1]="Start",e[e.H=2]="H",e[e.HT=3]="HT",e[e.HTT=4]="HTT",e[e.HTTP=5]="HTTP",e[e.F=6]="F",e[e.FI=7]="FI",e[e.FIL=8]="FIL",e[e.BeforeColon=9]="BeforeColon",e[e.AfterColon=10]="AfterColon",e[e.AlmostThere=11]="AlmostThere",e[e.End=12]="End",e[e.Accept=13]="Accept",e[e.LastKnownState=14]="LastKnownState"})(i1||(i1={}));var Xl=class{constructor(e,t,n){const r=new Uint8Array(e*t);for(let s=0,i=e*t;s<i;s++)r[s]=n;this.a=r,this.rows=e,this.cols=t}get(e,t){return this.a[e*this.cols+t]}set(e,t,n){this.a[e*this.cols+t]=n}},Jl=class{constructor(e){let t=0,n=0;for(let s=0,i=e.length;s<i;s++){const[a,u,o]=e[s];u>t&&(t=u),a>n&&(n=a),o>n&&(n=o)}t++,n++;const r=new Xl(n,t,0);for(let s=0,i=e.length;s<i;s++){const[a,u,o]=e[s];r.set(a,u,o)}this.a=r,this.b=t}nextState(e,t){return t<0||t>=this.b?0:this.a.get(e,t)}},Un=null;function Yl(){return Un===null&&(Un=new Jl([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),Un}var a1;(function(e){e[e.None=0]="None",e[e.ForceTermination=1]="ForceTermination",e[e.CannotEndIn=2]="CannotEndIn"})(a1||(a1={}));var Nt=null;function Zl(){if(Nt===null){Nt=new Gl(0);const e=` 	<>'"\u3001\u3002\uFF61\uFF64\uFF0C\uFF0E\uFF1A\uFF1B\u2018\u3008\u300C\u300E\u3014\uFF08\uFF3B\uFF5B\uFF62\uFF63\uFF5D\uFF3D\uFF09\u3015\u300F\u300D\u3009\u2019\uFF40\uFF5E\u2026|`;for(let n=0;n<e.length;n++)Nt.set(e.charCodeAt(n),1);const t=".,;:";for(let n=0;n<t.length;n++)Nt.set(t.charCodeAt(n),2)}return Nt}var Ql=class fr{static a(t,n,r,s,i){let a=i-1;do{const u=n.charCodeAt(a);if(t.get(u)!==2)break;a--}while(a>s);if(s>0){const u=n.charCodeAt(s-1),o=n.charCodeAt(a);(u===40&&o===41||u===91&&o===93||u===123&&o===125)&&a--}return{range:{startLineNumber:r,startColumn:s+1,endLineNumber:r,endColumn:a+2},url:n.substring(s,a+1)}}static computeLinks(t,n=Yl()){const r=Zl(),s=[];for(let i=1,a=t.getLineCount();i<=a;i++){const u=t.getLineContent(i),o=u.length;let c=0,f=0,h=0,g=1,m=!1,d=!1,p=!1,v=!1;for(;c<o;){let w=!1;const A=u.charCodeAt(c);if(g===13){let N;switch(A){case 40:m=!0,N=0;break;case 41:N=m?0:1;break;case 91:p=!0,d=!0,N=0;break;case 93:p=!1,N=d?0:1;break;case 123:v=!0,N=0;break;case 125:N=v?0:1;break;case 39:case 34:case 96:h===A?N=1:h===39||h===34||h===96?N=0:N=1;break;case 42:N=h===42?1:0;break;case 32:N=p?0:1;break;default:N=r.get(A)}N===1&&(s.push(fr.a(r,u,i,f,c)),w=!0)}else if(g===12){let N;A===91?(d=!0,N=0):N=r.get(A),N===1?w=!0:g=13}else g=n.nextState(g,A),g===0&&(w=!0);w&&(g=1,m=!1,d=!1,v=!1,f=c+1,h=A),c++}g===13&&s.push(fr.a(r,u,i,f,o))}return s}};function Kl(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:Ql.computeLinks(e)}var e0=class Yi{constructor(){this.c=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}static{this.INSTANCE=new Yi}navigateValueSet(t,n,r,s,i){if(t&&n){const a=this.a(n,i);if(a)return{range:t,value:a}}if(r&&s){const a=this.a(s,i);if(a)return{range:r,value:a}}return null}a(t,n){const r=this.b(t,n);return r!==null?r:this.d(t,n)}b(t,n){const r=Math.pow(10,t.length-(t.lastIndexOf(".")+1));let s=Number(t);const i=parseFloat(t);return!isNaN(s)&&!isNaN(i)&&s===i?s===0&&!n?null:(s=Math.floor(s*r),s+=n?r:-r,String(s/r)):null}d(t,n){return this.e(this.c,t,n)}e(t,n,r){let s=null;for(let i=0,a=t.length;s===null&&i<a;i++)s=this.f(t[i],n,r);return s}f(t,n,r){let s=t.indexOf(n);return s>=0?(s+=r?1:-1,s<0?s=t.length-1:s%=t.length,t[s]):null}},l1;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.F20=78]="F20",e[e.F21=79]="F21",e[e.F22=80]="F22",e[e.F23=81]="F23",e[e.F24=82]="F24",e[e.NumLock=83]="NumLock",e[e.ScrollLock=84]="ScrollLock",e[e.Semicolon=85]="Semicolon",e[e.Equal=86]="Equal",e[e.Comma=87]="Comma",e[e.Minus=88]="Minus",e[e.Period=89]="Period",e[e.Slash=90]="Slash",e[e.Backquote=91]="Backquote",e[e.BracketLeft=92]="BracketLeft",e[e.Backslash=93]="Backslash",e[e.BracketRight=94]="BracketRight",e[e.Quote=95]="Quote",e[e.OEM_8=96]="OEM_8",e[e.IntlBackslash=97]="IntlBackslash",e[e.Numpad0=98]="Numpad0",e[e.Numpad1=99]="Numpad1",e[e.Numpad2=100]="Numpad2",e[e.Numpad3=101]="Numpad3",e[e.Numpad4=102]="Numpad4",e[e.Numpad5=103]="Numpad5",e[e.Numpad6=104]="Numpad6",e[e.Numpad7=105]="Numpad7",e[e.Numpad8=106]="Numpad8",e[e.Numpad9=107]="Numpad9",e[e.NumpadMultiply=108]="NumpadMultiply",e[e.NumpadAdd=109]="NumpadAdd",e[e.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=111]="NumpadSubtract",e[e.NumpadDecimal=112]="NumpadDecimal",e[e.NumpadDivide=113]="NumpadDivide",e[e.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",e[e.ABNT_C1=115]="ABNT_C1",e[e.ABNT_C2=116]="ABNT_C2",e[e.AudioVolumeMute=117]="AudioVolumeMute",e[e.AudioVolumeUp=118]="AudioVolumeUp",e[e.AudioVolumeDown=119]="AudioVolumeDown",e[e.BrowserSearch=120]="BrowserSearch",e[e.BrowserHome=121]="BrowserHome",e[e.BrowserBack=122]="BrowserBack",e[e.BrowserForward=123]="BrowserForward",e[e.MediaTrackNext=124]="MediaTrackNext",e[e.MediaTrackPrevious=125]="MediaTrackPrevious",e[e.MediaStop=126]="MediaStop",e[e.MediaPlayPause=127]="MediaPlayPause",e[e.LaunchMediaPlayer=128]="LaunchMediaPlayer",e[e.LaunchMail=129]="LaunchMail",e[e.LaunchApp2=130]="LaunchApp2",e[e.Clear=131]="Clear",e[e.MAX_VALUE=132]="MAX_VALUE"})(l1||(l1={}));var u1;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.None=0]="None",e[e.Hyper=1]="Hyper",e[e.Super=2]="Super",e[e.Fn=3]="Fn",e[e.FnLock=4]="FnLock",e[e.Suspend=5]="Suspend",e[e.Resume=6]="Resume",e[e.Turbo=7]="Turbo",e[e.Sleep=8]="Sleep",e[e.WakeUp=9]="WakeUp",e[e.KeyA=10]="KeyA",e[e.KeyB=11]="KeyB",e[e.KeyC=12]="KeyC",e[e.KeyD=13]="KeyD",e[e.KeyE=14]="KeyE",e[e.KeyF=15]="KeyF",e[e.KeyG=16]="KeyG",e[e.KeyH=17]="KeyH",e[e.KeyI=18]="KeyI",e[e.KeyJ=19]="KeyJ",e[e.KeyK=20]="KeyK",e[e.KeyL=21]="KeyL",e[e.KeyM=22]="KeyM",e[e.KeyN=23]="KeyN",e[e.KeyO=24]="KeyO",e[e.KeyP=25]="KeyP",e[e.KeyQ=26]="KeyQ",e[e.KeyR=27]="KeyR",e[e.KeyS=28]="KeyS",e[e.KeyT=29]="KeyT",e[e.KeyU=30]="KeyU",e[e.KeyV=31]="KeyV",e[e.KeyW=32]="KeyW",e[e.KeyX=33]="KeyX",e[e.KeyY=34]="KeyY",e[e.KeyZ=35]="KeyZ",e[e.Digit1=36]="Digit1",e[e.Digit2=37]="Digit2",e[e.Digit3=38]="Digit3",e[e.Digit4=39]="Digit4",e[e.Digit5=40]="Digit5",e[e.Digit6=41]="Digit6",e[e.Digit7=42]="Digit7",e[e.Digit8=43]="Digit8",e[e.Digit9=44]="Digit9",e[e.Digit0=45]="Digit0",e[e.Enter=46]="Enter",e[e.Escape=47]="Escape",e[e.Backspace=48]="Backspace",e[e.Tab=49]="Tab",e[e.Space=50]="Space",e[e.Minus=51]="Minus",e[e.Equal=52]="Equal",e[e.BracketLeft=53]="BracketLeft",e[e.BracketRight=54]="BracketRight",e[e.Backslash=55]="Backslash",e[e.IntlHash=56]="IntlHash",e[e.Semicolon=57]="Semicolon",e[e.Quote=58]="Quote",e[e.Backquote=59]="Backquote",e[e.Comma=60]="Comma",e[e.Period=61]="Period",e[e.Slash=62]="Slash",e[e.CapsLock=63]="CapsLock",e[e.F1=64]="F1",e[e.F2=65]="F2",e[e.F3=66]="F3",e[e.F4=67]="F4",e[e.F5=68]="F5",e[e.F6=69]="F6",e[e.F7=70]="F7",e[e.F8=71]="F8",e[e.F9=72]="F9",e[e.F10=73]="F10",e[e.F11=74]="F11",e[e.F12=75]="F12",e[e.PrintScreen=76]="PrintScreen",e[e.ScrollLock=77]="ScrollLock",e[e.Pause=78]="Pause",e[e.Insert=79]="Insert",e[e.Home=80]="Home",e[e.PageUp=81]="PageUp",e[e.Delete=82]="Delete",e[e.End=83]="End",e[e.PageDown=84]="PageDown",e[e.ArrowRight=85]="ArrowRight",e[e.ArrowLeft=86]="ArrowLeft",e[e.ArrowDown=87]="ArrowDown",e[e.ArrowUp=88]="ArrowUp",e[e.NumLock=89]="NumLock",e[e.NumpadDivide=90]="NumpadDivide",e[e.NumpadMultiply=91]="NumpadMultiply",e[e.NumpadSubtract=92]="NumpadSubtract",e[e.NumpadAdd=93]="NumpadAdd",e[e.NumpadEnter=94]="NumpadEnter",e[e.Numpad1=95]="Numpad1",e[e.Numpad2=96]="Numpad2",e[e.Numpad3=97]="Numpad3",e[e.Numpad4=98]="Numpad4",e[e.Numpad5=99]="Numpad5",e[e.Numpad6=100]="Numpad6",e[e.Numpad7=101]="Numpad7",e[e.Numpad8=102]="Numpad8",e[e.Numpad9=103]="Numpad9",e[e.Numpad0=104]="Numpad0",e[e.NumpadDecimal=105]="NumpadDecimal",e[e.IntlBackslash=106]="IntlBackslash",e[e.ContextMenu=107]="ContextMenu",e[e.Power=108]="Power",e[e.NumpadEqual=109]="NumpadEqual",e[e.F13=110]="F13",e[e.F14=111]="F14",e[e.F15=112]="F15",e[e.F16=113]="F16",e[e.F17=114]="F17",e[e.F18=115]="F18",e[e.F19=116]="F19",e[e.F20=117]="F20",e[e.F21=118]="F21",e[e.F22=119]="F22",e[e.F23=120]="F23",e[e.F24=121]="F24",e[e.Open=122]="Open",e[e.Help=123]="Help",e[e.Select=124]="Select",e[e.Again=125]="Again",e[e.Undo=126]="Undo",e[e.Cut=127]="Cut",e[e.Copy=128]="Copy",e[e.Paste=129]="Paste",e[e.Find=130]="Find",e[e.AudioVolumeMute=131]="AudioVolumeMute",e[e.AudioVolumeUp=132]="AudioVolumeUp",e[e.AudioVolumeDown=133]="AudioVolumeDown",e[e.NumpadComma=134]="NumpadComma",e[e.IntlRo=135]="IntlRo",e[e.KanaMode=136]="KanaMode",e[e.IntlYen=137]="IntlYen",e[e.Convert=138]="Convert",e[e.NonConvert=139]="NonConvert",e[e.Lang1=140]="Lang1",e[e.Lang2=141]="Lang2",e[e.Lang3=142]="Lang3",e[e.Lang4=143]="Lang4",e[e.Lang5=144]="Lang5",e[e.Abort=145]="Abort",e[e.Props=146]="Props",e[e.NumpadParenLeft=147]="NumpadParenLeft",e[e.NumpadParenRight=148]="NumpadParenRight",e[e.NumpadBackspace=149]="NumpadBackspace",e[e.NumpadMemoryStore=150]="NumpadMemoryStore",e[e.NumpadMemoryRecall=151]="NumpadMemoryRecall",e[e.NumpadMemoryClear=152]="NumpadMemoryClear",e[e.NumpadMemoryAdd=153]="NumpadMemoryAdd",e[e.NumpadMemorySubtract=154]="NumpadMemorySubtract",e[e.NumpadClear=155]="NumpadClear",e[e.NumpadClearEntry=156]="NumpadClearEntry",e[e.ControlLeft=157]="ControlLeft",e[e.ShiftLeft=158]="ShiftLeft",e[e.AltLeft=159]="AltLeft",e[e.MetaLeft=160]="MetaLeft",e[e.ControlRight=161]="ControlRight",e[e.ShiftRight=162]="ShiftRight",e[e.AltRight=163]="AltRight",e[e.MetaRight=164]="MetaRight",e[e.BrightnessUp=165]="BrightnessUp",e[e.BrightnessDown=166]="BrightnessDown",e[e.MediaPlay=167]="MediaPlay",e[e.MediaRecord=168]="MediaRecord",e[e.MediaFastForward=169]="MediaFastForward",e[e.MediaRewind=170]="MediaRewind",e[e.MediaTrackNext=171]="MediaTrackNext",e[e.MediaTrackPrevious=172]="MediaTrackPrevious",e[e.MediaStop=173]="MediaStop",e[e.Eject=174]="Eject",e[e.MediaPlayPause=175]="MediaPlayPause",e[e.MediaSelect=176]="MediaSelect",e[e.LaunchMail=177]="LaunchMail",e[e.LaunchApp2=178]="LaunchApp2",e[e.LaunchApp1=179]="LaunchApp1",e[e.SelectTask=180]="SelectTask",e[e.LaunchScreenSaver=181]="LaunchScreenSaver",e[e.BrowserSearch=182]="BrowserSearch",e[e.BrowserHome=183]="BrowserHome",e[e.BrowserBack=184]="BrowserBack",e[e.BrowserForward=185]="BrowserForward",e[e.BrowserStop=186]="BrowserStop",e[e.BrowserRefresh=187]="BrowserRefresh",e[e.BrowserFavorites=188]="BrowserFavorites",e[e.ZoomToggle=189]="ZoomToggle",e[e.MailReply=190]="MailReply",e[e.MailForward=191]="MailForward",e[e.MailSend=192]="MailSend",e[e.MAX_VALUE=193]="MAX_VALUE"})(u1||(u1={}));var jn=class{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}},Xt=new jn,On=new jn,Wn=new jn,t0=new Array(230),n0={},r0=[],s0=Object.create(null),i0=Object.create(null),o1=[],Hn=[];for(let e=0;e<=193;e++)o1[e]=-1;for(let e=0;e<=132;e++)Hn[e]=-1;(function(){const e="",t=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],n=[],r=[];for(const s of t){const[i,a,u,o,c,f,h,g,m]=s;if(r[a]||(r[a]=!0,r0[a]=u,s0[u]=a,i0[u.toLowerCase()]=a,i&&(o1[a]=o,o!==0&&o!==3&&o!==5&&o!==4&&o!==6&&o!==57&&(Hn[o]=a))),!n[o]){if(n[o]=!0,!c)throw new Error(`String representation missing for key code ${o} around scan code ${u}`);Xt.define(o,c),On.define(o,g||c),Wn.define(o,m||g||c)}f&&(t0[f]=o),h&&(n0[h]=o)}Hn[3]=46})();var c1;(function(e){function t(u){return Xt.keyCodeToStr(u)}e.toString=t;function n(u){return Xt.strToKeyCode(u)}e.fromString=n;function r(u){return On.keyCodeToStr(u)}e.toUserSettingsUS=r;function s(u){return Wn.keyCodeToStr(u)}e.toUserSettingsGeneral=s;function i(u){return On.strToKeyCode(u)||Wn.strToKeyCode(u)}e.fromUserSettings=i;function a(u){if(u>=98&&u<=113)return null;switch(u){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return Xt.keyCodeToStr(u)}e.toElectronAccelerator=a})(c1||(c1={}));var h1;(function(e){e[e.CtrlCmd=2048]="CtrlCmd",e[e.Shift=1024]="Shift",e[e.Alt=512]="Alt",e[e.WinCtrl=256]="WinCtrl"})(h1||(h1={}));function a0(e,t){const n=(t&65535)<<16>>>0;return(e|n)>>>0}var Ue,zn=globalThis.vscode;if(typeof zn<"u"&&typeof zn.process<"u"){const e=zn.process;Ue={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?Ue={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:Ue={get platform(){return Ke?"win32":Ka?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var Jt=Ue.cwd,l0=Ue.env,u0=Ue.platform,fo=Ue.arch,o0=65,c0=97,h0=90,f0=122,je=46,ne=47,le=92,xe=58,g0=63,f1=class extends Error{constructor(e,t,n){let r;typeof t=="string"&&t.indexOf("not ")===0?(r="must not be",t=t.replace(/^not /,"")):r="must be";const s=e.indexOf(".")!==-1?"property":"argument";let i=`The "${e}" ${s} ${r} of type ${t}`;i+=`. Received type ${typeof n}`,super(i),this.code="ERR_INVALID_ARG_TYPE"}};function m0(e,t){if(e===null||typeof e!="object")throw new f1(t,"Object",e)}function Y(e,t){if(typeof e!="string")throw new f1(t,"string",e)}var ue=u0==="win32";function q(e){return e===ne||e===le}function Cn(e){return e===ne}function Ee(e){return e>=o0&&e<=h0||e>=c0&&e<=f0}function Yt(e,t,n,r){let s="",i=0,a=-1,u=0,o=0;for(let c=0;c<=e.length;++c){if(c<e.length)o=e.charCodeAt(c);else{if(r(o))break;o=ne}if(r(o)){if(!(a===c-1||u===1))if(u===2){if(s.length<2||i!==2||s.charCodeAt(s.length-1)!==je||s.charCodeAt(s.length-2)!==je){if(s.length>2){const f=s.lastIndexOf(n);f===-1?(s="",i=0):(s=s.slice(0,f),i=s.length-1-s.lastIndexOf(n)),a=c,u=0;continue}else if(s.length!==0){s="",i=0,a=c,u=0;continue}}t&&(s+=s.length>0?`${n}..`:"..",i=2)}else s.length>0?s+=`${n}${e.slice(a+1,c)}`:s=e.slice(a+1,c),i=c-a-1;a=c,u=0}else o===je&&u!==-1?++u:u=-1}return s}function d0(e){return e?`${e[0]==="."?"":"."}${e}`:""}function g1(e,t){m0(t,"pathObject");const n=t.dir||t.root,r=t.base||`${t.name||""}${d0(t.ext)}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}var Z={resolve(...e){let t="",n="",r=!1;for(let s=e.length-1;s>=-1;s--){let i;if(s>=0){if(i=e[s],Y(i,`paths[${s}]`),i.length===0)continue}else t.length===0?i=Jt():(i=l0[`=${t}`]||Jt(),(i===void 0||i.slice(0,2).toLowerCase()!==t.toLowerCase()&&i.charCodeAt(2)===le)&&(i=`${t}\\`));const a=i.length;let u=0,o="",c=!1;const f=i.charCodeAt(0);if(a===1)q(f)&&(u=1,c=!0);else if(q(f))if(c=!0,q(i.charCodeAt(1))){let h=2,g=h;for(;h<a&&!q(i.charCodeAt(h));)h++;if(h<a&&h!==g){const m=i.slice(g,h);for(g=h;h<a&&q(i.charCodeAt(h));)h++;if(h<a&&h!==g){for(g=h;h<a&&!q(i.charCodeAt(h));)h++;(h===a||h!==g)&&(o=`\\\\${m}\\${i.slice(g,h)}`,u=h)}}}else u=1;else Ee(f)&&i.charCodeAt(1)===xe&&(o=i.slice(0,2),u=2,a>2&&q(i.charCodeAt(2))&&(c=!0,u=3));if(o.length>0)if(t.length>0){if(o.toLowerCase()!==t.toLowerCase())continue}else t=o;if(r){if(t.length>0)break}else if(n=`${i.slice(u)}\\${n}`,r=c,c&&t.length>0)break}return n=Yt(n,!r,"\\",q),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){Y(e,"path");const t=e.length;if(t===0)return".";let n=0,r,s=!1;const i=e.charCodeAt(0);if(t===1)return Cn(i)?"\\":e;if(q(i))if(s=!0,q(e.charCodeAt(1))){let u=2,o=u;for(;u<t&&!q(e.charCodeAt(u));)u++;if(u<t&&u!==o){const c=e.slice(o,u);for(o=u;u<t&&q(e.charCodeAt(u));)u++;if(u<t&&u!==o){for(o=u;u<t&&!q(e.charCodeAt(u));)u++;if(u===t)return`\\\\${c}\\${e.slice(o)}\\`;u!==o&&(r=`\\\\${c}\\${e.slice(o,u)}`,n=u)}}}else n=1;else Ee(i)&&e.charCodeAt(1)===xe&&(r=e.slice(0,2),n=2,t>2&&q(e.charCodeAt(2))&&(s=!0,n=3));let a=n<t?Yt(e.slice(n),!s,"\\",q):"";if(a.length===0&&!s&&(a="."),a.length>0&&q(e.charCodeAt(t-1))&&(a+="\\"),!s&&r===void 0&&e.includes(":")){if(a.length>=2&&Ee(a.charCodeAt(0))&&a.charCodeAt(1)===xe)return`.\\${a}`;let u=e.indexOf(":");do if(u===t-1||q(e.charCodeAt(u+1)))return`.\\${a}`;while((u=e.indexOf(":",u+1))!==-1)}return r===void 0?s?`\\${a}`:a:s?`${r}\\${a}`:`${r}${a}`},isAbsolute(e){Y(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return q(n)||t>2&&Ee(n)&&e.charCodeAt(1)===xe&&q(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let i=0;i<e.length;++i){const a=e[i];Y(a,"path"),a.length>0&&(t===void 0?t=n=a:t+=`\\${a}`)}if(t===void 0)return".";let r=!0,s=0;if(typeof n=="string"&&q(n.charCodeAt(0))){++s;const i=n.length;i>1&&q(n.charCodeAt(1))&&(++s,i>2&&(q(n.charCodeAt(2))?++s:r=!1))}if(r){for(;s<t.length&&q(t.charCodeAt(s));)s++;s>=2&&(t=`\\${t.slice(s)}`)}return Z.normalize(t)},relative(e,t){if(Y(e,"from"),Y(t,"to"),e===t)return"";const n=Z.resolve(e),r=Z.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return"";if(n.length!==e.length||r.length!==t.length){const d=n.split("\\"),p=r.split("\\");d[d.length-1]===""&&d.pop(),p[p.length-1]===""&&p.pop();const v=d.length,w=p.length,A=v<w?v:w;let N;for(N=0;N<A&&d[N].toLowerCase()===p[N].toLowerCase();N++);return N===0?r:N===A?w>A?p.slice(N).join("\\"):v>A?"..\\".repeat(v-1-N)+"..":"":"..\\".repeat(v-N)+p.slice(N).join("\\")}let s=0;for(;s<e.length&&e.charCodeAt(s)===le;)s++;let i=e.length;for(;i-1>s&&e.charCodeAt(i-1)===le;)i--;const a=i-s;let u=0;for(;u<t.length&&t.charCodeAt(u)===le;)u++;let o=t.length;for(;o-1>u&&t.charCodeAt(o-1)===le;)o--;const c=o-u,f=a<c?a:c;let h=-1,g=0;for(;g<f;g++){const d=e.charCodeAt(s+g);if(d!==t.charCodeAt(u+g))break;d===le&&(h=g)}if(g!==f){if(h===-1)return r}else{if(c>f){if(t.charCodeAt(u+g)===le)return r.slice(u+g+1);if(g===2)return r.slice(u+g)}a>f&&(e.charCodeAt(s+g)===le?h=g:g===2&&(h=3)),h===-1&&(h=0)}let m="";for(g=s+h+1;g<=i;++g)(g===i||e.charCodeAt(g)===le)&&(m+=m.length===0?"..":"\\..");return u+=h,m.length>0?`${m}${r.slice(u,o)}`:(r.charCodeAt(u)===le&&++u,r.slice(u,o))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=Z.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===le){if(t.charCodeAt(1)===le){const n=t.charCodeAt(2);if(n!==g0&&n!==je)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(Ee(t.charCodeAt(0))&&t.charCodeAt(1)===xe&&t.charCodeAt(2)===le)return`\\\\?\\${t}`;return t},dirname(e){Y(e,"path");const t=e.length;if(t===0)return".";let n=-1,r=0;const s=e.charCodeAt(0);if(t===1)return q(s)?e:".";if(q(s)){if(n=r=1,q(e.charCodeAt(1))){let u=2,o=u;for(;u<t&&!q(e.charCodeAt(u));)u++;if(u<t&&u!==o){for(o=u;u<t&&q(e.charCodeAt(u));)u++;if(u<t&&u!==o){for(o=u;u<t&&!q(e.charCodeAt(u));)u++;if(u===t)return e;u!==o&&(n=r=u+1)}}}}else Ee(s)&&e.charCodeAt(1)===xe&&(n=t>2&&q(e.charCodeAt(2))?3:2,r=n);let i=-1,a=!0;for(let u=t-1;u>=r;--u)if(q(e.charCodeAt(u))){if(!a){i=u;break}}else a=!1;if(i===-1){if(n===-1)return".";i=n}return e.slice(0,i)},basename(e,t){t!==void 0&&Y(t,"suffix"),Y(e,"path");let n=0,r=-1,s=!0,i;if(e.length>=2&&Ee(e.charCodeAt(0))&&e.charCodeAt(1)===xe&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,u=-1;for(i=e.length-1;i>=n;--i){const o=e.charCodeAt(i);if(q(o)){if(!s){n=i+1;break}}else u===-1&&(s=!1,u=i+1),a>=0&&(o===t.charCodeAt(a)?--a===-1&&(r=i):(a=-1,r=u))}return n===r?r=u:r===-1&&(r=e.length),e.slice(n,r)}for(i=e.length-1;i>=n;--i)if(q(e.charCodeAt(i))){if(!s){n=i+1;break}}else r===-1&&(s=!1,r=i+1);return r===-1?"":e.slice(n,r)},extname(e){Y(e,"path");let t=0,n=-1,r=0,s=-1,i=!0,a=0;e.length>=2&&e.charCodeAt(1)===xe&&Ee(e.charCodeAt(0))&&(t=r=2);for(let u=e.length-1;u>=t;--u){const o=e.charCodeAt(u);if(q(o)){if(!i){r=u+1;break}continue}s===-1&&(i=!1,s=u+1),o===je?n===-1?n=u:a!==1&&(a=1):n!==-1&&(a=-1)}return n===-1||s===-1||a===0||a===1&&n===s-1&&n===r+1?"":e.slice(n,s)},format:g1.bind(null,"\\"),parse(e){Y(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let r=0,s=e.charCodeAt(0);if(n===1)return q(s)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(q(s)){if(r=1,q(e.charCodeAt(1))){let h=2,g=h;for(;h<n&&!q(e.charCodeAt(h));)h++;if(h<n&&h!==g){for(g=h;h<n&&q(e.charCodeAt(h));)h++;if(h<n&&h!==g){for(g=h;h<n&&!q(e.charCodeAt(h));)h++;h===n?r=h:h!==g&&(r=h+1)}}}}else if(Ee(s)&&e.charCodeAt(1)===xe){if(n<=2)return t.root=t.dir=e,t;if(r=2,q(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let i=-1,a=r,u=-1,o=!0,c=e.length-1,f=0;for(;c>=r;--c){if(s=e.charCodeAt(c),q(s)){if(!o){a=c+1;break}continue}u===-1&&(o=!1,u=c+1),s===je?i===-1?i=c:f!==1&&(f=1):i!==-1&&(f=-1)}return u!==-1&&(i===-1||f===0||f===1&&i===u-1&&i===a+1?t.base=t.name=e.slice(a,u):(t.name=e.slice(a,i),t.base=e.slice(a,u),t.ext=e.slice(i,u))),a>0&&a!==r?t.dir=e.slice(0,a-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},b0=(()=>{if(ue){const e=/\\/g;return()=>{const t=Jt().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>Jt()})(),C={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=0&&!n;r--){const s=e[r];Y(s,`paths[${r}]`),s.length!==0&&(t=`${s}/${t}`,n=s.charCodeAt(0)===ne)}if(!n){const r=b0();t=`${r}/${t}`,n=r.charCodeAt(0)===ne}return t=Yt(t,!n,"/",Cn),n?`/${t}`:t.length>0?t:"."},normalize(e){if(Y(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===ne,n=e.charCodeAt(e.length-1)===ne;return e=Yt(e,!t,"/",Cn),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return Y(e,"path"),e.length>0&&e.charCodeAt(0)===ne},join(...e){if(e.length===0)return".";const t=[];for(let n=0;n<e.length;++n){const r=e[n];Y(r,"path"),r.length>0&&t.push(r)}return t.length===0?".":C.normalize(t.join("/"))},relative(e,t){if(Y(e,"from"),Y(t,"to"),e===t||(e=C.resolve(e),t=C.resolve(t),e===t))return"";const n=1,r=e.length,s=r-n,i=1,a=t.length-i,u=s<a?s:a;let o=-1,c=0;for(;c<u;c++){const h=e.charCodeAt(n+c);if(h!==t.charCodeAt(i+c))break;h===ne&&(o=c)}if(c===u)if(a>u){if(t.charCodeAt(i+c)===ne)return t.slice(i+c+1);if(c===0)return t.slice(i+c)}else s>u&&(e.charCodeAt(n+c)===ne?o=c:c===0&&(o=0));let f="";for(c=n+o+1;c<=r;++c)(c===r||e.charCodeAt(c)===ne)&&(f+=f.length===0?"..":"/..");return`${f}${t.slice(i+o)}`},toNamespacedPath(e){return e},dirname(e){if(Y(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===ne;let n=-1,r=!0;for(let s=e.length-1;s>=1;--s)if(e.charCodeAt(s)===ne){if(!r){n=s;break}}else r=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&Y(t,"suffix"),Y(e,"path");let n=0,r=-1,s=!0,i;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,u=-1;for(i=e.length-1;i>=0;--i){const o=e.charCodeAt(i);if(o===ne){if(!s){n=i+1;break}}else u===-1&&(s=!1,u=i+1),a>=0&&(o===t.charCodeAt(a)?--a===-1&&(r=i):(a=-1,r=u))}return n===r?r=u:r===-1&&(r=e.length),e.slice(n,r)}for(i=e.length-1;i>=0;--i)if(e.charCodeAt(i)===ne){if(!s){n=i+1;break}}else r===-1&&(s=!1,r=i+1);return r===-1?"":e.slice(n,r)},extname(e){Y(e,"path");let t=-1,n=0,r=-1,s=!0,i=0;for(let a=e.length-1;a>=0;--a){const u=e[a];if(u==="/"){if(!s){n=a+1;break}continue}r===-1&&(s=!1,r=a+1),u==="."?t===-1?t=a:i!==1&&(i=1):t!==-1&&(i=-1)}return t===-1||r===-1||i===0||i===1&&t===r-1&&t===n+1?"":e.slice(t,r)},format:g1.bind(null,"/"),parse(e){Y(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===ne;let r;n?(t.root="/",r=1):r=0;let s=-1,i=0,a=-1,u=!0,o=e.length-1,c=0;for(;o>=r;--o){const f=e.charCodeAt(o);if(f===ne){if(!u){i=o+1;break}continue}a===-1&&(u=!1,a=o+1),f===je?s===-1?s=o:c!==1&&(c=1):s!==-1&&(c=-1)}if(a!==-1){const f=i===0&&n?1:i;s===-1||c===0||c===1&&s===a-1&&s===i+1?t.base=t.name=e.slice(f,a):(t.name=e.slice(f,s),t.base=e.slice(f,a),t.ext=e.slice(s,a))}return i>0?t.dir=e.slice(0,i-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};C.win32=Z.win32=Z,C.posix=Z.posix=C;var w0=ue?Z.normalize:C.normalize,go=ue?Z.isAbsolute:C.isAbsolute,v0=ue?Z.join:C.join,p0=ue?Z.resolve:C.resolve,L0=ue?Z.relative:C.relative,N0=ue?Z.dirname:C.dirname,mo=ue?Z.basename:C.basename,bo=ue?Z.extname:C.extname,wo=ue?Z.format:C.format,vo=ue?Z.parse:C.parse,po=ue?Z.toNamespacedPath:C.toNamespacedPath,Zt=ue?Z.sep:C.sep,Lo=ue?Z.delimiter:C.delimiter,R0=/^\w[\w\d+.-]*$/,A0=/^\//,x0=/^\/\//;function E0(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!R0.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!A0.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(x0.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function M0(e,t){return!e&&!t?"file":e}function y0(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==ve&&(t=ve+t):t=ve;break}return t}var X="",ve="/",k0=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,oe=class gn{static isUri(t){return t instanceof gn?!0:!t||typeof t!="object"?!1:typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function"}constructor(t,n,r,s,i,a=!1){typeof t=="object"?(this.scheme=t.scheme||X,this.authority=t.authority||X,this.path=t.path||X,this.query=t.query||X,this.fragment=t.fragment||X):(this.scheme=M0(t,a),this.authority=n||X,this.path=y0(this.scheme,r||X),this.query=s||X,this.fragment=i||X,E0(this,a))}get fsPath(){return Qt(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:r,path:s,query:i,fragment:a}=t;return n===void 0?n=this.scheme:n===null&&(n=X),r===void 0?r=this.authority:r===null&&(r=X),s===void 0?s=this.path:s===null&&(s=X),i===void 0?i=this.query:i===null&&(i=X),a===void 0?a=this.fragment:a===null&&(a=X),n===this.scheme&&r===this.authority&&s===this.path&&i===this.query&&a===this.fragment?this:new rt(n,r,s,i,a)}static parse(t,n=!1){const r=k0.exec(t);return r?new rt(r[2]||X,Kt(r[4]||X),Kt(r[5]||X),Kt(r[7]||X),Kt(r[9]||X),n):new rt(X,X,X,X,X)}static file(t){let n=X;if(Ke&&(t=t.replace(/\\/g,ve)),t[0]===ve&&t[1]===ve){const r=t.indexOf(ve,2);r===-1?(n=t.substring(2),t=ve):(n=t.substring(2,r),t=t.substring(r)||ve)}return new rt("file",n,t,X,X)}static from(t,n){return new rt(t.scheme,t.authority,t.path,t.query,t.fragment,n)}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return Ke&&t.scheme==="file"?r=gn.file(Z.join(Qt(t,!0),...n)).path:r=C.join(t.path,...n),t.with({path:r})}toString(t=!1){return Gn(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof gn)return t;{const n=new rt(t);return n._formatted=t.external??null,n._fsPath=t._sep===m1?t.fsPath??null:null,n}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},m1=Ke?1:void 0,rt=class extends oe{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=Qt(this,!1)),this._fsPath}toString(e=!1){return e?Gn(this,!0):(this._formatted||(this._formatted=Gn(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=m1),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},d1={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function b1(e,t,n){let r,s=-1;for(let i=0;i<e.length;i++){const a=e.charCodeAt(i);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===45||a===46||a===95||a===126||t&&a===47||n&&a===91||n&&a===93||n&&a===58)s!==-1&&(r+=encodeURIComponent(e.substring(s,i)),s=-1),r!==void 0&&(r+=e.charAt(i));else{r===void 0&&(r=e.substr(0,i));const u=d1[a];u!==void 0?(s!==-1&&(r+=encodeURIComponent(e.substring(s,i)),s=-1),r+=u):s===-1&&(s=i)}}return s!==-1&&(r+=encodeURIComponent(e.substring(s))),r!==void 0?r:e}function F0(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=d1[r]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function Qt(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,Ke&&(n=n.replace(/\//g,"\\")),n}function Gn(e,t){const n=t?F0:b1;let r="",{scheme:s,authority:i,path:a,query:u,fragment:o}=e;if(s&&(r+=s,r+=":"),(i||s==="file")&&(r+=ve,r+=ve),i){let c=i.indexOf("@");if(c!==-1){const f=i.substr(0,c);i=i.substr(c+1),c=f.lastIndexOf(":"),c===-1?r+=n(f,!1,!1):(r+=n(f.substr(0,c),!1,!1),r+=":",r+=n(f.substr(c+1),!1,!0)),r+="@"}i=i.toLowerCase(),c=i.lastIndexOf(":"),c===-1?r+=n(i,!1,!0):(r+=n(i.substr(0,c),!1,!0),r+=i.substr(c))}if(a){if(a.length>=3&&a.charCodeAt(0)===47&&a.charCodeAt(2)===58){const c=a.charCodeAt(1);c>=65&&c<=90&&(a=`/${String.fromCharCode(c+32)}:${a.substr(3)}`)}else if(a.length>=2&&a.charCodeAt(1)===58){const c=a.charCodeAt(0);c>=65&&c<=90&&(a=`${String.fromCharCode(c+32)}:${a.substr(2)}`)}r+=n(a,!0,!1)}return u&&(r+="?",r+=n(u,!1,!1)),o&&(r+="#",r+=t?o:b1(o,!1,!1)),r}function w1(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+w1(e.substr(3)):e}}var v1=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function Kt(e){return e.match(v1)?e.replace(v1,t=>w1(t)):e}var p1;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(p1||(p1={}));var P0=class be extends _{constructor(t,n,r,s){super(t,n,r,s),this.selectionStartLineNumber=t,this.selectionStartColumn=n,this.positionLineNumber=r,this.positionColumn=s}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(t){return be.selectionsEqual(this,t)}static selectionsEqual(t,n){return t.selectionStartLineNumber===n.selectionStartLineNumber&&t.selectionStartColumn===n.selectionStartColumn&&t.positionLineNumber===n.positionLineNumber&&t.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new be(this.startLineNumber,this.startColumn,t,n):new be(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new O(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new O(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new be(t,n,this.endLineNumber,this.endColumn):new be(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new be(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new be(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new be(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new be(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(t,n){if(t&&!n||!t&&n)return!1;if(!t&&!n)return!0;if(t.length!==n.length)return!1;for(let r=0,s=t.length;r<s;r++)if(!this.selectionsEqual(t[r],n[r]))return!1;return!0}static isISelection(t){return t&&typeof t.selectionStartLineNumber=="number"&&typeof t.selectionStartColumn=="number"&&typeof t.positionLineNumber=="number"&&typeof t.positionColumn=="number"}static createWithDirection(t,n,r,s,i){return i===0?new be(t,n,r,s):new be(r,s,t,n)}},L1=Object.create(null);function l(e,t){if(Ma(t)){const n=L1[t];if(n===void 0)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return L1[e]=t,{id:e}}var T0={add:l("add",6e4),plus:l("plus",6e4),gistNew:l("gist-new",6e4),repoCreate:l("repo-create",6e4),lightbulb:l("lightbulb",60001),lightBulb:l("light-bulb",60001),repo:l("repo",60002),repoDelete:l("repo-delete",60002),gistFork:l("gist-fork",60003),repoForked:l("repo-forked",60003),gitPullRequest:l("git-pull-request",60004),gitPullRequestAbandoned:l("git-pull-request-abandoned",60004),recordKeys:l("record-keys",60005),keyboard:l("keyboard",60005),tag:l("tag",60006),gitPullRequestLabel:l("git-pull-request-label",60006),tagAdd:l("tag-add",60006),tagRemove:l("tag-remove",60006),person:l("person",60007),personFollow:l("person-follow",60007),personOutline:l("person-outline",60007),personFilled:l("person-filled",60007),gitBranch:l("git-branch",60008),gitBranchCreate:l("git-branch-create",60008),gitBranchDelete:l("git-branch-delete",60008),sourceControl:l("source-control",60008),mirror:l("mirror",60009),mirrorPublic:l("mirror-public",60009),star:l("star",60010),starAdd:l("star-add",60010),starDelete:l("star-delete",60010),starEmpty:l("star-empty",60010),comment:l("comment",60011),commentAdd:l("comment-add",60011),alert:l("alert",60012),warning:l("warning",60012),search:l("search",60013),searchSave:l("search-save",60013),logOut:l("log-out",60014),signOut:l("sign-out",60014),logIn:l("log-in",60015),signIn:l("sign-in",60015),eye:l("eye",60016),eyeUnwatch:l("eye-unwatch",60016),eyeWatch:l("eye-watch",60016),circleFilled:l("circle-filled",60017),primitiveDot:l("primitive-dot",60017),closeDirty:l("close-dirty",60017),debugBreakpoint:l("debug-breakpoint",60017),debugBreakpointDisabled:l("debug-breakpoint-disabled",60017),debugHint:l("debug-hint",60017),terminalDecorationSuccess:l("terminal-decoration-success",60017),primitiveSquare:l("primitive-square",60018),edit:l("edit",60019),pencil:l("pencil",60019),info:l("info",60020),issueOpened:l("issue-opened",60020),gistPrivate:l("gist-private",60021),gitForkPrivate:l("git-fork-private",60021),lock:l("lock",60021),mirrorPrivate:l("mirror-private",60021),close:l("close",60022),removeClose:l("remove-close",60022),x:l("x",60022),repoSync:l("repo-sync",60023),sync:l("sync",60023),clone:l("clone",60024),desktopDownload:l("desktop-download",60024),beaker:l("beaker",60025),microscope:l("microscope",60025),vm:l("vm",60026),deviceDesktop:l("device-desktop",60026),file:l("file",60027),fileText:l("file-text",60027),more:l("more",60028),ellipsis:l("ellipsis",60028),kebabHorizontal:l("kebab-horizontal",60028),mailReply:l("mail-reply",60029),reply:l("reply",60029),organization:l("organization",60030),organizationFilled:l("organization-filled",60030),organizationOutline:l("organization-outline",60030),newFile:l("new-file",60031),fileAdd:l("file-add",60031),newFolder:l("new-folder",60032),fileDirectoryCreate:l("file-directory-create",60032),trash:l("trash",60033),trashcan:l("trashcan",60033),history:l("history",60034),clock:l("clock",60034),folder:l("folder",60035),fileDirectory:l("file-directory",60035),symbolFolder:l("symbol-folder",60035),logoGithub:l("logo-github",60036),markGithub:l("mark-github",60036),github:l("github",60036),terminal:l("terminal",60037),console:l("console",60037),repl:l("repl",60037),zap:l("zap",60038),symbolEvent:l("symbol-event",60038),error:l("error",60039),stop:l("stop",60039),variable:l("variable",60040),symbolVariable:l("symbol-variable",60040),array:l("array",60042),symbolArray:l("symbol-array",60042),symbolModule:l("symbol-module",60043),symbolPackage:l("symbol-package",60043),symbolNamespace:l("symbol-namespace",60043),symbolObject:l("symbol-object",60043),symbolMethod:l("symbol-method",60044),symbolFunction:l("symbol-function",60044),symbolConstructor:l("symbol-constructor",60044),symbolBoolean:l("symbol-boolean",60047),symbolNull:l("symbol-null",60047),symbolNumeric:l("symbol-numeric",60048),symbolNumber:l("symbol-number",60048),symbolStructure:l("symbol-structure",60049),symbolStruct:l("symbol-struct",60049),symbolParameter:l("symbol-parameter",60050),symbolTypeParameter:l("symbol-type-parameter",60050),symbolKey:l("symbol-key",60051),symbolText:l("symbol-text",60051),symbolReference:l("symbol-reference",60052),goToFile:l("go-to-file",60052),symbolEnum:l("symbol-enum",60053),symbolValue:l("symbol-value",60053),symbolRuler:l("symbol-ruler",60054),symbolUnit:l("symbol-unit",60054),activateBreakpoints:l("activate-breakpoints",60055),archive:l("archive",60056),arrowBoth:l("arrow-both",60057),arrowDown:l("arrow-down",60058),arrowLeft:l("arrow-left",60059),arrowRight:l("arrow-right",60060),arrowSmallDown:l("arrow-small-down",60061),arrowSmallLeft:l("arrow-small-left",60062),arrowSmallRight:l("arrow-small-right",60063),arrowSmallUp:l("arrow-small-up",60064),arrowUp:l("arrow-up",60065),bell:l("bell",60066),bold:l("bold",60067),book:l("book",60068),bookmark:l("bookmark",60069),debugBreakpointConditionalUnverified:l("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:l("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:l("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:l("debug-breakpoint-data-unverified",60072),debugBreakpointData:l("debug-breakpoint-data",60073),debugBreakpointDataDisabled:l("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:l("debug-breakpoint-log-unverified",60074),debugBreakpointLog:l("debug-breakpoint-log",60075),debugBreakpointLogDisabled:l("debug-breakpoint-log-disabled",60075),briefcase:l("briefcase",60076),broadcast:l("broadcast",60077),browser:l("browser",60078),bug:l("bug",60079),calendar:l("calendar",60080),caseSensitive:l("case-sensitive",60081),check:l("check",60082),checklist:l("checklist",60083),chevronDown:l("chevron-down",60084),chevronLeft:l("chevron-left",60085),chevronRight:l("chevron-right",60086),chevronUp:l("chevron-up",60087),chromeClose:l("chrome-close",60088),chromeMaximize:l("chrome-maximize",60089),chromeMinimize:l("chrome-minimize",60090),chromeRestore:l("chrome-restore",60091),circleOutline:l("circle-outline",60092),circle:l("circle",60092),debugBreakpointUnverified:l("debug-breakpoint-unverified",60092),terminalDecorationIncomplete:l("terminal-decoration-incomplete",60092),circleSlash:l("circle-slash",60093),circuitBoard:l("circuit-board",60094),clearAll:l("clear-all",60095),clippy:l("clippy",60096),closeAll:l("close-all",60097),cloudDownload:l("cloud-download",60098),cloudUpload:l("cloud-upload",60099),code:l("code",60100),collapseAll:l("collapse-all",60101),colorMode:l("color-mode",60102),commentDiscussion:l("comment-discussion",60103),creditCard:l("credit-card",60105),dash:l("dash",60108),dashboard:l("dashboard",60109),database:l("database",60110),debugContinue:l("debug-continue",60111),debugDisconnect:l("debug-disconnect",60112),debugPause:l("debug-pause",60113),debugRestart:l("debug-restart",60114),debugStart:l("debug-start",60115),debugStepInto:l("debug-step-into",60116),debugStepOut:l("debug-step-out",60117),debugStepOver:l("debug-step-over",60118),debugStop:l("debug-stop",60119),debug:l("debug",60120),deviceCameraVideo:l("device-camera-video",60121),deviceCamera:l("device-camera",60122),deviceMobile:l("device-mobile",60123),diffAdded:l("diff-added",60124),diffIgnored:l("diff-ignored",60125),diffModified:l("diff-modified",60126),diffRemoved:l("diff-removed",60127),diffRenamed:l("diff-renamed",60128),diff:l("diff",60129),diffSidebyside:l("diff-sidebyside",60129),discard:l("discard",60130),editorLayout:l("editor-layout",60131),emptyWindow:l("empty-window",60132),exclude:l("exclude",60133),extensions:l("extensions",60134),eyeClosed:l("eye-closed",60135),fileBinary:l("file-binary",60136),fileCode:l("file-code",60137),fileMedia:l("file-media",60138),filePdf:l("file-pdf",60139),fileSubmodule:l("file-submodule",60140),fileSymlinkDirectory:l("file-symlink-directory",60141),fileSymlinkFile:l("file-symlink-file",60142),fileZip:l("file-zip",60143),files:l("files",60144),filter:l("filter",60145),flame:l("flame",60146),foldDown:l("fold-down",60147),foldUp:l("fold-up",60148),fold:l("fold",60149),folderActive:l("folder-active",60150),folderOpened:l("folder-opened",60151),gear:l("gear",60152),gift:l("gift",60153),gistSecret:l("gist-secret",60154),gist:l("gist",60155),gitCommit:l("git-commit",60156),gitCompare:l("git-compare",60157),compareChanges:l("compare-changes",60157),gitMerge:l("git-merge",60158),githubAction:l("github-action",60159),githubAlt:l("github-alt",60160),globe:l("globe",60161),grabber:l("grabber",60162),graph:l("graph",60163),gripper:l("gripper",60164),heart:l("heart",60165),home:l("home",60166),horizontalRule:l("horizontal-rule",60167),hubot:l("hubot",60168),inbox:l("inbox",60169),issueReopened:l("issue-reopened",60171),issues:l("issues",60172),italic:l("italic",60173),jersey:l("jersey",60174),json:l("json",60175),kebabVertical:l("kebab-vertical",60176),key:l("key",60177),law:l("law",60178),lightbulbAutofix:l("lightbulb-autofix",60179),linkExternal:l("link-external",60180),link:l("link",60181),listOrdered:l("list-ordered",60182),listUnordered:l("list-unordered",60183),liveShare:l("live-share",60184),loading:l("loading",60185),location:l("location",60186),mailRead:l("mail-read",60187),mail:l("mail",60188),markdown:l("markdown",60189),megaphone:l("megaphone",60190),mention:l("mention",60191),milestone:l("milestone",60192),gitPullRequestMilestone:l("git-pull-request-milestone",60192),mortarBoard:l("mortar-board",60193),move:l("move",60194),multipleWindows:l("multiple-windows",60195),mute:l("mute",60196),noNewline:l("no-newline",60197),note:l("note",60198),octoface:l("octoface",60199),openPreview:l("open-preview",60200),package:l("package",60201),paintcan:l("paintcan",60202),pin:l("pin",60203),play:l("play",60204),run:l("run",60204),plug:l("plug",60205),preserveCase:l("preserve-case",60206),preview:l("preview",60207),project:l("project",60208),pulse:l("pulse",60209),question:l("question",60210),quote:l("quote",60211),radioTower:l("radio-tower",60212),reactions:l("reactions",60213),references:l("references",60214),refresh:l("refresh",60215),regex:l("regex",60216),remoteExplorer:l("remote-explorer",60217),remote:l("remote",60218),remove:l("remove",60219),replaceAll:l("replace-all",60220),replace:l("replace",60221),repoClone:l("repo-clone",60222),repoForcePush:l("repo-force-push",60223),repoPull:l("repo-pull",60224),repoPush:l("repo-push",60225),report:l("report",60226),requestChanges:l("request-changes",60227),rocket:l("rocket",60228),rootFolderOpened:l("root-folder-opened",60229),rootFolder:l("root-folder",60230),rss:l("rss",60231),ruby:l("ruby",60232),saveAll:l("save-all",60233),saveAs:l("save-as",60234),save:l("save",60235),screenFull:l("screen-full",60236),screenNormal:l("screen-normal",60237),searchStop:l("search-stop",60238),server:l("server",60240),settingsGear:l("settings-gear",60241),settings:l("settings",60242),shield:l("shield",60243),smiley:l("smiley",60244),sortPrecedence:l("sort-precedence",60245),splitHorizontal:l("split-horizontal",60246),splitVertical:l("split-vertical",60247),squirrel:l("squirrel",60248),starFull:l("star-full",60249),starHalf:l("star-half",60250),symbolClass:l("symbol-class",60251),symbolColor:l("symbol-color",60252),symbolConstant:l("symbol-constant",60253),symbolEnumMember:l("symbol-enum-member",60254),symbolField:l("symbol-field",60255),symbolFile:l("symbol-file",60256),symbolInterface:l("symbol-interface",60257),symbolKeyword:l("symbol-keyword",60258),symbolMisc:l("symbol-misc",60259),symbolOperator:l("symbol-operator",60260),symbolProperty:l("symbol-property",60261),wrench:l("wrench",60261),wrenchSubaction:l("wrench-subaction",60261),symbolSnippet:l("symbol-snippet",60262),tasklist:l("tasklist",60263),telescope:l("telescope",60264),textSize:l("text-size",60265),threeBars:l("three-bars",60266),thumbsdown:l("thumbsdown",60267),thumbsup:l("thumbsup",60268),tools:l("tools",60269),triangleDown:l("triangle-down",60270),triangleLeft:l("triangle-left",60271),triangleRight:l("triangle-right",60272),triangleUp:l("triangle-up",60273),twitter:l("twitter",60274),unfold:l("unfold",60275),unlock:l("unlock",60276),unmute:l("unmute",60277),unverified:l("unverified",60278),verified:l("verified",60279),versions:l("versions",60280),vmActive:l("vm-active",60281),vmOutline:l("vm-outline",60282),vmRunning:l("vm-running",60283),watch:l("watch",60284),whitespace:l("whitespace",60285),wholeWord:l("whole-word",60286),window:l("window",60287),wordWrap:l("word-wrap",60288),zoomIn:l("zoom-in",60289),zoomOut:l("zoom-out",60290),listFilter:l("list-filter",60291),listFlat:l("list-flat",60292),listSelection:l("list-selection",60293),selection:l("selection",60293),listTree:l("list-tree",60294),debugBreakpointFunctionUnverified:l("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:l("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:l("debug-breakpoint-function-disabled",60296),debugStackframeActive:l("debug-stackframe-active",60297),circleSmallFilled:l("circle-small-filled",60298),debugStackframeDot:l("debug-stackframe-dot",60298),terminalDecorationMark:l("terminal-decoration-mark",60298),debugStackframe:l("debug-stackframe",60299),debugStackframeFocused:l("debug-stackframe-focused",60299),debugBreakpointUnsupported:l("debug-breakpoint-unsupported",60300),symbolString:l("symbol-string",60301),debugReverseContinue:l("debug-reverse-continue",60302),debugStepBack:l("debug-step-back",60303),debugRestartFrame:l("debug-restart-frame",60304),debugAlt:l("debug-alt",60305),callIncoming:l("call-incoming",60306),callOutgoing:l("call-outgoing",60307),menu:l("menu",60308),expandAll:l("expand-all",60309),feedback:l("feedback",60310),gitPullRequestReviewer:l("git-pull-request-reviewer",60310),groupByRefType:l("group-by-ref-type",60311),ungroupByRefType:l("ungroup-by-ref-type",60312),account:l("account",60313),gitPullRequestAssignee:l("git-pull-request-assignee",60313),bellDot:l("bell-dot",60314),debugConsole:l("debug-console",60315),library:l("library",60316),output:l("output",60317),runAll:l("run-all",60318),syncIgnored:l("sync-ignored",60319),pinned:l("pinned",60320),githubInverted:l("github-inverted",60321),serverProcess:l("server-process",60322),serverEnvironment:l("server-environment",60323),pass:l("pass",60324),issueClosed:l("issue-closed",60324),stopCircle:l("stop-circle",60325),playCircle:l("play-circle",60326),record:l("record",60327),debugAltSmall:l("debug-alt-small",60328),vmConnect:l("vm-connect",60329),cloud:l("cloud",60330),merge:l("merge",60331),export:l("export",60332),graphLeft:l("graph-left",60333),magnet:l("magnet",60334),notebook:l("notebook",60335),redo:l("redo",60336),checkAll:l("check-all",60337),pinnedDirty:l("pinned-dirty",60338),passFilled:l("pass-filled",60339),circleLargeFilled:l("circle-large-filled",60340),circleLarge:l("circle-large",60341),circleLargeOutline:l("circle-large-outline",60341),combine:l("combine",60342),gather:l("gather",60342),table:l("table",60343),variableGroup:l("variable-group",60344),typeHierarchy:l("type-hierarchy",60345),typeHierarchySub:l("type-hierarchy-sub",60346),typeHierarchySuper:l("type-hierarchy-super",60347),gitPullRequestCreate:l("git-pull-request-create",60348),runAbove:l("run-above",60349),runBelow:l("run-below",60350),notebookTemplate:l("notebook-template",60351),debugRerun:l("debug-rerun",60352),workspaceTrusted:l("workspace-trusted",60353),workspaceUntrusted:l("workspace-untrusted",60354),workspaceUnknown:l("workspace-unknown",60355),terminalCmd:l("terminal-cmd",60356),terminalDebian:l("terminal-debian",60357),terminalLinux:l("terminal-linux",60358),terminalPowershell:l("terminal-powershell",60359),terminalTmux:l("terminal-tmux",60360),terminalUbuntu:l("terminal-ubuntu",60361),terminalBash:l("terminal-bash",60362),arrowSwap:l("arrow-swap",60363),copy:l("copy",60364),personAdd:l("person-add",60365),filterFilled:l("filter-filled",60366),wand:l("wand",60367),debugLineByLine:l("debug-line-by-line",60368),inspect:l("inspect",60369),layers:l("layers",60370),layersDot:l("layers-dot",60371),layersActive:l("layers-active",60372),compass:l("compass",60373),compassDot:l("compass-dot",60374),compassActive:l("compass-active",60375),azure:l("azure",60376),issueDraft:l("issue-draft",60377),gitPullRequestClosed:l("git-pull-request-closed",60378),gitPullRequestDraft:l("git-pull-request-draft",60379),debugAll:l("debug-all",60380),debugCoverage:l("debug-coverage",60381),runErrors:l("run-errors",60382),folderLibrary:l("folder-library",60383),debugContinueSmall:l("debug-continue-small",60384),beakerStop:l("beaker-stop",60385),graphLine:l("graph-line",60386),graphScatter:l("graph-scatter",60387),pieChart:l("pie-chart",60388),bracket:l("bracket",60175),bracketDot:l("bracket-dot",60389),bracketError:l("bracket-error",60390),lockSmall:l("lock-small",60391),azureDevops:l("azure-devops",60392),verifiedFilled:l("verified-filled",60393),newline:l("newline",60394),layout:l("layout",60395),layoutActivitybarLeft:l("layout-activitybar-left",60396),layoutActivitybarRight:l("layout-activitybar-right",60397),layoutPanelLeft:l("layout-panel-left",60398),layoutPanelCenter:l("layout-panel-center",60399),layoutPanelJustify:l("layout-panel-justify",60400),layoutPanelRight:l("layout-panel-right",60401),layoutPanel:l("layout-panel",60402),layoutSidebarLeft:l("layout-sidebar-left",60403),layoutSidebarRight:l("layout-sidebar-right",60404),layoutStatusbar:l("layout-statusbar",60405),layoutMenubar:l("layout-menubar",60406),layoutCentered:l("layout-centered",60407),target:l("target",60408),indent:l("indent",60409),recordSmall:l("record-small",60410),errorSmall:l("error-small",60411),terminalDecorationError:l("terminal-decoration-error",60411),arrowCircleDown:l("arrow-circle-down",60412),arrowCircleLeft:l("arrow-circle-left",60413),arrowCircleRight:l("arrow-circle-right",60414),arrowCircleUp:l("arrow-circle-up",60415),layoutSidebarRightOff:l("layout-sidebar-right-off",60416),layoutPanelOff:l("layout-panel-off",60417),layoutSidebarLeftOff:l("layout-sidebar-left-off",60418),blank:l("blank",60419),heartFilled:l("heart-filled",60420),map:l("map",60421),mapHorizontal:l("map-horizontal",60421),foldHorizontal:l("fold-horizontal",60421),mapFilled:l("map-filled",60422),mapHorizontalFilled:l("map-horizontal-filled",60422),foldHorizontalFilled:l("fold-horizontal-filled",60422),circleSmall:l("circle-small",60423),bellSlash:l("bell-slash",60424),bellSlashDot:l("bell-slash-dot",60425),commentUnresolved:l("comment-unresolved",60426),gitPullRequestGoToChanges:l("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:l("git-pull-request-new-changes",60428),searchFuzzy:l("search-fuzzy",60429),commentDraft:l("comment-draft",60430),send:l("send",60431),sparkle:l("sparkle",60432),insert:l("insert",60433),mic:l("mic",60434),thumbsdownFilled:l("thumbsdown-filled",60435),thumbsupFilled:l("thumbsup-filled",60436),coffee:l("coffee",60437),snake:l("snake",60438),game:l("game",60439),vr:l("vr",60440),chip:l("chip",60441),piano:l("piano",60442),music:l("music",60443),micFilled:l("mic-filled",60444),repoFetch:l("repo-fetch",60445),copilot:l("copilot",60446),lightbulbSparkle:l("lightbulb-sparkle",60447),robot:l("robot",60448),sparkleFilled:l("sparkle-filled",60449),diffSingle:l("diff-single",60450),diffMultiple:l("diff-multiple",60451),surroundWith:l("surround-with",60452),share:l("share",60453),gitStash:l("git-stash",60454),gitStashApply:l("git-stash-apply",60455),gitStashPop:l("git-stash-pop",60456),vscode:l("vscode",60457),vscodeInsiders:l("vscode-insiders",60458),codeOss:l("code-oss",60459),runCoverage:l("run-coverage",60460),runAllCoverage:l("run-all-coverage",60461),coverage:l("coverage",60462),githubProject:l("github-project",60463),mapVertical:l("map-vertical",60464),foldVertical:l("fold-vertical",60464),mapVerticalFilled:l("map-vertical-filled",60465),foldVerticalFilled:l("fold-vertical-filled",60465),goToSearch:l("go-to-search",60466),percentage:l("percentage",60467),sortPercentage:l("sort-percentage",60467),attach:l("attach",60468),goToEditingSession:l("go-to-editing-session",60469),editSession:l("edit-session",60470),codeReview:l("code-review",60471),copilotWarning:l("copilot-warning",60472),python:l("python",60473),copilotLarge:l("copilot-large",60474),copilotWarningLarge:l("copilot-warning-large",60475),keyboardTab:l("keyboard-tab",60476),copilotBlocked:l("copilot-blocked",60477),copilotNotConnected:l("copilot-not-connected",60478),flag:l("flag",60479),lightbulbEmpty:l("lightbulb-empty",60480),symbolMethodArrow:l("symbol-method-arrow",60481),copilotUnavailable:l("copilot-unavailable",60482),repoPinned:l("repo-pinned",60483),keyboardTabAbove:l("keyboard-tab-above",60484),keyboardTabBelow:l("keyboard-tab-below",60485),gitPullRequestDone:l("git-pull-request-done",60486),mcp:l("mcp",60487),extensionsLarge:l("extensions-large",60488),layoutPanelDock:l("layout-panel-dock",60489),layoutSidebarLeftDock:l("layout-sidebar-left-dock",60490),layoutSidebarRightDock:l("layout-sidebar-right-dock",60491),copilotInProgress:l("copilot-in-progress",60492),copilotError:l("copilot-error",60493),copilotSuccess:l("copilot-success",60494),chatSparkle:l("chat-sparkle",60495),searchSparkle:l("search-sparkle",60496),editSparkle:l("edit-sparkle",60497),copilotSnooze:l("copilot-snooze",60498),sendToRemoteAgent:l("send-to-remote-agent",60499)},_0={dialogError:l("dialog-error","error"),dialogWarning:l("dialog-warning","warning"),dialogInfo:l("dialog-info","info"),dialogClose:l("dialog-close","close"),treeItemExpanded:l("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:l("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:l("tree-filter-on-type-off","list-selection"),treeFilterClear:l("tree-filter-clear","close"),treeItemLoading:l("tree-item-loading","loading"),menuSelection:l("menu-selection","check"),menuSubmenu:l("menu-submenu","chevron-right"),menuBarMore:l("menubar-more","more"),scrollbarButtonLeft:l("scrollbar-button-left","triangle-left"),scrollbarButtonRight:l("scrollbar-button-right","triangle-right"),scrollbarButtonUp:l("scrollbar-button-up","triangle-up"),scrollbarButtonDown:l("scrollbar-button-down","triangle-down"),toolBarMore:l("toolbar-more","more"),quickInputBack:l("quick-input-back","arrow-left"),dropDownButton:l("drop-down-button",60084),symbolCustomColor:l("symbol-customcolor",60252),exportIcon:l("export",60332),workspaceUnspecified:l("workspace-unspecified",60355),newLine:l("newline",60394),thumbsDownFilled:l("thumbsdown-filled",60435),thumbsUpFilled:l("thumbsup-filled",60436),gitFetch:l("git-fetch",60445),lightbulbSparkleAutofix:l("lightbulb-sparkle-autofix",60447),debugBreakpointPending:l("debug-breakpoint-pending",60377)},D={...T0,..._0},D0=class{constructor(){this.a=new Map,this.b=new Map,this.c=new ge,this.onDidChange=this.c.event,this.d=null}handleChange(e){this.c.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this.a.set(e,t),this.handleChange([e]),Bt(()=>{this.a.get(e)===t&&(this.a.delete(e),this.handleChange([e]))})}get(e){return this.a.get(e)||null}registerFactory(e,t){this.b.get(e)?.dispose();const n=new S0(this,e,t);return this.b.set(e,n),Bt(()=>{const r=this.b.get(e);!r||r!==n||(this.b.delete(e),r.dispose())})}async getOrCreate(e){const t=this.get(e);if(t)return t;const n=this.b.get(e);return!n||n.isResolved?null:(await n.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;const n=this.b.get(e);return!!(!n||n.isResolved)}setColorMap(e){this.d=e,this.c.fire({changedLanguages:Array.from(this.a.keys()),changedColorMap:!0})}getColorMap(){return this.d}getDefaultBackground(){return this.d&&this.d.length>2?this.d[2]:null}},S0=class extends Ze{get isResolved(){return this.c}constructor(e,t,n){super(),this.f=e,this.g=t,this.h=n,this.a=!1,this.b=null,this.c=!1}dispose(){this.a=!0,super.dispose()}async resolve(){return this.b||(this.b=this.j()),this.b}async j(){const e=await this.h.tokenizationSupport;this.c=!0,e&&!this.a&&this.B(this.f.register(this.g,e))}},$0=class{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}},N1;(function(e){e[e.Increase=0]="Increase",e[e.Decrease=1]="Decrease"})(N1||(N1={}));var R1;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Tool=27]="Tool",e[e.Snippet=28]="Snippet"})(R1||(R1={}));var A1;(function(e){const t=new Map;t.set(0,D.symbolMethod),t.set(1,D.symbolFunction),t.set(2,D.symbolConstructor),t.set(3,D.symbolField),t.set(4,D.symbolVariable),t.set(5,D.symbolClass),t.set(6,D.symbolStruct),t.set(7,D.symbolInterface),t.set(8,D.symbolModule),t.set(9,D.symbolProperty),t.set(10,D.symbolEvent),t.set(11,D.symbolOperator),t.set(12,D.symbolUnit),t.set(13,D.symbolValue),t.set(15,D.symbolEnum),t.set(14,D.symbolConstant),t.set(15,D.symbolEnum),t.set(16,D.symbolEnumMember),t.set(17,D.symbolKeyword),t.set(28,D.symbolSnippet),t.set(18,D.symbolText),t.set(19,D.symbolColor),t.set(20,D.symbolFile),t.set(21,D.symbolReference),t.set(22,D.symbolCustomColor),t.set(23,D.symbolFolder),t.set(24,D.symbolTypeParameter),t.set(25,D.account),t.set(26,D.issues),t.set(27,D.tools);function n(a){let u=t.get(a);return u||(console.info("No codicon found for CompletionItemKind "+a),u=D.symbolProperty),u}e.toIcon=n;function r(a){switch(a){case 0:return S(833,null);case 1:return S(834,null);case 2:return S(835,null);case 3:return S(836,null);case 4:return S(837,null);case 5:return S(838,null);case 6:return S(839,null);case 7:return S(840,null);case 8:return S(841,null);case 9:return S(842,null);case 10:return S(843,null);case 11:return S(844,null);case 12:return S(845,null);case 13:return S(846,null);case 14:return S(847,null);case 15:return S(848,null);case 16:return S(849,null);case 17:return S(850,null);case 18:return S(851,null);case 19:return S(852,null);case 20:return S(853,null);case 21:return S(854,null);case 22:return S(855,null);case 23:return S(856,null);case 24:return S(857,null);case 25:return S(858,null);case 26:return S(859,null);case 27:return S(860,null);case 28:return S(861,null);default:return""}}e.toLabel=r;const s=new Map;s.set("method",0),s.set("function",1),s.set("constructor",2),s.set("field",3),s.set("variable",4),s.set("class",5),s.set("struct",6),s.set("interface",7),s.set("module",8),s.set("property",9),s.set("event",10),s.set("operator",11),s.set("unit",12),s.set("value",13),s.set("constant",14),s.set("enum",15),s.set("enum-member",16),s.set("enumMember",16),s.set("keyword",17),s.set("snippet",28),s.set("text",18),s.set("color",19),s.set("file",20),s.set("reference",21),s.set("customcolor",22),s.set("folder",23),s.set("type-parameter",24),s.set("typeParameter",24),s.set("account",25),s.set("issue",26),s.set("tool",27);function i(a,u){let o=s.get(a);return typeof o>"u"&&!u&&(o=9),o}e.fromString=i})(A1||(A1={}));var x1;(function(e){e[e.Deprecated=1]="Deprecated"})(x1||(x1={}));var E1;(function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(E1||(E1={}));var M1;(function(e){e[e.Word=0]="Word",e[e.Line=1]="Line",e[e.Suggest=2]="Suggest"})(M1||(M1={}));var y1;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(y1||(y1={}));var k1;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(k1||(k1={}));var F1;(function(e){e[e.Accepted=0]="Accepted",e[e.Rejected=1]="Rejected",e[e.Ignored=2]="Ignored"})(F1||(F1={}));var P1;(function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"})(P1||(P1={}));var T1;(function(e){e[e.Automatic=0]="Automatic",e[e.PasteAs=1]="PasteAs"})(T1||(T1={}));var _1;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(_1||(_1={}));var D1;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(D1||(D1={}));var S1;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(S1||(S1={}));var No={17:S(862,null),16:S(863,null),4:S(864,null),13:S(865,null),8:S(866,null),9:S(867,null),21:S(868,null),23:S(869,null),7:S(870,null),0:S(871,null),11:S(872,null),10:S(873,null),19:S(874,null),5:S(875,null),1:S(876,null),2:S(877,null),20:S(878,null),15:S(879,null),18:S(880,null),24:S(881,null),3:S(882,null),6:S(883,null),14:S(884,null),22:S(885,null),25:S(886,null),12:S(887,null)},$1;(function(e){e[e.Deprecated=1]="Deprecated"})($1||($1={}));var B1;(function(e){const t=new Map;t.set(0,D.symbolFile),t.set(1,D.symbolModule),t.set(2,D.symbolNamespace),t.set(3,D.symbolPackage),t.set(4,D.symbolClass),t.set(5,D.symbolMethod),t.set(6,D.symbolProperty),t.set(7,D.symbolField),t.set(8,D.symbolConstructor),t.set(9,D.symbolEnum),t.set(10,D.symbolInterface),t.set(11,D.symbolFunction),t.set(12,D.symbolVariable),t.set(13,D.symbolConstant),t.set(14,D.symbolString),t.set(15,D.symbolNumber),t.set(16,D.symbolBoolean),t.set(17,D.symbolArray),t.set(18,D.symbolObject),t.set(19,D.symbolKey),t.set(20,D.symbolNull),t.set(21,D.symbolEnumMember),t.set(22,D.symbolStruct),t.set(23,D.symbolEvent),t.set(24,D.symbolOperator),t.set(25,D.symbolTypeParameter);function n(i){let a=t.get(i);return a||(console.info("No codicon found for SymbolKind "+i),a=D.symbolProperty),a}e.toIcon=n;const r=new Map;r.set(0,20),r.set(1,8),r.set(2,8),r.set(3,8),r.set(4,5),r.set(5,0),r.set(6,9),r.set(7,3),r.set(8,2),r.set(9,15),r.set(10,7),r.set(11,1),r.set(12,4),r.set(13,14),r.set(14,18),r.set(15,13),r.set(16,13),r.set(17,13),r.set(18,13),r.set(19,17),r.set(20,13),r.set(21,16),r.set(22,6),r.set(23,10),r.set(24,11),r.set(25,24);function s(i){let a=r.get(i);return a===void 0&&(console.info("No completion kind found for SymbolKind "+i),a=20),a}e.toCompletionKind=s})(B1||(B1={}));var Ro=class $e{static{this.Comment=new $e("comment")}static{this.Imports=new $e("imports")}static{this.Region=new $e("region")}static fromValue(t){switch(t){case"comment":return $e.Comment;case"imports":return $e.Imports;case"region":return $e.Region}return new $e(t)}constructor(t){this.value=t}},V1;(function(e){e[e.AIGenerated=1]="AIGenerated"})(V1||(V1={}));var q1;(function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"})(q1||(q1={}));var I1;(function(e){function t(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}e.is=t})(I1||(I1={}));var U1;(function(e){e[e.Collapsed=0]="Collapsed",e[e.Expanded=1]="Expanded"})(U1||(U1={}));var j1;(function(e){e[e.Unresolved=0]="Unresolved",e[e.Resolved=1]="Resolved"})(j1||(j1={}));var O1;(function(e){e[e.Current=0]="Current",e[e.Outdated=1]="Outdated"})(O1||(O1={}));var W1;(function(e){e[e.Editing=0]="Editing",e[e.Preview=1]="Preview"})(W1||(W1={}));var H1;(function(e){e[e.Published=0]="Published",e[e.Draft=1]="Draft"})(H1||(H1={}));var z1;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(z1||(z1={}));var Ao=new D0,C1;(function(e){e[e.None=0]="None",e[e.Option=1]="Option",e[e.Default=2]="Default",e[e.Preferred=3]="Preferred"})(C1||(C1={}));var G1;(function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"})(G1||(G1={}));var X1;(function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"})(X1||(X1={}));var J1;(function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(J1||(J1={}));var Y1;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Tool=27]="Tool",e[e.Snippet=28]="Snippet"})(Y1||(Y1={}));var Z1;(function(e){e[e.Deprecated=1]="Deprecated"})(Z1||(Z1={}));var Q1;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(Q1||(Q1={}));var K1;(function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"})(K1||(K1={}));var es;(function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"})(es||(es={}));var ts;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(ts||(ts={}));var ns;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(ns||(ns={}));var rs;(function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"})(rs||(rs={}));var ss;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.allowOverflow=4]="allowOverflow",e[e.allowVariableLineHeights=5]="allowVariableLineHeights",e[e.allowVariableFonts=6]="allowVariableFonts",e[e.allowVariableFontsInAccessibilityMode=7]="allowVariableFontsInAccessibilityMode",e[e.ariaLabel=8]="ariaLabel",e[e.ariaRequired=9]="ariaRequired",e[e.autoClosingBrackets=10]="autoClosingBrackets",e[e.autoClosingComments=11]="autoClosingComments",e[e.screenReaderAnnounceInlineSuggestion=12]="screenReaderAnnounceInlineSuggestion",e[e.autoClosingDelete=13]="autoClosingDelete",e[e.autoClosingOvertype=14]="autoClosingOvertype",e[e.autoClosingQuotes=15]="autoClosingQuotes",e[e.autoIndent=16]="autoIndent",e[e.autoIndentOnPaste=17]="autoIndentOnPaste",e[e.autoIndentOnPasteWithinString=18]="autoIndentOnPasteWithinString",e[e.automaticLayout=19]="automaticLayout",e[e.autoSurround=20]="autoSurround",e[e.bracketPairColorization=21]="bracketPairColorization",e[e.guides=22]="guides",e[e.codeLens=23]="codeLens",e[e.codeLensFontFamily=24]="codeLensFontFamily",e[e.codeLensFontSize=25]="codeLensFontSize",e[e.colorDecorators=26]="colorDecorators",e[e.colorDecoratorsLimit=27]="colorDecoratorsLimit",e[e.columnSelection=28]="columnSelection",e[e.comments=29]="comments",e[e.contextmenu=30]="contextmenu",e[e.copyWithSyntaxHighlighting=31]="copyWithSyntaxHighlighting",e[e.cursorBlinking=32]="cursorBlinking",e[e.cursorSmoothCaretAnimation=33]="cursorSmoothCaretAnimation",e[e.cursorStyle=34]="cursorStyle",e[e.cursorSurroundingLines=35]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=36]="cursorSurroundingLinesStyle",e[e.cursorWidth=37]="cursorWidth",e[e.cursorHeight=38]="cursorHeight",e[e.disableLayerHinting=39]="disableLayerHinting",e[e.disableMonospaceOptimizations=40]="disableMonospaceOptimizations",e[e.domReadOnly=41]="domReadOnly",e[e.dragAndDrop=42]="dragAndDrop",e[e.dropIntoEditor=43]="dropIntoEditor",e[e.editContext=44]="editContext",e[e.emptySelectionClipboard=45]="emptySelectionClipboard",e[e.experimentalGpuAcceleration=46]="experimentalGpuAcceleration",e[e.experimentalWhitespaceRendering=47]="experimentalWhitespaceRendering",e[e.extraEditorClassName=48]="extraEditorClassName",e[e.fastScrollSensitivity=49]="fastScrollSensitivity",e[e.find=50]="find",e[e.fixedOverflowWidgets=51]="fixedOverflowWidgets",e[e.folding=52]="folding",e[e.foldingStrategy=53]="foldingStrategy",e[e.foldingHighlight=54]="foldingHighlight",e[e.foldingImportsByDefault=55]="foldingImportsByDefault",e[e.foldingMaximumRegions=56]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=57]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=58]="fontFamily",e[e.fontInfo=59]="fontInfo",e[e.fontLigatures=60]="fontLigatures",e[e.fontSize=61]="fontSize",e[e.fontWeight=62]="fontWeight",e[e.fontVariations=63]="fontVariations",e[e.formatOnPaste=64]="formatOnPaste",e[e.formatOnType=65]="formatOnType",e[e.glyphMargin=66]="glyphMargin",e[e.gotoLocation=67]="gotoLocation",e[e.hideCursorInOverviewRuler=68]="hideCursorInOverviewRuler",e[e.hover=69]="hover",e[e.inDiffEditor=70]="inDiffEditor",e[e.inlineSuggest=71]="inlineSuggest",e[e.letterSpacing=72]="letterSpacing",e[e.lightbulb=73]="lightbulb",e[e.lineDecorationsWidth=74]="lineDecorationsWidth",e[e.lineHeight=75]="lineHeight",e[e.lineNumbers=76]="lineNumbers",e[e.lineNumbersMinChars=77]="lineNumbersMinChars",e[e.linkedEditing=78]="linkedEditing",e[e.links=79]="links",e[e.matchBrackets=80]="matchBrackets",e[e.minimap=81]="minimap",e[e.mouseStyle=82]="mouseStyle",e[e.mouseWheelScrollSensitivity=83]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=84]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=85]="multiCursorMergeOverlapping",e[e.multiCursorModifier=86]="multiCursorModifier",e[e.multiCursorPaste=87]="multiCursorPaste",e[e.multiCursorLimit=88]="multiCursorLimit",e[e.occurrencesHighlight=89]="occurrencesHighlight",e[e.occurrencesHighlightDelay=90]="occurrencesHighlightDelay",e[e.overtypeCursorStyle=91]="overtypeCursorStyle",e[e.overtypeOnPaste=92]="overtypeOnPaste",e[e.overviewRulerBorder=93]="overviewRulerBorder",e[e.overviewRulerLanes=94]="overviewRulerLanes",e[e.padding=95]="padding",e[e.pasteAs=96]="pasteAs",e[e.parameterHints=97]="parameterHints",e[e.peekWidgetDefaultFocus=98]="peekWidgetDefaultFocus",e[e.placeholder=99]="placeholder",e[e.definitionLinkOpensInPeek=100]="definitionLinkOpensInPeek",e[e.quickSuggestions=101]="quickSuggestions",e[e.quickSuggestionsDelay=102]="quickSuggestionsDelay",e[e.readOnly=103]="readOnly",e[e.readOnlyMessage=104]="readOnlyMessage",e[e.renameOnType=105]="renameOnType",e[e.renderRichScreenReaderContent=106]="renderRichScreenReaderContent",e[e.renderControlCharacters=107]="renderControlCharacters",e[e.renderFinalNewline=108]="renderFinalNewline",e[e.renderLineHighlight=109]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=110]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=111]="renderValidationDecorations",e[e.renderWhitespace=112]="renderWhitespace",e[e.revealHorizontalRightPadding=113]="revealHorizontalRightPadding",e[e.roundedSelection=114]="roundedSelection",e[e.rulers=115]="rulers",e[e.scrollbar=116]="scrollbar",e[e.scrollBeyondLastColumn=117]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=118]="scrollBeyondLastLine",e[e.scrollPredominantAxis=119]="scrollPredominantAxis",e[e.selectionClipboard=120]="selectionClipboard",e[e.selectionHighlight=121]="selectionHighlight",e[e.selectionHighlightMaxLength=122]="selectionHighlightMaxLength",e[e.selectionHighlightMultiline=123]="selectionHighlightMultiline",e[e.selectOnLineNumbers=124]="selectOnLineNumbers",e[e.showFoldingControls=125]="showFoldingControls",e[e.showUnused=126]="showUnused",e[e.snippetSuggestions=127]="snippetSuggestions",e[e.smartSelect=128]="smartSelect",e[e.smoothScrolling=129]="smoothScrolling",e[e.stickyScroll=130]="stickyScroll",e[e.stickyTabStops=131]="stickyTabStops",e[e.stopRenderingLineAfter=132]="stopRenderingLineAfter",e[e.suggest=133]="suggest",e[e.suggestFontSize=134]="suggestFontSize",e[e.suggestLineHeight=135]="suggestLineHeight",e[e.suggestOnTriggerCharacters=136]="suggestOnTriggerCharacters",e[e.suggestSelection=137]="suggestSelection",e[e.tabCompletion=138]="tabCompletion",e[e.tabIndex=139]="tabIndex",e[e.trimWhitespaceOnDelete=140]="trimWhitespaceOnDelete",e[e.unicodeHighlighting=141]="unicodeHighlighting",e[e.unusualLineTerminators=142]="unusualLineTerminators",e[e.useShadowDOM=143]="useShadowDOM",e[e.useTabStops=144]="useTabStops",e[e.wordBreak=145]="wordBreak",e[e.wordSegmenterLocales=146]="wordSegmenterLocales",e[e.wordSeparators=147]="wordSeparators",e[e.wordWrap=148]="wordWrap",e[e.wordWrapBreakAfterCharacters=149]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=150]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=151]="wordWrapColumn",e[e.wordWrapOverride1=152]="wordWrapOverride1",e[e.wordWrapOverride2=153]="wordWrapOverride2",e[e.wrappingIndent=154]="wrappingIndent",e[e.wrappingStrategy=155]="wrappingStrategy",e[e.showDeprecated=156]="showDeprecated",e[e.inertialScroll=157]="inertialScroll",e[e.inlayHints=158]="inlayHints",e[e.wrapOnEscapedLineFeeds=159]="wrapOnEscapedLineFeeds",e[e.effectiveCursorStyle=160]="effectiveCursorStyle",e[e.editorClassName=161]="editorClassName",e[e.pixelRatio=162]="pixelRatio",e[e.tabFocusMode=163]="tabFocusMode",e[e.layoutInfo=164]="layoutInfo",e[e.wrappingInfo=165]="wrappingInfo",e[e.defaultColorDecorators=166]="defaultColorDecorators",e[e.colorDecoratorsActivatedOn=167]="colorDecoratorsActivatedOn",e[e.inlineCompletionsAccessibilityVerbose=168]="inlineCompletionsAccessibilityVerbose",e[e.effectiveEditContext=169]="effectiveEditContext",e[e.scrollOnMiddleClick=170]="scrollOnMiddleClick",e[e.effectiveAllowVariableFonts=171]="effectiveAllowVariableFonts"})(ss||(ss={}));var is;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(is||(is={}));var as;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(as||(as={}));var ls;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"})(ls||(ls={}));var us;(function(e){e[e.Increase=0]="Increase",e[e.Decrease=1]="Decrease"})(us||(us={}));var os;(function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"})(os||(os={}));var cs;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(cs||(cs={}));var hs;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(hs||(hs={}));var fs;(function(e){e[e.Accepted=0]="Accepted",e[e.Rejected=1]="Rejected",e[e.Ignored=2]="Ignored"})(fs||(fs={}));var gs;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit"})(gs||(gs={}));var Xn;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.F20=78]="F20",e[e.F21=79]="F21",e[e.F22=80]="F22",e[e.F23=81]="F23",e[e.F24=82]="F24",e[e.NumLock=83]="NumLock",e[e.ScrollLock=84]="ScrollLock",e[e.Semicolon=85]="Semicolon",e[e.Equal=86]="Equal",e[e.Comma=87]="Comma",e[e.Minus=88]="Minus",e[e.Period=89]="Period",e[e.Slash=90]="Slash",e[e.Backquote=91]="Backquote",e[e.BracketLeft=92]="BracketLeft",e[e.Backslash=93]="Backslash",e[e.BracketRight=94]="BracketRight",e[e.Quote=95]="Quote",e[e.OEM_8=96]="OEM_8",e[e.IntlBackslash=97]="IntlBackslash",e[e.Numpad0=98]="Numpad0",e[e.Numpad1=99]="Numpad1",e[e.Numpad2=100]="Numpad2",e[e.Numpad3=101]="Numpad3",e[e.Numpad4=102]="Numpad4",e[e.Numpad5=103]="Numpad5",e[e.Numpad6=104]="Numpad6",e[e.Numpad7=105]="Numpad7",e[e.Numpad8=106]="Numpad8",e[e.Numpad9=107]="Numpad9",e[e.NumpadMultiply=108]="NumpadMultiply",e[e.NumpadAdd=109]="NumpadAdd",e[e.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=111]="NumpadSubtract",e[e.NumpadDecimal=112]="NumpadDecimal",e[e.NumpadDivide=113]="NumpadDivide",e[e.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",e[e.ABNT_C1=115]="ABNT_C1",e[e.ABNT_C2=116]="ABNT_C2",e[e.AudioVolumeMute=117]="AudioVolumeMute",e[e.AudioVolumeUp=118]="AudioVolumeUp",e[e.AudioVolumeDown=119]="AudioVolumeDown",e[e.BrowserSearch=120]="BrowserSearch",e[e.BrowserHome=121]="BrowserHome",e[e.BrowserBack=122]="BrowserBack",e[e.BrowserForward=123]="BrowserForward",e[e.MediaTrackNext=124]="MediaTrackNext",e[e.MediaTrackPrevious=125]="MediaTrackPrevious",e[e.MediaStop=126]="MediaStop",e[e.MediaPlayPause=127]="MediaPlayPause",e[e.LaunchMediaPlayer=128]="LaunchMediaPlayer",e[e.LaunchMail=129]="LaunchMail",e[e.LaunchApp2=130]="LaunchApp2",e[e.Clear=131]="Clear",e[e.MAX_VALUE=132]="MAX_VALUE"})(Xn||(Xn={}));var Jn;(function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"})(Jn||(Jn={}));var Yn;(function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"})(Yn||(Yn={}));var ms;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(ms||(ms={}));var ds;(function(e){e[e.Normal=1]="Normal",e[e.Underlined=2]="Underlined"})(ds||(ds={}));var bs;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(bs||(bs={}));var ws;(function(e){e[e.AIGenerated=1]="AIGenerated"})(ws||(ws={}));var vs;(function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"})(vs||(vs={}));var ps;(function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"})(ps||(ps={}));var Ls;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(Ls||(Ls={}));var Ns;(function(e){e[e.Word=0]="Word",e[e.Line=1]="Line",e[e.Suggest=2]="Suggest"})(Ns||(Ns={}));var Rs;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(Rs||(Rs={}));var As;(function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"})(As||(As={}));var xs;(function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"})(xs||(xs={}));var Es;(function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"})(Es||(Es={}));var Ms;(function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"})(Ms||(Ms={}));var Zn;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(Zn||(Zn={}));var ys;(function(e){e.Off="off",e.OnCode="onCode",e.On="on"})(ys||(ys={}));var ks;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(ks||(ks={}));var Fs;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(Fs||(Fs={}));var Ps;(function(e){e[e.Deprecated=1]="Deprecated"})(Ps||(Ps={}));var Ts;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(Ts||(Ts={}));var _s;(function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"})(_s||(_s={}));var Ds;(function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"})(Ds||(Ds={}));var Ss;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(Ss||(Ss={}));var $s;(function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"})($s||($s={}));var B0=class{static{this.CtrlCmd=2048}static{this.Shift=1024}static{this.Alt=512}static{this.WinCtrl=256}static chord(e,t){return a0(e,t)}};function V0(){return{editor:void 0,languages:void 0,CancellationTokenSource:ul,Emitter:ge,KeyCode:Xn,KeyMod:B0,Position:O,Range:_,Selection:P0,SelectionDirection:Zn,MarkerSeverity:Jn,MarkerTag:Yn,Uri:oe,Token:$0}}var q0=60,I0=q0*60,Qn=I0*24,xo=Qn*7,Eo=Qn*30,Mo=Qn*365,Bs;(function(e){e[e.Regular=0]="Regular",e[e.Whitespace=1]="Whitespace",e[e.WordSeparator=2]="WordSeparator"})(Bs||(Bs={}));var yo=new Aa(10),Vs;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(Vs||(Vs={}));var qs;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"})(qs||(qs={}));var Is;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(Is||(Is={}));var Us;(function(e){e[e.Normal=1]="Normal",e[e.Underlined=2]="Underlined"})(Us||(Us={}));var js;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(js||(js={}));var Os;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(Os||(Os={}));var Ws;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Ws||(Ws={}));var Hs;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(Hs||(Hs={}));var zs;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(zs||(zs={}));var Cs;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(Cs||(Cs={}));var Gs;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(Gs||(Gs={}));var Xs;(function(e){e[e.FIRST_LINE_DETECTION_LENGTH_LIMIT=1e3]="FIRST_LINE_DETECTION_LENGTH_LIMIT"})(Xs||(Xs={}));function U0(e){if(!e||e.length===0)return!1;for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r===10)return!0;if(r===92){if(t++,t>=n)break;const s=e.charCodeAt(t);if(s===110||s===114||s===87)return!0}}return!1}function j0(e,t,n,r,s){if(r===0)return!0;const i=t.charCodeAt(r-1);if(e.get(i)!==0||i===13||i===10)return!0;if(s>0){const a=t.charCodeAt(r);if(e.get(a)!==0)return!0}return!1}function O0(e,t,n,r,s){if(r+s===n)return!0;const i=t.charCodeAt(r+s);if(e.get(i)!==0||i===13||i===10)return!0;if(s>0){const a=t.charCodeAt(r+s-1);if(e.get(a)!==0)return!0}return!1}function W0(e,t,n,r,s){return j0(e,t,n,r,s)&&O0(e,t,n,r,s)}var H0=class{constructor(e,t){this._wordSeparators=e,this.a=t,this.b=-1,this.c=0}reset(e){this.a.lastIndex=e,this.b=-1,this.c=0}next(e){const t=e.length;let n;do{if(this.b+this.c===t||(n=this.a.exec(e),!n))return null;const r=n.index,s=n[0].length;if(r===this.b&&s===this.c){if(s===0){pl(e,t,this.a.lastIndex)>65535?this.a.lastIndex+=2:this.a.lastIndex+=1;continue}return null}if(this.b=r,this.c=s,!this._wordSeparators||W0(this._wordSeparators,e,t,r,s))return n}while(n);return null}},z0="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function C0(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of z0)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}var Js=C0();function Ys(e){let t=Js;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}var Zs=new _a;Zs.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function Kn(e,t,n,r,s){if(t=Ys(t),s||(s=$t.first(Zs)),n.length>s.maxLen){let c=e-s.maxLen/2;return c<0?c=0:r+=c,n=n.substring(c,e+s.maxLen/2),Kn(e,t,n,r,s)}const i=Date.now(),a=e-1-r;let u=-1,o=null;for(let c=1;!(Date.now()-i>=s.timeBudget);c++){const f=a-s.windowSize*c;t.lastIndex=Math.max(0,f);const h=G0(t,n,a,u);if(!h&&o||(o=h,f<=0))break;u=f}if(o){const c={word:o[0],startColumn:r+1+o.index,endColumn:r+1+o.index+o[0].length};return t.lastIndex=0,c}return null}function G0(e,t,n,r){let s;for(;s=e.exec(t);){const i=s.index||0;if(i<=n&&e.lastIndex>=n)return s;if(r>0&&i>r)return null}return null}var X0=class{static computeUnicodeHighlights(e,t,n){const r=n?n.startLineNumber:1,s=n?n.endLineNumber:e.getLineCount(),i=new Ks(t),a=i.getCandidateCodePoints();let u;a==="allNonBasicAscii"?u=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):u=new RegExp(`${J0(Array.from(a))}`,"g");const o=new H0(null,u),c=[];let f=!1,h,g=0,m=0,d=0;e:for(let p=r,v=s;p<=v;p++){const w=e.getLineContent(p),A=w.length;o.reset(0);do if(h=o.next(w),h){let N=h.index,x=h.index+h[0].length;if(N>0){const R=w.charCodeAt(N-1);Gt(R)&&N--}if(x+1<A){const R=w.charCodeAt(x-1);Gt(R)&&x++}const k=w.substring(N,x);let M=Kn(N+1,Js,w,0);M&&M.endColumn<=N+1&&(M=null);const L=i.shouldHighlightNonBasicASCII(k,M?M.word:null);if(L!==0){if(L===3?g++:L===2?m++:L===1?d++:xa(L),c.length>=1e3){f=!0;break e}c.push(new _(p,N+1,p,x+1))}}while(h)}return{ranges:c,hasMore:f,ambiguousCharacterCount:g,invisibleCharacterCount:m,nonBasicAsciiCharacterCount:d}}static computeUnicodeHighlightReason(e,t){const n=new Ks(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{const s=e.codePointAt(0),i=n.ambiguousCharacters.getPrimaryConfusable(s),a=Dn.getLocales().filter(u=>!Dn.getInstance(new Set([...t.allowedLocales,u])).isAmbiguous(s));return{kind:0,confusableWith:String.fromCodePoint(i),notAmbiguousInLocales:a}}case 1:return{kind:2}}}};function J0(e,t){return`[${hl(e.map(r=>String.fromCodePoint(r)).join(""))}]`}var Qs;(function(e){e[e.Ambiguous=0]="Ambiguous",e[e.Invisible=1]="Invisible",e[e.NonBasicAscii=2]="NonBasicAscii"})(Qs||(Qs={}));var Ks=class{constructor(e){this.b=e,this.a=new Set(e.allowedCodePoints),this.ambiguousCharacters=Dn.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.b.nonBasicASCII)return"allNonBasicAscii";const e=new Set;if(this.b.invisibleCharacters)for(const t of Sn.codePoints)ei(String.fromCodePoint(t))||e.add(t);if(this.b.ambiguousCharacters)for(const t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(const t of this.a)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){const n=e.codePointAt(0);if(this.a.has(n))return 0;if(this.b.nonBasicASCII)return 1;let r=!1,s=!1;if(t)for(const i of t){const a=i.codePointAt(0),u=Nl(i);r=r||u,!u&&!this.ambiguousCharacters.isAmbiguous(a)&&!Sn.isInvisibleCharacter(a)&&(s=!0)}return!r&&s?0:this.b.invisibleCharacters&&!ei(e)&&Sn.isInvisibleCharacter(n)?2:this.b.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}};function ei(e){return e===" "||e===`
`||e==="	"}var ti;(function(e){e[e.None=0]="None",e[e.NonBasicASCII=1]="NonBasicASCII",e[e.Invisible=2]="Invisible",e[e.Ambiguous=3]="Ambiguous"})(ti||(ti={}));var en=class{constructor(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}},Y0=class Zi{constructor(t,n){this.lineRangeMapping=t,this.changes=n}flip(){return new Zi(this.lineRangeMapping.flip(),this.changes.map(t=>t.flip()))}},V=class ce{static fromTo(t,n){return new ce(t,n)}static addRange(t,n){let r=0;for(;r<n.length&&n[r].endExclusive<t.start;)r++;let s=r;for(;s<n.length&&n[s].start<=t.endExclusive;)s++;if(r===s)n.splice(r,0,t);else{const i=Math.min(t.start,n[r].start),a=Math.max(t.endExclusive,n[s-1].endExclusive);n.splice(r,s-r,new ce(i,a))}}static tryCreate(t,n){if(!(t>n))return new ce(t,n)}static ofLength(t){return new ce(0,t)}static ofStartAndLength(t,n){return new ce(t,t+n)}static emptyAt(t){return new ce(t,t)}constructor(t,n){if(this.start=t,this.endExclusive=n,t>n)throw new K(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(t){return new ce(this.start+t,this.endExclusive+t)}deltaStart(t){return new ce(this.start+t,this.endExclusive)}deltaEnd(t){return new ce(this.start,this.endExclusive+t)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}equals(t){return this.start===t.start&&this.endExclusive===t.endExclusive}containsRange(t){return this.start<=t.start&&t.endExclusive<=this.endExclusive}contains(t){return this.start<=t&&t<this.endExclusive}join(t){return new ce(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))}intersect(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);if(n<=r)return new ce(n,r)}intersectionLength(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);return Math.max(0,r-n)}intersects(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);return n<r}intersectsOrTouches(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);return n<=r}isBefore(t){return this.endExclusive<=t.start}isAfter(t){return this.start>=t.endExclusive}slice(t){return t.slice(this.start,this.endExclusive)}substring(t){return t.substring(this.start,this.endExclusive)}clip(t){if(this.isEmpty)throw new K(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,t))}clipCyclic(t){if(this.isEmpty)throw new K(`Invalid clipping range: ${this.toString()}`);return t<this.start?this.endExclusive-(this.start-t)%this.length:t>=this.endExclusive?this.start+(t-this.start)%this.length:t}map(t){const n=[];for(let r=this.start;r<this.endExclusive;r++)n.push(t(r));return n}forEach(t){for(let n=this.start;n<this.endExclusive;n++)t(n)}joinRightTouching(t){if(this.endExclusive!==t.start)throw new K(`Invalid join: ${this.toString()} and ${t.toString()}`);return new ce(this.start,t.endExclusive)}},W=class ie{static ofLength(t,n){return new ie(t,t+n)}static fromRange(t){return new ie(t.startLineNumber,t.endLineNumber)}static fromRangeInclusive(t){return new ie(t.startLineNumber,t.endLineNumber+1)}static{this.compareByStart=Ie(t=>t.startLineNumber,Je)}static subtract(t,n){return n?t.startLineNumber<n.startLineNumber&&n.endLineNumberExclusive<t.endLineNumberExclusive?[new ie(t.startLineNumber,n.startLineNumber),new ie(n.endLineNumberExclusive,t.endLineNumberExclusive)]:n.startLineNumber<=t.startLineNumber&&t.endLineNumberExclusive<=n.endLineNumberExclusive?[]:n.endLineNumberExclusive<t.endLineNumberExclusive?[new ie(Math.max(n.endLineNumberExclusive,t.startLineNumber),t.endLineNumberExclusive)]:[new ie(t.startLineNumber,Math.min(n.startLineNumber,t.endLineNumberExclusive))]:[t]}static joinMany(t){if(t.length===0)return[];let n=new tn(t[0].slice());for(let r=1;r<t.length;r++)n=n.getUnion(new tn(t[r].slice()));return n.ranges}static join(t){if(t.length===0)throw new K("lineRanges cannot be empty");let n=t[0].startLineNumber,r=t[0].endLineNumberExclusive;for(let s=1;s<t.length;s++)n=Math.min(n,t[s].startLineNumber),r=Math.max(r,t[s].endLineNumberExclusive);return new ie(n,r)}static deserialize(t){return new ie(t[0],t[1])}constructor(t,n){if(t>n)throw new K(`startLineNumber ${t} cannot be after endLineNumberExclusive ${n}`);this.startLineNumber=t,this.endLineNumberExclusive=n}contains(t){return this.startLineNumber<=t&&t<this.endLineNumberExclusive}containsRange(t){return this.startLineNumber<=t.startLineNumber&&t.endLineNumberExclusive<=this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(t){return new ie(this.startLineNumber+t,this.endLineNumberExclusive+t)}deltaLength(t){return new ie(this.startLineNumber,this.endLineNumberExclusive+t)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(t){return new ie(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(t){const n=Math.max(this.startLineNumber,t.startLineNumber),r=Math.min(this.endLineNumberExclusive,t.endLineNumberExclusive);if(n<=r)return new ie(n,r)}intersectsStrict(t){return this.startLineNumber<t.endLineNumberExclusive&&t.startLineNumber<this.endLineNumberExclusive}intersectsOrTouches(t){return this.startLineNumber<=t.endLineNumberExclusive&&t.startLineNumber<=this.endLineNumberExclusive}equals(t){return this.startLineNumber===t.startLineNumber&&this.endLineNumberExclusive===t.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new _(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new _(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(t){const n=[];for(let r=this.startLineNumber;r<this.endLineNumberExclusive;r++)n.push(t(r));return n}forEach(t){for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t(n)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}toOffsetRange(){return new V(this.startLineNumber-1,this.endLineNumberExclusive-1)}distanceToRange(t){return this.endLineNumberExclusive<=t.startLineNumber?t.startLineNumber-this.endLineNumberExclusive:t.endLineNumberExclusive<=this.startLineNumber?this.startLineNumber-t.endLineNumberExclusive:0}distanceToLine(t){return this.contains(t)?0:t<this.startLineNumber?this.startLineNumber-t:t-this.endLineNumberExclusive}addMargin(t,n){return new ie(this.startLineNumber-t,this.endLineNumberExclusive+n)}},tn=class ht{constructor(t=[]){this.c=t}get ranges(){return this.c}addRange(t){if(t.length===0)return;const n=Nn(this.c,s=>s.endLineNumberExclusive>=t.startLineNumber),r=Xe(this.c,s=>s.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)this.c.splice(n,0,t);else if(n===r-1){const s=this.c[n];this.c[n]=s.join(t)}else{const s=this.c[n].join(this.c[r-1]).join(t);this.c.splice(n,r-n,s)}}contains(t){const n=Ge(this.c,r=>r.startLineNumber<=t);return!!n&&n.endLineNumberExclusive>t}intersects(t){const n=Ge(this.c,r=>r.startLineNumber<t.endLineNumberExclusive);return!!n&&n.endLineNumberExclusive>t.startLineNumber}getUnion(t){if(this.c.length===0)return t;if(t.c.length===0)return this;const n=[];let r=0,s=0,i=null;for(;r<this.c.length||s<t.c.length;){let a=null;if(r<this.c.length&&s<t.c.length){const u=this.c[r],o=t.c[s];u.startLineNumber<o.startLineNumber?(a=u,r++):(a=o,s++)}else r<this.c.length?(a=this.c[r],r++):(a=t.c[s],s++);i===null?i=a:i.endLineNumberExclusive>=a.startLineNumber?i=new W(i.startLineNumber,Math.max(i.endLineNumberExclusive,a.endLineNumberExclusive)):(n.push(i),i=a)}return i!==null&&n.push(i),new ht(n)}subtractFrom(t){const n=Nn(this.c,a=>a.endLineNumberExclusive>=t.startLineNumber),r=Xe(this.c,a=>a.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)return new ht([t]);const s=[];let i=t.startLineNumber;for(let a=n;a<r;a++){const u=this.c[a];u.startLineNumber>i&&s.push(new W(i,u.startLineNumber)),i=u.endLineNumberExclusive}return i<t.endLineNumberExclusive&&s.push(new W(i,t.endLineNumberExclusive)),new ht(s)}toString(){return this.c.map(t=>t.toString()).join(", ")}getIntersection(t){const n=[];let r=0,s=0;for(;r<this.c.length&&s<t.c.length;){const i=this.c[r],a=t.c[s],u=i.intersect(a);u&&!u.isEmpty&&n.push(u),i.endLineNumberExclusive<a.endLineNumberExclusive?r++:s++}return new ht(n)}getWithDelta(t){return new ht(this.c.map(n=>n.delta(t)))}},st=class we{static{this.zero=new we(0,0)}static lengthDiffNonNegative(t,n){return n.isLessThan(t)?we.zero:t.lineCount===n.lineCount?new we(0,n.columnCount-t.columnCount):new we(n.lineCount-t.lineCount,n.columnCount)}static betweenPositions(t,n){return t.lineNumber===n.lineNumber?new we(0,n.column-t.column):new we(n.lineNumber-t.lineNumber,n.column-1)}static fromPosition(t){return new we(t.lineNumber-1,t.column-1)}static ofRange(t){return we.betweenPositions(t.getStartPosition(),t.getEndPosition())}static ofText(t){let n=0,r=0;for(const s of t)s===`
`?(n++,r=0):r++;return new we(n,r)}constructor(t,n){this.lineCount=t,this.columnCount=n}isZero(){return this.lineCount===0&&this.columnCount===0}isLessThan(t){return this.lineCount!==t.lineCount?this.lineCount<t.lineCount:this.columnCount<t.columnCount}isGreaterThan(t){return this.lineCount!==t.lineCount?this.lineCount>t.lineCount:this.columnCount>t.columnCount}isGreaterThanOrEqualTo(t){return this.lineCount!==t.lineCount?this.lineCount>t.lineCount:this.columnCount>=t.columnCount}equals(t){return this.lineCount===t.lineCount&&this.columnCount===t.columnCount}compare(t){return this.lineCount!==t.lineCount?this.lineCount-t.lineCount:this.columnCount-t.columnCount}add(t){return t.lineCount===0?new we(this.lineCount,this.columnCount+t.columnCount):new we(this.lineCount+t.lineCount,t.columnCount)}createRange(t){return this.lineCount===0?new _(t.lineNumber,t.column,t.lineNumber,t.column+this.columnCount):new _(t.lineNumber,t.column,t.lineNumber+this.lineCount,this.columnCount+1)}toRange(){return new _(1,1,this.lineCount+1,this.columnCount+1)}toLineRange(){return W.ofLength(1,this.lineCount+1)}addToPosition(t){return this.lineCount===0?new O(t.lineNumber,t.column+this.columnCount):new O(t.lineNumber+this.lineCount,this.columnCount+1)}addToRange(t){return _.fromPositions(this.addToPosition(t.getStartPosition()),this.addToPosition(t.getEndPosition()))}toString(){return`${this.lineCount},${this.columnCount}`}},Z0=class{getOffsetRange(e){return new V(this.getOffset(e.getStartPosition()),this.getOffset(e.getEndPosition()))}getRange(e){return _.fromPositions(this.getPosition(e.start),this.getPosition(e.endExclusive))}getStringEdit(e){const t=e.replacements.map(n=>this.getStringReplacement(n));return new Oe.deps.StringEdit(t)}getStringReplacement(e){return new Oe.deps.StringReplacement(this.getOffsetRange(e.range),e.text)}getTextReplacement(e){return new Oe.deps.TextReplacement(this.getRange(e.replaceRange),e.newText)}getTextEdit(e){const t=e.replacements.map(n=>this.getTextReplacement(n));return new Oe.deps.TextEdit(t)}},Oe=class{static{this._deps=void 0}static get deps(){if(!this._deps)throw new Error("Dependencies not set. Call _setDependencies first.");return this._deps}};function Q0(e){Oe._deps=e}var ni=class extends Z0{constructor(e){super(),this.text=e,this.a=[],this.b=[],this.a.push(0);for(let t=0;t<e.length;t++)e.charAt(t)===`
`&&(this.a.push(t+1),t>0&&e.charAt(t-1)==="\r"?this.b.push(t-1):this.b.push(t));this.b.push(e.length)}getOffset(e){const t=this.c(e);return this.a[t.lineNumber-1]+t.column-1}c(e){if(e.lineNumber<1)return new O(1,1);const t=this.textLength.lineCount+1;if(e.lineNumber>t){const r=this.getLineLength(t);return new O(t,r+1)}if(e.column<1)return new O(e.lineNumber,1);const n=this.getLineLength(e.lineNumber);return e.column-1>n?new O(e.lineNumber,n+1):e}getPosition(e){const t=Xe(this.a,s=>s<=e),n=t+1,r=e-this.a[t]+1;return new O(n,r)}getTextLength(e){return Oe.deps.TextLength.ofRange(this.getRange(e))}get textLength(){const e=this.a.length-1;return new Oe.deps.TextLength(e,this.text.length-this.a[e])}getLineLength(e){return this.b[e-1]-this.a[e-1]}},ri=class{constructor(){this.a=void 0}get endPositionExclusive(){return this.length.addToPosition(new O(1,1))}get lineRange(){return this.length.toLineRange()}getValue(){return this.getValueOfRange(this.length.toRange())}getValueOfOffsetRange(e){return this.getValueOfRange(this.getTransformer().getRange(e))}getLineLength(e){return this.getValueOfRange(new _(e,1,e,Number.MAX_SAFE_INTEGER)).length}getTransformer(){return this.a||(this.a=new ni(this.getValue())),this.a}getLineAt(e){return this.getValueOfRange(new _(e,1,e,Number.MAX_SAFE_INTEGER))}getLines(){const e=this.getValue();return qr(e)}getLinesOfRange(e){return e.mapToLineArray(t=>this.getLineAt(t))}equals(e){return this===e?!0:this.getValue()===e.getValue()}},K0=class extends ri{constructor(e,t){Ea(t>=1),super(),this.b=e,this.c=t}getValueOfRange(e){if(e.startLineNumber===e.endLineNumber)return this.b(e.startLineNumber).substring(e.startColumn-1,e.endColumn-1);let t=this.b(e.startLineNumber).substring(e.startColumn-1);for(let n=e.startLineNumber+1;n<e.endLineNumber;n++)t+=`
`+this.b(n);return t+=`
`+this.b(e.endLineNumber).substring(0,e.endColumn-1),t}getLineLength(e){return this.b(e).length}get length(){const e=this.b(this.c);return new st(this.c-1,e.length)}},nn=class extends K0{constructor(e){super(t=>e[t-1],e.length)}},Rt=class extends ri{constructor(e){super(),this.value=e,this.b=new ni(this.value)}getValueOfRange(e){return this.b.getOffsetRange(e).substring(this.value)}get length(){return this.b.textLength}},er=class Be{static fromStringEdit(t,n){const r=t.replacements.map(s=>Me.fromStringReplacement(s,n));return new Be(r)}static replace(t,n){return new Be([new Me(t,n)])}static delete(t){return new Be([new Me(t,"")])}static insert(t,n){return new Be([new Me(_.fromPositions(t,t),n)])}static fromParallelReplacementsUnsorted(t){const n=t.slice().sort(Ie(r=>r.range,_.compareRangesUsingStarts));return new Be(n)}constructor(t){this.replacements=t,wt(()=>An(t,(n,r)=>n.range.getEndPosition().isBeforeOrEqual(r.range.getStartPosition())))}normalize(){const t=[];for(const n of this.replacements)if(t.length>0&&t[t.length-1].range.getEndPosition().equals(n.range.getStartPosition())){const r=t[t.length-1];t[t.length-1]=new Me(r.range.plusRange(n.range),r.text+n.text)}else n.isEmpty||t.push(n);return new Be(t)}mapPosition(t){let n=0,r=0,s=0;for(const i of this.replacements){const a=i.range.getStartPosition();if(t.isBeforeOrEqual(a))break;const u=i.range.getEndPosition(),o=st.ofText(i.text);if(t.isBefore(u)){const c=new O(a.lineNumber+n,a.column+(a.lineNumber+n===r?s:0)),f=o.addToPosition(c);return rn(c,f)}a.lineNumber+n!==r&&(s=0),n+=o.lineCount-(i.range.endLineNumber-i.range.startLineNumber),o.lineCount===0?u.lineNumber!==a.lineNumber?s+=o.columnCount-(u.column-1):s+=o.columnCount-(u.column-a.column):s=o.columnCount,r=u.lineNumber+n}return new O(t.lineNumber+n,t.column+(t.lineNumber+n===r?s:0))}mapRange(t){function n(a){return a instanceof O?a:a.getStartPosition()}function r(a){return a instanceof O?a:a.getEndPosition()}const s=n(this.mapPosition(t.getStartPosition())),i=r(this.mapPosition(t.getEndPosition()));return rn(s,i)}inverseMapPosition(t,n){return this.inverse(n).mapPosition(t)}inverseMapRange(t,n){return this.inverse(n).mapRange(t)}apply(t){let n="",r=new O(1,1);for(const i of this.replacements){const a=i.range,u=a.getStartPosition(),o=a.getEndPosition(),c=rn(r,u);c.isEmpty()||(n+=t.getValueOfRange(c)),n+=i.text,r=o}const s=rn(r,t.endPositionExclusive);return s.isEmpty()||(n+=t.getValueOfRange(s)),n}applyToString(t){const n=new Rt(t);return this.apply(n)}inverse(t){const n=this.getNewRanges();return new Be(this.replacements.map((r,s)=>new Me(n[s],t.getValueOfRange(r.range))))}getNewRanges(){const t=[];let n=0,r=0,s=0;for(const i of this.replacements){const a=st.ofText(i.text),u=O.lift({lineNumber:i.range.startLineNumber+r,column:i.range.startColumn+(i.range.startLineNumber===n?s:0)}),o=a.createRange(u);t.push(o),r=o.endLineNumber-i.range.endLineNumber,s=o.endColumn-i.range.endColumn,n=i.range.endLineNumber}return t}toReplacement(t){if(this.replacements.length===0)throw new K;if(this.replacements.length===1)return this.replacements[0];const n=this.replacements[0].range.getStartPosition(),r=this.replacements[this.replacements.length-1].range.getEndPosition();let s="";for(let i=0;i<this.replacements.length;i++){const a=this.replacements[i];if(s+=a.text,i<this.replacements.length-1){const u=this.replacements[i+1],o=_.fromPositions(a.range.getEndPosition(),u.range.getStartPosition()),c=t.getValueOfRange(o);s+=c}}return new Me(_.fromPositions(n,r),s)}equals(t){return vr(this.replacements,t.replacements,(n,r)=>n.equals(r))}toString(t){return t===void 0?this.replacements.map(n=>n.toString()).join(`
`):typeof t=="string"?this.toString(new Rt(t)):this.replacements.length===0?"":this.replacements.map(n=>{const s=t.getValueOfRange(n.range),i=_.fromPositions(new O(Math.max(1,n.range.startLineNumber-1),1),n.range.getStartPosition());let a=t.getValueOfRange(i);a.length>10&&(a="..."+a.substring(a.length-10));const u=_.fromPositions(n.range.getEndPosition(),new O(n.range.endLineNumber+1,1));let o=t.getValueOfRange(u);o.length>10&&(o=o.substring(0,10)+"...");let c=s;if(c.length>10){const h=Math.floor(5);c=c.substring(0,h)+"..."+c.substring(c.length-h)}let f=n.text;if(f.length>10){const h=Math.floor(5);f=f.substring(0,h)+"..."+f.substring(f.length-h)}return c.length===0?`${a}\u2770${f}\u2771${o}`:`${a}\u2770${c}\u21A6${f}\u2771${o}`}).join(`
`)}},Me=class Ve{static joinReplacements(t,n){if(t.length===0)throw new K;if(t.length===1)return t[0];const r=t[0].range.getStartPosition(),s=t[t.length-1].range.getEndPosition();let i="";for(let a=0;a<t.length;a++){const u=t[a];if(i+=u.text,a<t.length-1){const o=t[a+1],c=_.fromPositions(u.range.getEndPosition(),o.range.getStartPosition()),f=n.getValueOfRange(c);i+=f}}return new Ve(_.fromPositions(r,s),i)}static fromStringReplacement(t,n){return new Ve(n.getTransformer().getRange(t.replaceRange),t.newText)}static delete(t){return new Ve(t,"")}constructor(t,n){this.range=t,this.text=n}get isEmpty(){return this.range.isEmpty()&&this.text.length===0}static equals(t,n){return t.range.equalsRange(n.range)&&t.text===n.text}toSingleEditOperation(){return{range:this.range,text:this.text}}toEdit(){return new er([this])}equals(t){return Ve.equals(this,t)}extendToCoverRange(t,n){if(this.range.containsRange(t))return this;const r=this.range.plusRange(t),s=n.getValueOfRange(_.fromPositions(r.getStartPosition(),this.range.getStartPosition())),i=n.getValueOfRange(_.fromPositions(this.range.getEndPosition(),r.getEndPosition())),a=s+this.text+i;return new Ve(r,a)}extendToFullLine(t){const n=new _(this.range.startLineNumber,1,this.range.endLineNumber,t.getTransformer().getLineLength(this.range.endLineNumber)+1);return this.extendToCoverRange(n,t)}removeCommonPrefixAndSuffix(t){return this.removeCommonPrefix(t).removeCommonSuffix(t)}removeCommonPrefix(t){const n=t.getValueOfRange(this.range).replaceAll(`\r
`,`
`),r=this.text.replaceAll(`\r
`,`
`),s=zt(n,r),i=st.ofText(n.substring(0,s)).addToPosition(this.range.getStartPosition()),a=r.substring(s),u=_.fromPositions(i,this.range.getEndPosition());return new Ve(u,a)}removeCommonSuffix(t){const n=t.getValueOfRange(this.range).replaceAll(`\r
`,`
`),r=this.text.replaceAll(`\r
`,`
`),s=Ct(n,r),i=st.ofText(n.substring(0,n.length-s)).addToPosition(this.range.getStartPosition()),a=r.substring(0,r.length-s),u=_.fromPositions(this.range.getStartPosition(),i);return new Ve(u,a)}isEffectiveDeletion(t){let n=this.text.replaceAll(`\r
`,`
`),r=t.getValueOfRange(this.range).replaceAll(`\r
`,`
`);const s=zt(n,r);n=n.substring(s),r=r.substring(s);const i=Ct(n,r);return n=n.substring(0,n.length-i),r=r.substring(0,r.length-i),n===""}toString(){const t=this.range.getStartPosition(),n=this.range.getEndPosition();return`(${t.lineNumber},${t.column} -> ${n.lineNumber},${n.column}): "${this.text}"`}};function rn(e,t){if(e.lineNumber===t.lineNumber&&e.column===Number.MAX_SAFE_INTEGER)return _.fromPositions(t,t);if(!e.isBeforeOrEqual(t))throw new K("start must be before end");return new _(e.lineNumber,e.column,t.lineNumber,t.column)}var it=class ft{static inverse(t,n,r){const s=[];let i=1,a=1;for(const o of t){const c=new ft(new W(i,o.original.startLineNumber),new W(a,o.modified.startLineNumber));c.modified.isEmpty||s.push(c),i=o.original.endLineNumberExclusive,a=o.modified.endLineNumberExclusive}const u=new ft(new W(i,n+1),new W(a,r+1));return u.modified.isEmpty||s.push(u),s}static clip(t,n,r){const s=[];for(const i of t){const a=i.original.intersect(n),u=i.modified.intersect(r);a&&!a.isEmpty&&u&&!u.isEmpty&&s.push(new ft(a,u))}return s}constructor(t,n){this.original=t,this.modified=n}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new ft(this.modified,this.original)}join(t){return new ft(this.original.join(t.original),this.modified.join(t.modified))}get changedLineCount(){return Math.max(this.original.length,this.modified.length)}toRangeMapping(){const t=this.original.toInclusiveRange(),n=this.modified.toInclusiveRange();if(t&&n)return new Ne(t,n);if(this.original.startLineNumber===1||this.modified.startLineNumber===1){if(!(this.modified.startLineNumber===1&&this.original.startLineNumber===1))throw new K("not a valid diff");return new Ne(new _(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new _(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1))}else return new Ne(new _(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),new _(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER))}toRangeMapping2(t,n){if(si(this.original.endLineNumberExclusive,t)&&si(this.modified.endLineNumberExclusive,n))return new Ne(new _(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new _(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1));if(!this.original.isEmpty&&!this.modified.isEmpty)return new Ne(_.fromPositions(new O(this.original.startLineNumber,1),at(new O(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),t)),_.fromPositions(new O(this.modified.startLineNumber,1),at(new O(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),n)));if(this.original.startLineNumber>1&&this.modified.startLineNumber>1)return new Ne(_.fromPositions(at(new O(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER),t),at(new O(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),t)),_.fromPositions(at(new O(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER),n),at(new O(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),n)));throw new K}};function at(e,t){if(e.lineNumber<1)return new O(1,1);if(e.lineNumber>t.length)return new O(t.length,t[t.length-1].length+1);const n=t[e.lineNumber-1];return e.column>n.length+1?new O(e.lineNumber,n.length+1):e}function si(e,t){return e>=1&&e<=t.length}var lt=class mn extends it{static toTextEdit(t,n){const r=[];for(const s of t)for(const i of s.innerChanges??[]){const a=i.toTextEdit(n);r.push(a)}return new er(r)}static fromRangeMappings(t){const n=W.join(t.map(s=>W.fromRangeInclusive(s.originalRange))),r=W.join(t.map(s=>W.fromRangeInclusive(s.modifiedRange)));return new mn(n,r,t)}constructor(t,n,r){super(t,n),this.innerChanges=r}flip(){return new mn(this.modified,this.original,this.innerChanges?.map(t=>t.flip()))}withInnerChangesFromLineRanges(){return new mn(this.original,this.modified,[this.toRangeMapping()])}},Ne=class gt{static fromEdit(t){const n=t.getNewRanges();return t.replacements.map((s,i)=>new gt(s.range,n[i]))}static fromEditJoin(t){const n=t.getNewRanges(),r=t.replacements.map((s,i)=>new gt(s.range,n[i]));return gt.join(r)}static join(t){if(t.length===0)throw new K("Cannot join an empty list of range mappings");let n=t[0];for(let r=1;r<t.length;r++)n=n.join(t[r]);return n}static assertSorted(t){for(let n=1;n<t.length;n++){const r=t[n-1],s=t[n];if(!(r.originalRange.getEndPosition().isBeforeOrEqual(s.originalRange.getStartPosition())&&r.modifiedRange.getEndPosition().isBeforeOrEqual(s.modifiedRange.getStartPosition())))throw new K("Range mappings must be sorted")}}constructor(t,n){this.originalRange=t,this.modifiedRange=n}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new gt(this.modifiedRange,this.originalRange)}toTextEdit(t){const n=t.getValueOfRange(this.modifiedRange);return new Me(this.originalRange,n)}join(t){return new gt(this.originalRange.plusRange(t.originalRange),this.modifiedRange.plusRange(t.modifiedRange))}};function ii(e,t,n,r=!1){const s=[];for(const i of ga(e.map(a=>eu(a,t,n)),(a,u)=>a.original.intersectsOrTouches(u.original)||a.modified.intersectsOrTouches(u.modified))){const a=i[0],u=i[i.length-1];s.push(new lt(a.original.join(u.original),a.modified.join(u.modified),i.map(o=>o.innerChanges[0])))}return wt(()=>!r&&s.length>0&&(s[0].modified.startLineNumber!==s[0].original.startLineNumber||n.length.lineCount-s[s.length-1].modified.endLineNumberExclusive!==t.length.lineCount-s[s.length-1].original.endLineNumberExclusive)?!1:An(s,(i,a)=>a.original.startLineNumber-i.original.endLineNumberExclusive===a.modified.startLineNumber-i.modified.endLineNumberExclusive&&i.original.endLineNumberExclusive<a.original.startLineNumber&&i.modified.endLineNumberExclusive<a.modified.startLineNumber)),s}function eu(e,t,n){let r=0,s=0;e.modifiedRange.endColumn===1&&e.originalRange.endColumn===1&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(s=-1),e.modifiedRange.startColumn-1>=n.getLineLength(e.modifiedRange.startLineNumber)&&e.originalRange.startColumn-1>=t.getLineLength(e.originalRange.startLineNumber)&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+s&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+s&&(r=1);const i=new W(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+s),a=new W(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+s);return new lt(i,a,[e])}var tu=3,nu=class{computeDiff(e,t,n){const s=new ui(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),i=[];let a=null;for(const u of s.changes){let o;u.originalEndLineNumber===0?o=new W(u.originalStartLineNumber+1,u.originalStartLineNumber+1):o=new W(u.originalStartLineNumber,u.originalEndLineNumber+1);let c;u.modifiedEndLineNumber===0?c=new W(u.modifiedStartLineNumber+1,u.modifiedStartLineNumber+1):c=new W(u.modifiedStartLineNumber,u.modifiedEndLineNumber+1);let f=new lt(o,c,u.charChanges?.map(h=>new Ne(new _(h.originalStartLineNumber,h.originalStartColumn,h.originalEndLineNumber,h.originalEndColumn),new _(h.modifiedStartLineNumber,h.modifiedStartColumn,h.modifiedEndLineNumber,h.modifiedEndColumn))));a&&(a.modified.endLineNumberExclusive===f.modified.startLineNumber||a.original.endLineNumberExclusive===f.original.startLineNumber)&&(f=new lt(a.original.join(f.original),a.modified.join(f.modified),a.innerChanges&&f.innerChanges?a.innerChanges.concat(f.innerChanges):void 0),i.pop()),i.push(f),a=f}return wt(()=>An(i,(u,o)=>o.original.startLineNumber-u.original.endLineNumberExclusive===o.modified.startLineNumber-u.modified.endLineNumberExclusive&&u.original.endLineNumberExclusive<o.original.startLineNumber&&u.modified.endLineNumberExclusive<o.modified.startLineNumber)),new en(i,[],s.quitEarly)}};function ai(e,t,n,r){return new t1(e,t,n).ComputeDiff(r)}var li=class{constructor(e){const t=[],n=[];for(let r=0,s=e.length;r<s;r++)t[r]=nr(e[r],1),n[r]=rr(e[r],1);this.lines=e,this.a=t,this.b=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this.a[t]-1,this.b[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const r=[],s=[],i=[];let a=0;for(let u=t;u<=n;u++){const o=this.lines[u],c=e?this.a[u]:1,f=e?this.b[u]:o.length+1;for(let h=c;h<f;h++)r[a]=o.charCodeAt(h-1),s[a]=u+1,i[a]=h,a++;!e&&u<n&&(r[a]=10,s[a]=u+1,i[a]=o.length+1,a++)}return new ru(r,s,i)}},ru=class{constructor(e,t,n){this.a=e,this.b=t,this.d=n}toString(){return"["+this.a.map((e,t)=>(e===10?"\\n":String.fromCharCode(e))+`-(${this.b[t]},${this.d[t]})`).join(", ")+"]"}e(e,t){if(e<0||e>=t.length)throw new Error("Illegal index")}getElements(){return this.a}getStartLineNumber(e){return e>0&&e===this.b.length?this.getEndLineNumber(e-1):(this.e(e,this.b),this.b[e])}getEndLineNumber(e){return e===-1?this.getStartLineNumber(e+1):(this.e(e,this.b),this.a[e]===10?this.b[e]+1:this.b[e])}getStartColumn(e){return e>0&&e===this.d.length?this.getEndColumn(e-1):(this.e(e,this.d),this.d[e])}getEndColumn(e){return e===-1?this.getStartColumn(e+1):(this.e(e,this.d),this.a[e]===10?1:this.d[e]+1)}},sn=class Qi{constructor(t,n,r,s,i,a,u,o){this.originalStartLineNumber=t,this.originalStartColumn=n,this.originalEndLineNumber=r,this.originalEndColumn=s,this.modifiedStartLineNumber=i,this.modifiedStartColumn=a,this.modifiedEndLineNumber=u,this.modifiedEndColumn=o}static createFromDiffChange(t,n,r){const s=n.getStartLineNumber(t.originalStart),i=n.getStartColumn(t.originalStart),a=n.getEndLineNumber(t.originalStart+t.originalLength-1),u=n.getEndColumn(t.originalStart+t.originalLength-1),o=r.getStartLineNumber(t.modifiedStart),c=r.getStartColumn(t.modifiedStart),f=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),h=r.getEndColumn(t.modifiedStart+t.modifiedLength-1);return new Qi(s,i,a,u,o,c,f,h)}};function su(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let r=1,s=e.length;r<s;r++){const i=e[r],a=i.originalStart-(n.originalStart+n.originalLength),u=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(a,u)<tu?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(t.push(i),n=i)}return t}var tr=class Ki{constructor(t,n,r,s,i){this.originalStartLineNumber=t,this.originalEndLineNumber=n,this.modifiedStartLineNumber=r,this.modifiedEndLineNumber=s,this.charChanges=i}static createFromDiffResult(t,n,r,s,i,a,u){let o,c,f,h,g;if(n.originalLength===0?(o=r.getStartLineNumber(n.originalStart)-1,c=0):(o=r.getStartLineNumber(n.originalStart),c=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(f=s.getStartLineNumber(n.modifiedStart)-1,h=0):(f=s.getStartLineNumber(n.modifiedStart),h=s.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),a&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&i()){const m=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),d=s.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);if(m.getElements().length>0&&d.getElements().length>0){let p=ai(m,d,i,!0).changes;u&&(p=su(p)),g=[];for(let v=0,w=p.length;v<w;v++)g.push(sn.createFromDiffChange(p[v],m,d))}}return new Ki(o,c,f,h,g)}},ui=class{constructor(e,t,n){this.a=n.shouldComputeCharChanges,this.b=n.shouldPostProcessCharChanges,this.d=n.shouldIgnoreTrimWhitespace,this.e=n.shouldMakePrettyDiff,this.f=e,this.g=t,this.h=new li(e),this.j=new li(t),this.k=oi(n.maxComputationTime),this.l=oi(n.maxComputationTime===0?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(this.h.lines.length===1&&this.h.lines[0].length===0)return this.j.lines.length===1&&this.j.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.j.lines.length,charChanges:void 0}]};if(this.j.lines.length===1&&this.j.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.h.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const e=ai(this.h,this.j,this.k,this.e),t=e.changes,n=e.quitEarly;if(this.d){const a=[];for(let u=0,o=t.length;u<o;u++)a.push(tr.createFromDiffResult(this.d,t[u],this.h,this.j,this.l,this.a,this.b));return{quitEarly:n,changes:a}}const r=[];let s=0,i=0;for(let a=-1,u=t.length;a<u;a++){const o=a+1<u?t[a+1]:null,c=o?o.originalStart:this.f.length,f=o?o.modifiedStart:this.g.length;for(;s<c&&i<f;){const h=this.f[s],g=this.g[i];if(h!==g){{let m=nr(h,1),d=nr(g,1);for(;m>1&&d>1;){const p=h.charCodeAt(m-2),v=g.charCodeAt(d-2);if(p!==v)break;m--,d--}(m>1||d>1)&&this.m(r,s+1,1,m,i+1,1,d)}{let m=rr(h,1),d=rr(g,1);const p=h.length+1,v=g.length+1;for(;m<p&&d<v;){const w=h.charCodeAt(m-1),A=h.charCodeAt(d-1);if(w!==A)break;m++,d++}(m<p||d<v)&&this.m(r,s+1,m,p,i+1,d,v)}}s++,i++}o&&(r.push(tr.createFromDiffResult(this.d,o,this.h,this.j,this.l,this.a,this.b)),s+=o.originalLength,i+=o.modifiedLength)}return{quitEarly:n,changes:r}}m(e,t,n,r,s,i,a){if(this.n(e,t,n,r,s,i,a))return;let u;this.a&&(u=[new sn(t,n,t,r,s,i,s,a)]),e.push(new tr(t,t,s,s,u))}n(e,t,n,r,s,i,a){const u=e.length;if(u===0)return!1;const o=e[u-1];return o.originalEndLineNumber===0||o.modifiedEndLineNumber===0?!1:o.originalEndLineNumber===t&&o.modifiedEndLineNumber===s?(this.a&&o.charChanges&&o.charChanges.push(new sn(t,n,t,r,s,i,s,a)),!0):o.originalEndLineNumber+1===t&&o.modifiedEndLineNumber+1===s?(o.originalEndLineNumber=t,o.modifiedEndLineNumber=s,this.a&&o.charChanges&&o.charChanges.push(new sn(t,n,t,r,s,i,s,a)),!0):!1}};function nr(e,t){const n=gl(e);return n===-1?t:n+1}function rr(e,t){const n=ml(e);return n===-1?t:n+2}function oi(e){if(e===0)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}var ut=class gr{static trivial(t,n){return new gr([new ee(V.ofLength(t.length),V.ofLength(n.length))],!1)}static trivialTimedOut(t,n){return new gr([new ee(V.ofLength(t.length),V.ofLength(n.length))],!0)}constructor(t,n){this.diffs=t,this.hitTimeout=n}},ee=class ke{static invert(t,n){const r=[];return ma(t,(s,i)=>{r.push(ke.fromOffsetPairs(s?s.getEndExclusives():We.zero,i?i.getStarts():new We(n,(s?s.seq2Range.endExclusive-s.seq1Range.endExclusive:0)+n)))}),r}static fromOffsetPairs(t,n){return new ke(new V(t.offset1,n.offset1),new V(t.offset2,n.offset2))}static assertSorted(t){let n;for(const r of t){if(n&&!(n.seq1Range.endExclusive<=r.seq1Range.start&&n.seq2Range.endExclusive<=r.seq2Range.start))throw new K("Sequence diffs must be sorted");n=r}}constructor(t,n){this.seq1Range=t,this.seq2Range=n}swap(){return new ke(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(t){return new ke(this.seq1Range.join(t.seq1Range),this.seq2Range.join(t.seq2Range))}delta(t){return t===0?this:new ke(this.seq1Range.delta(t),this.seq2Range.delta(t))}deltaStart(t){return t===0?this:new ke(this.seq1Range.deltaStart(t),this.seq2Range.deltaStart(t))}deltaEnd(t){return t===0?this:new ke(this.seq1Range.deltaEnd(t),this.seq2Range.deltaEnd(t))}intersectsOrTouches(t){return this.seq1Range.intersectsOrTouches(t.seq1Range)||this.seq2Range.intersectsOrTouches(t.seq2Range)}intersect(t){const n=this.seq1Range.intersect(t.seq1Range),r=this.seq2Range.intersect(t.seq2Range);if(!(!n||!r))return new ke(n,r)}getStarts(){return new We(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new We(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}},We=class dn{static{this.zero=new dn(0,0)}static{this.max=new dn(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER)}constructor(t,n){this.offset1=t,this.offset2=n}toString(){return`${this.offset1} <-> ${this.offset2}`}delta(t){return t===0?this:new dn(this.offset1+t,this.offset2+t)}equals(t){return this.offset1===t.offset1&&this.offset2===t.offset2}},sr=class ea{static{this.instance=new ea}isValid(){return!0}},iu=class{constructor(e){if(this.e=e,this.c=Date.now(),this.d=!0,e<=0)throw new K("timeout must be positive")}isValid(){return!(Date.now()-this.c<this.e)&&this.d&&(this.d=!1),this.d}disable(){this.e=Number.MAX_SAFE_INTEGER,this.isValid=()=>!0,this.d=!0}},ir=class{constructor(e,t){this.width=e,this.height=t,this.a=[],this.a=new Array(e*t)}get(e,t){return this.a[e+t*this.width]}set(e,t,n){this.a[e+t*this.width]=n}};function ar(e){return e===32||e===9}var ci=class mr{static{this.a=new Map}static b(t){let n=this.a.get(t);return n===void 0&&(n=this.a.size,this.a.set(t,n)),n}constructor(t,n,r){this.range=t,this.lines=n,this.source=r,this.d=[];let s=0;for(let i=t.startLineNumber-1;i<t.endLineNumberExclusive-1;i++){const a=n[i];for(let o=0;o<a.length;o++){s++;const c=a[o],f=mr.b(c);this.d[f]=(this.d[f]||0)+1}s++;const u=mr.b(`
`);this.d[u]=(this.d[u]||0)+1}this.c=s}computeSimilarity(t){let n=0;const r=Math.max(this.d.length,t.d.length);for(let s=0;s<r;s++)n+=Math.abs((this.d[s]??0)-(t.d[s]??0));return 1-n/(this.c+t.c)}},au=class{compute(e,t,n=sr.instance,r){if(e.length===0||t.length===0)return ut.trivial(e,t);const s=new ir(e.length,t.length),i=new ir(e.length,t.length),a=new ir(e.length,t.length);for(let m=0;m<e.length;m++)for(let d=0;d<t.length;d++){if(!n.isValid())return ut.trivialTimedOut(e,t);const p=m===0?0:s.get(m-1,d),v=d===0?0:s.get(m,d-1);let w;e.getElement(m)===t.getElement(d)?(m===0||d===0?w=0:w=s.get(m-1,d-1),m>0&&d>0&&i.get(m-1,d-1)===3&&(w+=a.get(m-1,d-1)),w+=r?r(m,d):1):w=-1;const A=Math.max(p,v,w);if(A===w){const N=m>0&&d>0?a.get(m-1,d-1):0;a.set(m,d,N+1),i.set(m,d,3)}else A===p?(a.set(m,d,0),i.set(m,d,1)):A===v&&(a.set(m,d,0),i.set(m,d,2));s.set(m,d,A)}const u=[];let o=e.length,c=t.length;function f(m,d){(m+1!==o||d+1!==c)&&u.push(new ee(new V(m+1,o),new V(d+1,c))),o=m,c=d}let h=e.length-1,g=t.length-1;for(;h>=0&&g>=0;)i.get(h,g)===3?(f(h,g),h--,g--):i.get(h,g)===1?h--:g--;return f(-1,-1),u.reverse(),new ut(u,!1)}},hi=class{compute(e,t,n=sr.instance){if(e.length===0||t.length===0)return ut.trivial(e,t);const r=e,s=t;function i(d,p){for(;d<r.length&&p<s.length&&r.getElement(d)===s.getElement(p);)d++,p++;return d}let a=0;const u=new lu;u.set(0,i(0,0));const o=new uu;o.set(0,u.get(0)===0?null:new fi(null,0,0,u.get(0)));let c=0;e:for(;;){if(a++,!n.isValid())return ut.trivialTimedOut(r,s);const d=-Math.min(a,s.length+a%2),p=Math.min(a,r.length+a%2);for(c=d;c<=p;c+=2){let v=0;const w=c===p?-1:u.get(c+1),A=c===d?-1:u.get(c-1)+1;v++;const N=Math.min(Math.max(w,A),r.length),x=N-c;if(v++,N>r.length||x>s.length)continue;const k=i(N,x);u.set(c,k);const M=N===w?o.get(c+1):o.get(c-1);if(o.set(c,k!==N?new fi(M,N,x,k-N):M),u.get(c)===r.length&&u.get(c)-c===s.length)break e}}let f=o.get(c);const h=[];let g=r.length,m=s.length;for(;;){const d=f?f.x+f.length:0,p=f?f.y+f.length:0;if((d!==g||p!==m)&&h.push(new ee(new V(d,g),new V(p,m))),!f)break;g=f.x,m=f.y,f=f.prev}return h.reverse(),new ut(h,!1)}},fi=class{constructor(e,t,n,r){this.prev=e,this.x=t,this.y=n,this.length=r}},lu=class{constructor(){this.a=new Int32Array(10),this.b=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.b[e]):this.a[e]}set(e,t){if(e<0){if(e=-e-1,e>=this.b.length){const n=this.b;this.b=new Int32Array(n.length*2),this.b.set(n)}this.b[e]=t}else{if(e>=this.a.length){const n=this.a;this.a=new Int32Array(n.length*2),this.a.set(n)}this.a[e]=t}}},uu=class{constructor(){this.a=[],this.b=[]}get(e){return e<0?(e=-e-1,this.b[e]):this.a[e]}set(e,t){e<0?(e=-e-1,this.b[e]=t):this.a[e]=t}},an=class{constructor(e,t,n){this.lines=e,this.g=t,this.considerWhitespaceChanges=n,this.b=[],this.c=[],this.d=[],this.f=[],this.c.push(0);for(let r=this.g.startLineNumber;r<=this.g.endLineNumber;r++){let s=e[r-1],i=0;r===this.g.startLineNumber&&this.g.startColumn>1&&(i=this.g.startColumn-1,s=s.substring(i)),this.d.push(i);let a=0;if(!n){const o=s.trimStart();a=s.length-o.length,s=o.trimEnd()}this.f.push(a);const u=r===this.g.endLineNumber?Math.min(this.g.endColumn-1-i-a,s.length):s.length;for(let o=0;o<u;o++)this.b.push(s.charCodeAt(o));r<this.g.endLineNumber&&(this.b.push(10),this.c.push(this.b.length))}}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new V(0,this.length))}getText(e){return this.b.slice(e.start,e.endExclusive).map(t=>String.fromCharCode(t)).join("")}getElement(e){return this.b[e]}get length(){return this.b.length}getBoundaryScore(e){const t=bi(e>0?this.b[e-1]:-1),n=bi(e<this.b.length?this.b[e]:-1);if(t===7&&n===8)return 0;if(t===8)return 150;let r=0;return t!==n&&(r+=10,t===0&&n===1&&(r+=1)),r+=di(t),r+=di(n),r}translateOffset(e,t="right"){const n=Xe(this.c,s=>s<=e),r=e-this.c[n];return new O(this.g.startLineNumber+n,1+this.d[n]+r+(r===0&&t==="left"?0:this.f[n]))}translateRange(e){const t=this.translateOffset(e.start,"right"),n=this.translateOffset(e.endExclusive,"left");return n.isBefore(t)?_.fromPositions(n,n):_.fromPositions(t,n)}findWordContaining(e){if(e<0||e>=this.b.length||!ot(this.b[e]))return;let t=e;for(;t>0&&ot(this.b[t-1]);)t--;let n=e;for(;n<this.b.length&&ot(this.b[n]);)n++;return new V(t,n)}findSubWordContaining(e){if(e<0||e>=this.b.length||!ot(this.b[e]))return;let t=e;for(;t>0&&ot(this.b[t-1])&&!gi(this.b[t]);)t--;let n=e;for(;n<this.b.length&&ot(this.b[n])&&!gi(this.b[n]);)n++;return new V(t,n)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,t){return this.b[e]===this.b[t]}extendToFullLines(e){const t=Ge(this.c,r=>r<=e.start)??0,n=fa(this.c,r=>e.endExclusive<=r)??this.b.length;return new V(t,n)}};function ot(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}function gi(e){return e>=65&&e<=90}var mi;(function(e){e[e.WordLower=0]="WordLower",e[e.WordUpper=1]="WordUpper",e[e.WordNumber=2]="WordNumber",e[e.End=3]="End",e[e.Other=4]="Other",e[e.Separator=5]="Separator",e[e.Space=6]="Space",e[e.LineBreakCR=7]="LineBreakCR",e[e.LineBreakLF=8]="LineBreakLF"})(mi||(mi={}));var ou={0:0,1:0,2:0,3:10,4:2,5:30,6:3,7:10,8:10};function di(e){return ou[e]}function bi(e){return e===10?8:e===13?7:ar(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:e===-1?3:e===44||e===59?5:4}function cu(e,t,n,r,s,i){let{moves:a,excludedChanges:u}=fu(e,t,n,i);if(!i.isValid())return[];const o=e.filter(f=>!u.has(f)),c=gu(o,r,s,t,n,i);return ba(a,c),a=mu(a),a=a.filter(f=>{const h=f.original.toOffsetRange().slice(t).map(m=>m.trim());return h.join(`
`).length>=15&&hu(h,m=>m.length>=2)>=2}),a=du(e,a),a}function hu(e,t){let n=0;for(const r of e)t(r)&&n++;return n}function fu(e,t,n,r){const s=[],i=e.filter(o=>o.modified.isEmpty&&o.original.length>=3).map(o=>new ci(o.original,t,o)),a=new Set(e.filter(o=>o.original.isEmpty&&o.modified.length>=3).map(o=>new ci(o.modified,n,o))),u=new Set;for(const o of i){let c=-1,f;for(const h of a){const g=o.computeSimilarity(h);g>c&&(c=g,f=h)}if(c>.9&&f&&(a.delete(f),s.push(new it(o.range,f.range)),u.add(o.source),u.add(f.source)),!r.isValid())return{moves:s,excludedChanges:u}}return{moves:s,excludedChanges:u}}function gu(e,t,n,r,s,i){const a=[],u=new xr;for(const g of e)for(let m=g.original.startLineNumber;m<g.original.endLineNumberExclusive-2;m++){const d=`${t[m-1]}:${t[m+1-1]}:${t[m+2-1]}`;u.add(d,{range:new W(m,m+3)})}const o=[];e.sort(Ie(g=>g.modified.startLineNumber,Je));for(const g of e){let m=[];for(let d=g.modified.startLineNumber;d<g.modified.endLineNumberExclusive-2;d++){const p=`${n[d-1]}:${n[d+1-1]}:${n[d+2-1]}`,v=new W(d,d+3),w=[];u.forEach(p,({range:A})=>{for(const x of m)if(x.originalLineRange.endLineNumberExclusive+1===A.endLineNumberExclusive&&x.modifiedLineRange.endLineNumberExclusive+1===v.endLineNumberExclusive){x.originalLineRange=new W(x.originalLineRange.startLineNumber,A.endLineNumberExclusive),x.modifiedLineRange=new W(x.modifiedLineRange.startLineNumber,v.endLineNumberExclusive),w.push(x);return}const N={modifiedLineRange:v,originalLineRange:A};o.push(N),w.push(N)}),m=w}if(!i.isValid())return[]}o.sort(wa(Ie(g=>g.modifiedLineRange.length,Je)));const c=new tn,f=new tn;for(const g of o){const m=g.modifiedLineRange.startLineNumber-g.originalLineRange.startLineNumber,d=c.subtractFrom(g.modifiedLineRange),p=f.subtractFrom(g.originalLineRange).getWithDelta(m),v=d.getIntersection(p);for(const w of v.ranges){if(w.length<3)continue;const A=w,N=w.delta(-m);a.push(new it(N,A)),c.addRange(A),f.addRange(N)}}a.sort(Ie(g=>g.original.startLineNumber,Je));const h=new wr(e);for(let g=0;g<a.length;g++){const m=a[g],d=h.findLastMonotonous(M=>M.original.startLineNumber<=m.original.startLineNumber),p=Ge(e,M=>M.modified.startLineNumber<=m.modified.startLineNumber),v=Math.max(m.original.startLineNumber-d.original.startLineNumber,m.modified.startLineNumber-p.modified.startLineNumber),w=h.findLastMonotonous(M=>M.original.startLineNumber<m.original.endLineNumberExclusive),A=Ge(e,M=>M.modified.startLineNumber<m.modified.endLineNumberExclusive),N=Math.max(w.original.endLineNumberExclusive-m.original.endLineNumberExclusive,A.modified.endLineNumberExclusive-m.modified.endLineNumberExclusive);let x;for(x=0;x<v;x++){const M=m.original.startLineNumber-x-1,L=m.modified.startLineNumber-x-1;if(M>r.length||L>s.length||c.contains(L)||f.contains(M)||!wi(r[M-1],s[L-1],i))break}x>0&&(f.addRange(new W(m.original.startLineNumber-x,m.original.startLineNumber)),c.addRange(new W(m.modified.startLineNumber-x,m.modified.startLineNumber)));let k;for(k=0;k<N;k++){const M=m.original.endLineNumberExclusive+k,L=m.modified.endLineNumberExclusive+k;if(M>r.length||L>s.length||c.contains(L)||f.contains(M)||!wi(r[M-1],s[L-1],i))break}k>0&&(f.addRange(new W(m.original.endLineNumberExclusive,m.original.endLineNumberExclusive+k)),c.addRange(new W(m.modified.endLineNumberExclusive,m.modified.endLineNumberExclusive+k))),(x>0||k>0)&&(a[g]=new it(new W(m.original.startLineNumber-x,m.original.endLineNumberExclusive+k),new W(m.modified.startLineNumber-x,m.modified.endLineNumberExclusive+k)))}return a}function wi(e,t,n){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;const s=new hi().compute(new an([e],new _(1,1,1,e.length),!1),new an([t],new _(1,1,1,t.length),!1),n);let i=0;const a=ee.invert(s.diffs,e.length);for(const f of a)f.seq1Range.forEach(h=>{ar(e.charCodeAt(h))||i++});function u(f){let h=0;for(let g=0;g<e.length;g++)ar(f.charCodeAt(g))||h++;return h}const o=u(e.length>t.length?e:t);return i/o>.6&&o>10}function mu(e){if(e.length===0)return e;e.sort(Ie(n=>n.original.startLineNumber,Je));const t=[e[0]];for(let n=1;n<e.length;n++){const r=t[t.length-1],s=e[n],i=s.original.startLineNumber-r.original.endLineNumberExclusive,a=s.modified.startLineNumber-r.modified.endLineNumberExclusive;if(i>=0&&a>=0&&i+a<=2){t[t.length-1]=r.join(s);continue}t.push(s)}return t}function du(e,t){const n=new wr(e);return t=t.filter(r=>{const s=n.findLastMonotonous(u=>u.original.startLineNumber<r.original.endLineNumberExclusive)||new it(new W(1,1),new W(1,1)),i=Ge(e,u=>u.modified.startLineNumber<r.modified.endLineNumberExclusive);return s!==i}),t}function vi(e,t,n){let r=n;return r=pi(e,t,r),r=pi(e,t,r),r=bu(e,t,r),r}function pi(e,t,n){if(n.length===0)return n;const r=[];r.push(n[0]);for(let i=1;i<n.length;i++){const a=r[r.length-1];let u=n[i];if(u.seq1Range.isEmpty||u.seq2Range.isEmpty){const o=u.seq1Range.start-a.seq1Range.endExclusive;let c;for(c=1;c<=o&&!(e.getElement(u.seq1Range.start-c)!==e.getElement(u.seq1Range.endExclusive-c)||t.getElement(u.seq2Range.start-c)!==t.getElement(u.seq2Range.endExclusive-c));c++);if(c--,c===o){r[r.length-1]=new ee(new V(a.seq1Range.start,u.seq1Range.endExclusive-o),new V(a.seq2Range.start,u.seq2Range.endExclusive-o));continue}u=u.delta(-c)}r.push(u)}const s=[];for(let i=0;i<r.length-1;i++){const a=r[i+1];let u=r[i];if(u.seq1Range.isEmpty||u.seq2Range.isEmpty){const o=a.seq1Range.start-u.seq1Range.endExclusive;let c;for(c=0;c<o&&!(!e.isStronglyEqual(u.seq1Range.start+c,u.seq1Range.endExclusive+c)||!t.isStronglyEqual(u.seq2Range.start+c,u.seq2Range.endExclusive+c));c++);if(c===o){r[i+1]=new ee(new V(u.seq1Range.start+o,a.seq1Range.endExclusive),new V(u.seq2Range.start+o,a.seq2Range.endExclusive));continue}c>0&&(u=u.delta(c))}s.push(u)}return r.length>0&&s.push(r[r.length-1]),s}function bu(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){const s=r>0?n[r-1]:void 0,i=n[r],a=r+1<n.length?n[r+1]:void 0,u=new V(s?s.seq1Range.endExclusive+1:0,a?a.seq1Range.start-1:e.length),o=new V(s?s.seq2Range.endExclusive+1:0,a?a.seq2Range.start-1:t.length);i.seq1Range.isEmpty?n[r]=Li(i,e,t,u,o):i.seq2Range.isEmpty&&(n[r]=Li(i.swap(),t,e,o,u).swap())}return n}function Li(e,t,n,r,s){let a=1;for(;e.seq1Range.start-a>=r.start&&e.seq2Range.start-a>=s.start&&n.isStronglyEqual(e.seq2Range.start-a,e.seq2Range.endExclusive-a)&&a<100;)a++;a--;let u=0;for(;e.seq1Range.start+u<r.endExclusive&&e.seq2Range.endExclusive+u<s.endExclusive&&n.isStronglyEqual(e.seq2Range.start+u,e.seq2Range.endExclusive+u)&&u<100;)u++;if(a===0&&u===0)return e;let o=0,c=-1;for(let f=-a;f<=u;f++){const h=e.seq2Range.start+f,g=e.seq2Range.endExclusive+f,m=e.seq1Range.start+f,d=t.getBoundaryScore(m)+n.getBoundaryScore(h)+n.getBoundaryScore(g);d>c&&(c=d,o=f)}return e.delta(o)}function wu(e,t,n){const r=[];for(const s of n){const i=r[r.length-1];if(!i){r.push(s);continue}s.seq1Range.start-i.seq1Range.endExclusive<=2||s.seq2Range.start-i.seq2Range.endExclusive<=2?r[r.length-1]=new ee(i.seq1Range.join(s.seq1Range),i.seq2Range.join(s.seq2Range)):r.push(s)}return r}function Ni(e,t,n,r,s=!1){const i=ee.invert(n,e.length),a=[];let u=new We(0,0);function o(f,h){if(f.offset1<u.offset1||f.offset2<u.offset2)return;const g=r(e,f.offset1),m=r(t,f.offset2);if(!g||!m)return;let d=new ee(g,m);const p=d.intersect(h);let v=p.seq1Range.length,w=p.seq2Range.length;for(;i.length>0;){const A=i[0];if(!(A.seq1Range.intersects(d.seq1Range)||A.seq2Range.intersects(d.seq2Range)))break;const x=r(e,A.seq1Range.start),k=r(t,A.seq2Range.start),M=new ee(x,k),L=M.intersect(A);if(v+=L.seq1Range.length,w+=L.seq2Range.length,d=d.join(M),d.seq1Range.endExclusive>=A.seq1Range.endExclusive)i.shift();else break}(s&&v+w<d.seq1Range.length+d.seq2Range.length||v+w<(d.seq1Range.length+d.seq2Range.length)*2/3)&&a.push(d),u=d.getEndExclusives()}for(;i.length>0;){const f=i.shift();f.seq1Range.isEmpty||(o(f.getStarts(),f),o(f.getEndExclusives().delta(-1),f))}return vu(n,a)}function vu(e,t){const n=[];for(;e.length>0||t.length>0;){const r=e[0],s=t[0];let i;r&&(!s||r.seq1Range.start<s.seq1Range.start)?i=e.shift():i=t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=i.seq1Range.start?n[n.length-1]=n[n.length-1].join(i):n.push(i)}return n}function pu(e,t,n){let r=n;if(r.length===0)return r;let s=0,i;do{i=!1;const u=[r[0]];for(let o=1;o<r.length;o++){let c=function(m,d){const p=new V(h.seq1Range.endExclusive,f.seq1Range.start);return e.getText(p).replace(/\s/g,"").length<=4&&(m.seq1Range.length+m.seq2Range.length>5||d.seq1Range.length+d.seq2Range.length>5)};var a=c;const f=r[o],h=u[u.length-1];c(h,f)?(i=!0,u[u.length-1]=u[u.length-1].join(f)):u.push(f)}r=u}while(s++<10&&i);return r}function Lu(e,t,n){let r=n;if(r.length===0)return r;let s=0,i;do{i=!1;const o=[r[0]];for(let c=1;c<r.length;c++){let f=function(d,p){const v=new V(g.seq1Range.endExclusive,h.seq1Range.start);if(e.countLinesIn(v)>5||v.length>500)return!1;const A=e.getText(v).trim();if(A.length>20||A.split(/\r\n|\r|\n/).length>1)return!1;const N=e.countLinesIn(d.seq1Range),x=d.seq1Range.length,k=t.countLinesIn(d.seq2Range),M=d.seq2Range.length,L=e.countLinesIn(p.seq1Range),R=p.seq1Range.length,F=t.countLinesIn(p.seq2Range),U=p.seq2Range.length,j=2*40+50;function B(pe){return Math.min(pe,j)}return Math.pow(Math.pow(B(N*40+x),1.5)+Math.pow(B(k*40+M),1.5),1.5)+Math.pow(Math.pow(B(L*40+R),1.5)+Math.pow(B(F*40+U),1.5),1.5)>(j**1.5)**1.5*1.3};var a=f;const h=r[c],g=o[o.length-1];f(g,h)?(i=!0,o[o.length-1]=o[o.length-1].join(h)):o.push(h)}r=o}while(s++<10&&i);const u=[];return da(r,(o,c,f)=>{let h=c;function g(A){return A.length>0&&A.trim().length<=3&&c.seq1Range.length+c.seq2Range.length>100}const m=e.extendToFullLines(c.seq1Range),d=e.getText(new V(m.start,c.seq1Range.start));g(d)&&(h=h.deltaStart(-d.length));const p=e.getText(new V(c.seq1Range.endExclusive,m.endExclusive));g(p)&&(h=h.deltaEnd(p.length));const v=ee.fromOffsetPairs(o?o.getEndExclusives():We.zero,f?f.getStarts():We.max),w=h.intersect(v);u.length>0&&w.getStarts().equals(u[u.length-1].getEndExclusives())?u[u.length-1]=u[u.length-1].join(w):u.push(w)}),u}var Ri=class{constructor(e,t){this.a=e,this.b=t}getElement(e){return this.a[e]}get length(){return this.a.length}getBoundaryScore(e){const t=e===0?0:Ai(this.b[e-1]),n=e===this.b.length?0:Ai(this.b[e]);return 1e3-(t+n)}getText(e){return this.b.slice(e.start,e.endExclusive).join(`
`)}isStronglyEqual(e,t){return this.b[e]===this.b[t]}};function Ai(e){let t=0;for(;t<e.length&&(e.charCodeAt(t)===32||e.charCodeAt(t)===9);)t++;return t}var Nu=class{constructor(){this.e=new au,this.f=new hi}computeDiff(e,t,n){if(e.length<=1&&vr(e,t,(M,L)=>M===L))return new en([],[],!1);if(e.length===1&&e[0].length===0||t.length===1&&t[0].length===0)return new en([new lt(new W(1,e.length+1),new W(1,t.length+1),[new Ne(new _(1,1,e.length,e[e.length-1].length+1),new _(1,1,t.length,t[t.length-1].length+1))])],[],!1);const r=n.maxComputationTimeMs===0?sr.instance:new iu(n.maxComputationTimeMs),s=!n.ignoreTrimWhitespace,i=new Map;function a(M){let L=i.get(M);return L===void 0&&(L=i.size,i.set(M,L)),L}const u=e.map(M=>a(M.trim())),o=t.map(M=>a(M.trim())),c=new Ri(u,e),f=new Ri(o,t),h=c.length+f.length<1700?this.e.compute(c,f,r,(M,L)=>e[M]===t[L]?t[L].length===0?.1:1+Math.log(1+t[L].length):.99):this.f.compute(c,f,r);let g=h.diffs,m=h.hitTimeout;g=vi(c,f,g),g=pu(c,f,g);const d=[],p=M=>{if(s)for(let L=0;L<M;L++){const R=v+L,F=w+L;if(e[R]!==t[F]){const U=this.h(e,t,new ee(new V(R,R+1),new V(F,F+1)),r,s,n);for(const j of U.mappings)d.push(j);U.hitTimeout&&(m=!0)}}};let v=0,w=0;for(const M of g){wt(()=>M.seq1Range.start-v===M.seq2Range.start-w);const L=M.seq1Range.start-v;p(L),v=M.seq1Range.endExclusive,w=M.seq2Range.endExclusive;const R=this.h(e,t,M,r,s,n);R.hitTimeout&&(m=!0);for(const F of R.mappings)d.push(F)}p(e.length-v);const A=new nn(e),N=new nn(t),x=ii(d,A,N);let k=[];return n.computeMoves&&(k=this.g(x,e,t,u,o,r,s,n)),wt(()=>{function M(R,F){if(R.lineNumber<1||R.lineNumber>F.length)return!1;const U=F[R.lineNumber-1];return!(R.column<1||R.column>U.length+1)}function L(R,F){return!(R.startLineNumber<1||R.startLineNumber>F.length+1||R.endLineNumberExclusive<1||R.endLineNumberExclusive>F.length+1)}for(const R of x){if(!R.innerChanges)return!1;for(const F of R.innerChanges)if(!(M(F.modifiedRange.getStartPosition(),t)&&M(F.modifiedRange.getEndPosition(),t)&&M(F.originalRange.getStartPosition(),e)&&M(F.originalRange.getEndPosition(),e)))return!1;if(!L(R.modified,t)||!L(R.original,e))return!1}return!0}),new en(x,k,m)}g(e,t,n,r,s,i,a,u){return cu(e,t,n,r,s,i).map(f=>{const h=this.h(t,n,new ee(f.original.toOffsetRange(),f.modified.toOffsetRange()),i,a,u),g=ii(h.mappings,new nn(t),new nn(n),!0);return new Y0(f,g)})}h(e,t,n,r,s,i){const u=Ru(n).toRangeMapping2(e,t),o=new an(e,u.originalRange,s),c=new an(t,u.modifiedRange,s),f=o.length+c.length<500?this.e.compute(o,c,r):this.f.compute(o,c,r),h=!1;let g=f.diffs;h&&ee.assertSorted(g),g=vi(o,c,g),h&&ee.assertSorted(g),g=Ni(o,c,g,(d,p)=>d.findWordContaining(p)),h&&ee.assertSorted(g),i.extendToSubwords&&(g=Ni(o,c,g,(d,p)=>d.findSubWordContaining(p),!0),h&&ee.assertSorted(g)),g=wu(o,c,g),h&&ee.assertSorted(g),g=Lu(o,c,g),h&&ee.assertSorted(g);const m=g.map(d=>new Ne(o.translateRange(d.seq1Range),c.translateRange(d.seq2Range)));return h&&Ne.assertSorted(m),{mappings:m,hitTimeout:f.hitTimeout}}};function Ru(e){return new it(new W(e.seq1Range.start+1,e.seq1Range.endExclusive+1),new W(e.seq2Range.start+1,e.seq2Range.endExclusive+1))}var At={getLegacy:()=>new nu,getDefault:()=>new Nu};function _e(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var b=class{constructor(e,t,n,r=1){this._rgbaBrand=void 0,this.r=Math.min(255,Math.max(0,e))|0,this.g=Math.min(255,Math.max(0,t))|0,this.b=Math.min(255,Math.max(0,n))|0,this.a=_e(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}},He=class Pt{constructor(t,n,r,s){this._hslaBrand=void 0,this.h=Math.max(Math.min(360,t),0)|0,this.s=_e(Math.max(Math.min(1,n),0),3),this.l=_e(Math.max(Math.min(1,r),0),3),this.a=_e(Math.max(Math.min(1,s),0),3)}static equals(t,n){return t.h===n.h&&t.s===n.s&&t.l===n.l&&t.a===n.a}static fromRGBA(t){const n=t.r/255,r=t.g/255,s=t.b/255,i=t.a,a=Math.max(n,r,s),u=Math.min(n,r,s);let o=0,c=0;const f=(u+a)/2,h=a-u;if(h>0){switch(c=Math.min(f<=.5?h/(2*f):h/(2-2*f),1),a){case n:o=(r-s)/h+(r<s?6:0);break;case r:o=(s-n)/h+2;break;case s:o=(n-r)/h+4;break}o*=60,o=Math.round(o)}return new Pt(o,c,f,i)}static i(t,n,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+(n-t)*6*r:r<1/2?n:r<2/3?t+(n-t)*(2/3-r)*6:t}static toRGBA(t){const n=t.h/360,{s:r,l:s,a:i}=t;let a,u,o;if(r===0)a=u=o=s;else{const c=s<.5?s*(1+r):s+r-s*r,f=2*s-c;a=Pt.i(f,c,n+1/3),u=Pt.i(f,c,n),o=Pt.i(f,c,n-1/3)}return new b(Math.round(a*255),Math.round(u*255),Math.round(o*255),i)}},ln=class ta{constructor(t,n,r,s){this._hsvaBrand=void 0,this.h=Math.max(Math.min(360,t),0)|0,this.s=_e(Math.max(Math.min(1,n),0),3),this.v=_e(Math.max(Math.min(1,r),0),3),this.a=_e(Math.max(Math.min(1,s),0),3)}static equals(t,n){return t.h===n.h&&t.s===n.s&&t.v===n.v&&t.a===n.a}static fromRGBA(t){const n=t.r/255,r=t.g/255,s=t.b/255,i=Math.max(n,r,s),a=Math.min(n,r,s),u=i-a,o=i===0?0:u/i;let c;return u===0?c=0:i===n?c=((r-s)/u%6+6)%6:i===r?c=(s-n)/u+2:c=(n-r)/u+4,new ta(Math.round(c*60),o,i,t.a)}static toRGBA(t){const{h:n,s:r,v:s,a:i}=t,a=s*r,u=a*(1-Math.abs(n/60%2-1)),o=s-a;let[c,f,h]=[0,0,0];return n<60?(c=a,f=u):n<120?(c=u,f=a):n<180?(f=a,h=u):n<240?(f=u,h=a):n<300?(c=u,h=a):n<=360&&(c=a,h=u),c=Math.round((c+o)*255),f=Math.round((f+o)*255),h=Math.round((h+o)*255),new b(c,f,h,i)}},un=class G{static fromHex(t){return G.Format.CSS.parseHex(t)||G.red}static equals(t,n){return!t&&!n?!0:!t||!n?!1:t.equals(n)}get hsla(){return this.i?this.i:He.fromRGBA(this.rgba)}get hsva(){return this.j?this.j:ln.fromRGBA(this.rgba)}constructor(t){if(t)if(t instanceof b)this.rgba=t;else if(t instanceof He)this.i=t,this.rgba=He.toRGBA(t);else if(t instanceof ln)this.j=t,this.rgba=ln.toRGBA(t);else throw new Error("Invalid color ctor argument");else throw new Error("Color needs a value")}equals(t){return!!t&&b.equals(this.rgba,t.rgba)&&He.equals(this.hsla,t.hsla)&&ln.equals(this.hsva,t.hsva)}getRelativeLuminance(){const t=G.k(this.rgba.r),n=G.k(this.rgba.g),r=G.k(this.rgba.b),s=.2126*t+.7152*n+.0722*r;return _e(s,4)}reduceRelativeLuminace(t,n){let{r,g:s,b:i}=t.rgba,a=this.getContrastRatio(t);for(;a<n&&(r>0||s>0||i>0);)r-=Math.max(0,Math.ceil(r*.1)),s-=Math.max(0,Math.ceil(s*.1)),i-=Math.max(0,Math.ceil(i*.1)),a=this.getContrastRatio(new G(new b(r,s,i)));return new G(new b(r,s,i))}increaseRelativeLuminace(t,n){let{r,g:s,b:i}=t.rgba,a=this.getContrastRatio(t);for(;a<n&&(r<255||s<255||i<255);)r=Math.min(255,r+Math.ceil((255-r)*.1)),s=Math.min(255,s+Math.ceil((255-s)*.1)),i=Math.min(255,i+Math.ceil((255-i)*.1)),a=this.getContrastRatio(new G(new b(r,s,i)));return new G(new b(r,s,i))}static k(t){const n=t/255;return n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4)}getContrastRatio(t){const n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n>r?(n+.05)/(r+.05):(r+.05)/(n+.05)}isDarker(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3<128}isLighter(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3>=128}isLighterThan(t){const n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n>r}isDarkerThan(t){const n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n<r}ensureConstrast(t,n){const r=this.getRelativeLuminance(),s=t.getRelativeLuminance();if(this.getContrastRatio(t)<n){if(s<r){const o=this.reduceRelativeLuminace(t,n),c=this.getContrastRatio(o);if(c<n){const f=this.increaseRelativeLuminace(t,n),h=this.getContrastRatio(f);return c>h?o:f}return o}const a=this.increaseRelativeLuminace(t,n),u=this.getContrastRatio(a);if(u<n){const o=this.reduceRelativeLuminace(t,n),c=this.getContrastRatio(o);return u>c?a:o}return a}return t}lighten(t){return new G(new He(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*t,this.hsla.a))}darken(t){return new G(new He(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*t,this.hsla.a))}transparent(t){const{r:n,g:r,b:s,a:i}=this.rgba;return new G(new b(n,r,s,i*t))}isTransparent(){return this.rgba.a===0}isOpaque(){return this.rgba.a===1}opposite(){return new G(new b(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}blend(t){const n=t.rgba,r=this.rgba.a,s=n.a,i=r+s*(1-r);if(i<1e-6)return G.transparent;const a=this.rgba.r*r/i+n.r*s*(1-r)/i,u=this.rgba.g*r/i+n.g*s*(1-r)/i,o=this.rgba.b*r/i+n.b*s*(1-r)/i;return new G(new b(a,u,o,i))}mix(t,n=.5){const r=Math.min(Math.max(n,0),1),s=this.rgba,i=t.rgba,a=s.r+(i.r-s.r)*r,u=s.g+(i.g-s.g)*r,o=s.b+(i.b-s.b)*r,c=s.a+(i.a-s.a)*r;return new G(new b(a,u,o,c))}makeOpaque(t){if(this.isOpaque()||t.rgba.a!==1)return this;const{r:n,g:r,b:s,a:i}=this.rgba;return new G(new b(t.rgba.r-i*(t.rgba.r-n),t.rgba.g-i*(t.rgba.g-r),t.rgba.b-i*(t.rgba.b-s),1))}flatten(...t){const n=t.reduceRight((r,s)=>G.o(s,r));return G.o(this,n)}static o(t,n){const r=1-t.rgba.a;return new G(new b(r*n.rgba.r+t.rgba.a*t.rgba.r,r*n.rgba.g+t.rgba.a*t.rgba.g,r*n.rgba.b+t.rgba.a*t.rgba.b))}toString(){return this.u||(this.u=G.Format.CSS.format(this)),this.u}toNumber32Bit(){return this.w||(this.w=(this.rgba.r<<24|this.rgba.g<<16|this.rgba.b<<8|this.rgba.a*255<<0)>>>0),this.w}static getLighterColor(t,n,r){if(t.isLighterThan(n))return t;r=r||.5;const s=t.getRelativeLuminance(),i=n.getRelativeLuminance();return r=r*(i-s)/i,t.lighten(r)}static getDarkerColor(t,n,r){if(t.isDarkerThan(n))return t;r=r||.5;const s=t.getRelativeLuminance(),i=n.getRelativeLuminance();return r=r*(s-i)/s,t.darken(r)}static{this.white=new G(new b(255,255,255,1))}static{this.black=new G(new b(0,0,0,1))}static{this.red=new G(new b(255,0,0,1))}static{this.blue=new G(new b(0,0,255,1))}static{this.green=new G(new b(0,255,0,1))}static{this.cyan=new G(new b(0,255,255,1))}static{this.lightgrey=new G(new b(211,211,211,1))}static{this.transparent=new G(new b(0,0,0,0))}};(function(e){let t;(function(n){let r;(function(s){function i(w){return w.rgba.a===1?`rgb(${w.rgba.r}, ${w.rgba.g}, ${w.rgba.b})`:e.Format.CSS.formatRGBA(w)}s.formatRGB=i;function a(w){return`rgba(${w.rgba.r}, ${w.rgba.g}, ${w.rgba.b}, ${+w.rgba.a.toFixed(2)})`}s.formatRGBA=a;function u(w){return w.hsla.a===1?`hsl(${w.hsla.h}, ${Math.round(w.hsla.s*100)}%, ${Math.round(w.hsla.l*100)}%)`:e.Format.CSS.formatHSLA(w)}s.formatHSL=u;function o(w){return`hsla(${w.hsla.h}, ${Math.round(w.hsla.s*100)}%, ${Math.round(w.hsla.l*100)}%, ${w.hsla.a.toFixed(2)})`}s.formatHSLA=o;function c(w){const A=w.toString(16);return A.length!==2?"0"+A:A}function f(w){return`#${c(w.rgba.r)}${c(w.rgba.g)}${c(w.rgba.b)}`}s.formatHex=f;function h(w,A=!1){return A&&w.rgba.a===1?e.Format.CSS.formatHex(w):`#${c(w.rgba.r)}${c(w.rgba.g)}${c(w.rgba.b)}${c(Math.round(w.rgba.a*255))}`}s.formatHexA=h;function g(w){return w.isOpaque()?e.Format.CSS.formatHex(w):e.Format.CSS.formatRGBA(w)}s.format=g;function m(w){if(w==="transparent")return e.transparent;if(w.startsWith("#"))return p(w);if(w.startsWith("rgba(")){const A=w.match(/rgba\((?<r>(?:\+|-)?\d+), *(?<g>(?:\+|-)?\d+), *(?<b>(?:\+|-)?\d+), *(?<a>(?:\+|-)?\d+(\.\d+)?)\)/);if(!A)throw new Error("Invalid color format "+w);const N=parseInt(A.groups?.r??"0"),x=parseInt(A.groups?.g??"0"),k=parseInt(A.groups?.b??"0"),M=parseFloat(A.groups?.a??"0");return new e(new b(N,x,k,M))}if(w.startsWith("rgb(")){const A=w.match(/rgb\((?<r>(?:\+|-)?\d+), *(?<g>(?:\+|-)?\d+), *(?<b>(?:\+|-)?\d+)\)/);if(!A)throw new Error("Invalid color format "+w);const N=parseInt(A.groups?.r??"0"),x=parseInt(A.groups?.g??"0"),k=parseInt(A.groups?.b??"0");return new e(new b(N,x,k))}return d(w)}s.parse=m;function d(w){switch(w){case"aliceblue":return new e(new b(240,248,255,1));case"antiquewhite":return new e(new b(250,235,215,1));case"aqua":return new e(new b(0,255,255,1));case"aquamarine":return new e(new b(127,255,212,1));case"azure":return new e(new b(240,255,255,1));case"beige":return new e(new b(245,245,220,1));case"bisque":return new e(new b(255,228,196,1));case"black":return new e(new b(0,0,0,1));case"blanchedalmond":return new e(new b(255,235,205,1));case"blue":return new e(new b(0,0,255,1));case"blueviolet":return new e(new b(138,43,226,1));case"brown":return new e(new b(165,42,42,1));case"burlywood":return new e(new b(222,184,135,1));case"cadetblue":return new e(new b(95,158,160,1));case"chartreuse":return new e(new b(127,255,0,1));case"chocolate":return new e(new b(210,105,30,1));case"coral":return new e(new b(255,127,80,1));case"cornflowerblue":return new e(new b(100,149,237,1));case"cornsilk":return new e(new b(255,248,220,1));case"crimson":return new e(new b(220,20,60,1));case"cyan":return new e(new b(0,255,255,1));case"darkblue":return new e(new b(0,0,139,1));case"darkcyan":return new e(new b(0,139,139,1));case"darkgoldenrod":return new e(new b(184,134,11,1));case"darkgray":return new e(new b(169,169,169,1));case"darkgreen":return new e(new b(0,100,0,1));case"darkgrey":return new e(new b(169,169,169,1));case"darkkhaki":return new e(new b(189,183,107,1));case"darkmagenta":return new e(new b(139,0,139,1));case"darkolivegreen":return new e(new b(85,107,47,1));case"darkorange":return new e(new b(255,140,0,1));case"darkorchid":return new e(new b(153,50,204,1));case"darkred":return new e(new b(139,0,0,1));case"darksalmon":return new e(new b(233,150,122,1));case"darkseagreen":return new e(new b(143,188,143,1));case"darkslateblue":return new e(new b(72,61,139,1));case"darkslategray":return new e(new b(47,79,79,1));case"darkslategrey":return new e(new b(47,79,79,1));case"darkturquoise":return new e(new b(0,206,209,1));case"darkviolet":return new e(new b(148,0,211,1));case"deeppink":return new e(new b(255,20,147,1));case"deepskyblue":return new e(new b(0,191,255,1));case"dimgray":return new e(new b(105,105,105,1));case"dimgrey":return new e(new b(105,105,105,1));case"dodgerblue":return new e(new b(30,144,255,1));case"firebrick":return new e(new b(178,34,34,1));case"floralwhite":return new e(new b(255,250,240,1));case"forestgreen":return new e(new b(34,139,34,1));case"fuchsia":return new e(new b(255,0,255,1));case"gainsboro":return new e(new b(220,220,220,1));case"ghostwhite":return new e(new b(248,248,255,1));case"gold":return new e(new b(255,215,0,1));case"goldenrod":return new e(new b(218,165,32,1));case"gray":return new e(new b(128,128,128,1));case"green":return new e(new b(0,128,0,1));case"greenyellow":return new e(new b(173,255,47,1));case"grey":return new e(new b(128,128,128,1));case"honeydew":return new e(new b(240,255,240,1));case"hotpink":return new e(new b(255,105,180,1));case"indianred":return new e(new b(205,92,92,1));case"indigo":return new e(new b(75,0,130,1));case"ivory":return new e(new b(255,255,240,1));case"khaki":return new e(new b(240,230,140,1));case"lavender":return new e(new b(230,230,250,1));case"lavenderblush":return new e(new b(255,240,245,1));case"lawngreen":return new e(new b(124,252,0,1));case"lemonchiffon":return new e(new b(255,250,205,1));case"lightblue":return new e(new b(173,216,230,1));case"lightcoral":return new e(new b(240,128,128,1));case"lightcyan":return new e(new b(224,255,255,1));case"lightgoldenrodyellow":return new e(new b(250,250,210,1));case"lightgray":return new e(new b(211,211,211,1));case"lightgreen":return new e(new b(144,238,144,1));case"lightgrey":return new e(new b(211,211,211,1));case"lightpink":return new e(new b(255,182,193,1));case"lightsalmon":return new e(new b(255,160,122,1));case"lightseagreen":return new e(new b(32,178,170,1));case"lightskyblue":return new e(new b(135,206,250,1));case"lightslategray":return new e(new b(119,136,153,1));case"lightslategrey":return new e(new b(119,136,153,1));case"lightsteelblue":return new e(new b(176,196,222,1));case"lightyellow":return new e(new b(255,255,224,1));case"lime":return new e(new b(0,255,0,1));case"limegreen":return new e(new b(50,205,50,1));case"linen":return new e(new b(250,240,230,1));case"magenta":return new e(new b(255,0,255,1));case"maroon":return new e(new b(128,0,0,1));case"mediumaquamarine":return new e(new b(102,205,170,1));case"mediumblue":return new e(new b(0,0,205,1));case"mediumorchid":return new e(new b(186,85,211,1));case"mediumpurple":return new e(new b(147,112,219,1));case"mediumseagreen":return new e(new b(60,179,113,1));case"mediumslateblue":return new e(new b(123,104,238,1));case"mediumspringgreen":return new e(new b(0,250,154,1));case"mediumturquoise":return new e(new b(72,209,204,1));case"mediumvioletred":return new e(new b(199,21,133,1));case"midnightblue":return new e(new b(25,25,112,1));case"mintcream":return new e(new b(245,255,250,1));case"mistyrose":return new e(new b(255,228,225,1));case"moccasin":return new e(new b(255,228,181,1));case"navajowhite":return new e(new b(255,222,173,1));case"navy":return new e(new b(0,0,128,1));case"oldlace":return new e(new b(253,245,230,1));case"olive":return new e(new b(128,128,0,1));case"olivedrab":return new e(new b(107,142,35,1));case"orange":return new e(new b(255,165,0,1));case"orangered":return new e(new b(255,69,0,1));case"orchid":return new e(new b(218,112,214,1));case"palegoldenrod":return new e(new b(238,232,170,1));case"palegreen":return new e(new b(152,251,152,1));case"paleturquoise":return new e(new b(175,238,238,1));case"palevioletred":return new e(new b(219,112,147,1));case"papayawhip":return new e(new b(255,239,213,1));case"peachpuff":return new e(new b(255,218,185,1));case"peru":return new e(new b(205,133,63,1));case"pink":return new e(new b(255,192,203,1));case"plum":return new e(new b(221,160,221,1));case"powderblue":return new e(new b(176,224,230,1));case"purple":return new e(new b(128,0,128,1));case"rebeccapurple":return new e(new b(102,51,153,1));case"red":return new e(new b(255,0,0,1));case"rosybrown":return new e(new b(188,143,143,1));case"royalblue":return new e(new b(65,105,225,1));case"saddlebrown":return new e(new b(139,69,19,1));case"salmon":return new e(new b(250,128,114,1));case"sandybrown":return new e(new b(244,164,96,1));case"seagreen":return new e(new b(46,139,87,1));case"seashell":return new e(new b(255,245,238,1));case"sienna":return new e(new b(160,82,45,1));case"silver":return new e(new b(192,192,192,1));case"skyblue":return new e(new b(135,206,235,1));case"slateblue":return new e(new b(106,90,205,1));case"slategray":return new e(new b(112,128,144,1));case"slategrey":return new e(new b(112,128,144,1));case"snow":return new e(new b(255,250,250,1));case"springgreen":return new e(new b(0,255,127,1));case"steelblue":return new e(new b(70,130,180,1));case"tan":return new e(new b(210,180,140,1));case"teal":return new e(new b(0,128,128,1));case"thistle":return new e(new b(216,191,216,1));case"tomato":return new e(new b(255,99,71,1));case"turquoise":return new e(new b(64,224,208,1));case"violet":return new e(new b(238,130,238,1));case"wheat":return new e(new b(245,222,179,1));case"white":return new e(new b(255,255,255,1));case"whitesmoke":return new e(new b(245,245,245,1));case"yellow":return new e(new b(255,255,0,1));case"yellowgreen":return new e(new b(154,205,50,1));default:return null}}function p(w){const A=w.length;if(A===0||w.charCodeAt(0)!==35)return null;if(A===7){const N=16*v(w.charCodeAt(1))+v(w.charCodeAt(2)),x=16*v(w.charCodeAt(3))+v(w.charCodeAt(4)),k=16*v(w.charCodeAt(5))+v(w.charCodeAt(6));return new e(new b(N,x,k,1))}if(A===9){const N=16*v(w.charCodeAt(1))+v(w.charCodeAt(2)),x=16*v(w.charCodeAt(3))+v(w.charCodeAt(4)),k=16*v(w.charCodeAt(5))+v(w.charCodeAt(6)),M=16*v(w.charCodeAt(7))+v(w.charCodeAt(8));return new e(new b(N,x,k,M/255))}if(A===4){const N=v(w.charCodeAt(1)),x=v(w.charCodeAt(2)),k=v(w.charCodeAt(3));return new e(new b(16*N+N,16*x+x,16*k+k))}if(A===5){const N=v(w.charCodeAt(1)),x=v(w.charCodeAt(2)),k=v(w.charCodeAt(3)),M=v(w.charCodeAt(4));return new e(new b(16*N+N,16*x+x,16*k+k,(16*M+M)/255))}return null}s.parseHex=p;function v(w){switch(w){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}})(r=n.CSS||(n.CSS={}))})(t=e.Format||(e.Format={}))})(un||(un={}));function xi(e){const t=[];for(const n of e){const r=Number(n);(r||r===0&&n.replace(/\s/g,"")!=="")&&t.push(r)}return t}function lr(e,t,n,r){return{red:e/255,blue:n/255,green:t/255,alpha:r}}function xt(e,t){const n=t.index,r=t[0].length;if(n===void 0)return;const s=e.positionAt(n);return{startLineNumber:s.lineNumber,startColumn:s.column,endLineNumber:s.lineNumber,endColumn:s.column+r}}function Au(e,t){if(!e)return;const n=un.Format.CSS.parseHex(t);if(n)return{range:e,color:lr(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}}function Ei(e,t,n){if(!e||t.length!==1)return;const s=t[0].values(),i=xi(s);return{range:e,color:lr(i[0],i[1],i[2],n?i[3]:1)}}function Mi(e,t,n){if(!e||t.length!==1)return;const s=t[0].values(),i=xi(s),a=new un(new He(i[0],i[1]/100,i[2]/100,n?i[3]:1));return{range:e,color:lr(a.rgba.r,a.rgba.g,a.rgba.b,a.rgba.a)}}function Et(e,t){return typeof e=="string"?[...e.matchAll(t)]:e.findMatches(t)}function xu(e){const t=[],r=Et(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|^(#)([A-Fa-f0-9]{3})\b|^(#)([A-Fa-f0-9]{4})\b|^(#)([A-Fa-f0-9]{6})\b|^(#)([A-Fa-f0-9]{8})\b|(?<=['"\s])(#)([A-Fa-f0-9]{3})\b|(?<=['"\s])(#)([A-Fa-f0-9]{4})\b|(?<=['"\s])(#)([A-Fa-f0-9]{6})\b|(?<=['"\s])(#)([A-Fa-f0-9]{8})\b/gm);if(r.length>0)for(const s of r){const i=s.filter(c=>c!==void 0),a=i[1],u=i[2];if(!u)continue;let o;if(a==="rgb"){const c=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;o=Ei(xt(e,s),Et(u,c),!1)}else if(a==="rgba"){const c=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;o=Ei(xt(e,s),Et(u,c),!0)}else if(a==="hsl"){const c=/^\(\s*((?:360(?:\.0+)?|(?:36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])(?:\.\d+)?))\s*[\s,]\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*[\s,]\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;o=Mi(xt(e,s),Et(u,c),!1)}else if(a==="hsla"){const c=/^\(\s*((?:360(?:\.0+)?|(?:36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])(?:\.\d+)?))\s*[\s,]\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*[\s,]\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*[\s,]\s*(0[.][0-9]+|[.][0-9]+|[01][.]0*|[01])\s*\)$/gm;o=Mi(xt(e,s),Et(u,c),!0)}else a==="#"&&(o=Au(xt(e,s),a+u));o&&t.push(o)}return t}function Eu(e){return!e||typeof e.getValue!="function"||typeof e.positionAt!="function"?[]:xu(e)}var Mu=/^-+|-+$/g,yi=100,yu=5;function ku(e,t){let n=[];if(t.findRegionSectionHeaders&&t.foldingRules?.markers){const r=Fu(e,t);n=n.concat(r)}if(t.findMarkSectionHeaders){const r=Pu(e,t);n=n.concat(r)}return n}function Fu(e,t){const n=[],r=e.getLineCount();for(let s=1;s<=r;s++){const i=e.getLineContent(s),a=i.match(t.foldingRules.markers.start);if(a){const u={startLineNumber:s,startColumn:a[0].length+1,endLineNumber:s,endColumn:i.length+1};if(u.endColumn>u.startColumn){const o={range:u,...Tu(i.substring(a[0].length)),shouldBeInComments:!1};(o.text||o.hasSeparatorLine)&&n.push(o)}}}return n}function Pu(e,t){const n=[],r=e.getLineCount();if(!t.markSectionHeaderRegex||t.markSectionHeaderRegex.trim()==="")return n;const s=U0(t.markSectionHeaderRegex),i=new RegExp(t.markSectionHeaderRegex,`gdm${s?"s":""}`);if(fl(i))return n;for(let a=1;a<=r;a+=yi-yu){const u=Math.min(a+yi-1,r),o=[];for(let h=a;h<=u;h++)o.push(e.getLineContent(h));const c=o.join(`
`);i.lastIndex=0;let f;for(;(f=i.exec(c))!==null;){const h=c.substring(0,f.index),g=(h.match(/\n/g)||[]).length,m=a+g,d=f[0].split(`
`),p=d.length,v=m+p-1,w=h.lastIndexOf(`
`)+1,A=f.index-w+1,N=d[d.length-1],x=p===1?A+f[0].length:N.length+1,k={startLineNumber:m,startColumn:A,endLineNumber:v,endColumn:x},M=(f.groups??{}).label??"",L=((f.groups??{}).separator??"")!=="",R={range:k,text:M,hasSeparatorLine:L,shouldBeInComments:!0};(R.text||R.hasSeparatorLine)&&(n.length===0||n[n.length-1].range.endLineNumber<R.range.startLineNumber)&&n.push(R),i.lastIndex=f.index+f[0].length}}return n}function Tu(e){e=e.trim();const t=e.startsWith("-");return e=e.replace(Mu,""),{text:e,hasSeparatorLine:t}}function De(e){return e===47||e===92}function ki(e){return e.replace(/[\\/]/g,C.sep)}function _u(e){return e.indexOf("/")===-1&&(e=ki(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function Fi(e,t=C.sep){if(!e)return"";const n=e.length,r=e.charCodeAt(0);if(De(r)){if(De(e.charCodeAt(1))&&!De(e.charCodeAt(2))){let i=3;const a=i;for(;i<n&&!De(e.charCodeAt(i));i++);if(a!==i&&!De(e.charCodeAt(i+1))){for(i+=1;i<n;i++)if(De(e.charCodeAt(i)))return e.slice(0,i+1).replace(/[\\/]/g,t)}}return t}else if(Du(r)&&e.charCodeAt(1)===58)return De(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let s=e.indexOf("://");if(s!==-1){for(s+=3;s<n;s++)if(De(e.charCodeAt(s)))return e.slice(0,s+1)}return""}function Pi(e,t,n,r=Zt){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(n){if(!vl(e,t))return!1;if(t.length===e.length)return!0;let i=t.length;return t.charAt(t.length-1)===r&&i--,e.charAt(i)===r}return t.charAt(t.length-1)!==r&&(t+=r),e.indexOf(t)===0}function Du(e){return e>=65&&e<=90||e>=97&&e<=122}var re;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatEditor="vscode-chat-editor",e.vscodeChatInput="chatSessionInput",e.vscodeChatSession="vscode-chat-session",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(re||(re={}));var Su="tkn",$u=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=C.join(t??"/",Vu(e))}getServerRootPath(){return this.f}get g(){return C.join(this.f,re.vscodeRemoteResource)}set(e,t,n){this.a[e]=t,this.b[e]=n}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(a){return bt(a),e}const t=e.authority;let n=this.a[t];n&&n.indexOf(":")!==-1&&n.indexOf("[")===-1&&(n=`[${n}]`);const r=this.b[t],s=this.c[t];let i=`path=${encodeURIComponent(e.path)}`;return typeof s=="string"&&(i+=`&${Su}=${encodeURIComponent(s)}`),oe.from({scheme:nl?this.d:re.vscodeRemoteResource,authority:`${n}:${r}`,path:this.g,query:i})}},Bu=new $u;function Vu(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var qu="vscode-app",Iu=class bn{static{this.a=qu}asBrowserUri(t){const n=this.b(t);return this.uriToBrowserUri(n)}uriToBrowserUri(t){return t.scheme===re.vscodeRemote?Bu.rewrite(t):t.scheme===re.file&&(tl||sl===`${re.vscodeFileResource}://${bn.a}`)?t.with({scheme:re.vscodeFileResource,authority:t.authority||bn.a,query:null,fragment:null}):t}asFileUri(t){const n=this.b(t);return this.uriToFileUri(n)}uriToFileUri(t){return t.scheme===re.vscodeFileResource?t.with({scheme:re.file,authority:t.authority!==bn.a?t.authority:null,query:null,fragment:null}):t}b(t){if(oe.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const n=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(n))return oe.joinPath(oe.parse(n,!0),t);const r=v0(n,t);return oe.file(r)}throw new Error("Cannot determine URI for module id!")}},ko=new Iu,Fo=Object.freeze({"Cache-Control":"no-cache, no-store"}),Po=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),Ti;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const n="vscode-coi";function r(i){let a;typeof i=="string"?a=new URL(i).searchParams:i instanceof URL?a=i.searchParams:oe.isUri(i)&&(a=new URL(i.toString(!0)).searchParams);const u=a?.get(n);if(u)return t.get(u)}e.getHeadersFromQuery=r;function s(i,a,u){if(!globalThis.crossOriginIsolated)return;const o=a&&u?"3":u?"2":"1";i instanceof URLSearchParams?i.set(n,o):i[n]=o}e.addSearchParam=s})(Ti||(Ti={}));function ye(e){return Qt(e,!0)}var ur=class{constructor(e){this.a=e}compare(e,t,n=!1){return e===t?0:dl(this.getComparisonKey(e,n),this.getComparisonKey(t,n))}isEqual(e,t,n=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,n)===this.getComparisonKey(t,n)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,n=!1){if(e.scheme===t.scheme){if(e.scheme===re.file)return Pi(ye(e),ye(t),this.a(e))&&e.query===t.query&&(n||e.fragment===t.fragment);if(_i(e.authority,t.authority))return Pi(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(n||e.fragment===t.fragment)}return!1}joinPath(e,...t){return oe.joinPath(e,...t)}basenameOrAuthority(e){return Uu(e)||e.authority}basename(e){return C.basename(e.path)}extname(e){return C.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===re.file?t=oe.file(N0(ye(e))).path:(t=C.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===re.file?t=oe.file(w0(ye(e))).path:t=C.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!_i(e.authority,t.authority))return;if(e.scheme===re.file){const s=L0(ye(e),ye(t));return Ke?ki(s):s}let n=e.path||"/";const r=t.path||"/";if(this.a(e)){let s=0;for(const i=Math.min(n.length,r.length);s<i&&!(n.charCodeAt(s)!==r.charCodeAt(s)&&n.charAt(s).toLowerCase()!==r.charAt(s).toLowerCase());s++);n=r.substr(0,s)+n.substr(s)}return C.relative(n,r)}resolvePath(e,t){if(e.scheme===re.file){const n=oe.file(p0(ye(e),t));return e.with({authority:n.authority,path:n.path})}return t=_u(t),e.with({path:C.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&wl(e,t)}hasTrailingPathSeparator(e,t=Zt){if(e.scheme===re.file){const n=ye(e);return n.length>Fi(n).length&&n[n.length-1]===t}else{const n=e.path;return n.length>1&&n.charCodeAt(n.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=Zt){return Di(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=Zt){let n=!1;if(e.scheme===re.file){const r=ye(e);n=r!==void 0&&r.length===Fi(r).length&&r[r.length-1]===t}else{t="/";const r=e.path;n=r.length===1&&r.charCodeAt(r.length-1)===47}return!n&&!Di(e,t)?e.with({path:e.path+"/"}):e}},z=new ur(()=>!1),To=new ur(e=>e.scheme===re.file?!el:!0),_o=new ur(e=>!0),Do=z.isEqual.bind(z),So=z.isEqualOrParent.bind(z),$o=z.getComparisonKey.bind(z),Bo=z.basenameOrAuthority.bind(z),Uu=z.basename.bind(z),Vo=z.extname.bind(z),qo=z.dirname.bind(z),Io=z.joinPath.bind(z),Uo=z.normalizePath.bind(z),jo=z.relativePath.bind(z),Oo=z.resolvePath.bind(z),Wo=z.isAbsolutePath.bind(z),_i=z.isEqualAuthority.bind(z),Di=z.hasTrailingPathSeparator.bind(z),Ho=z.removeTrailingPathSeparator.bind(z),zo=z.addTrailingPathSeparator.bind(z),Si;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(n){const r=new Map;n.path.substring(n.path.indexOf(";")+1,n.path.lastIndexOf(";")).split(";").forEach(a=>{const[u,o]=a.split(":");u&&o&&r.set(u,o)});const i=n.path.substring(0,n.path.indexOf(";"));return i&&r.set(e.META_DATA_MIME,i),r}e.parseMetaData=t})(Si||(Si={}));var Co=Symbol("MicrotaskDelay"),ju,or;(function(){const e=globalThis;typeof e.requestIdleCallback!="function"||typeof e.cancelIdleCallback!="function"?or=(t,n,r)=>{al(()=>{if(s)return;const i=Date.now()+15;n(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,i-Date.now())}}))});let s=!1;return{dispose(){s||(s=!0)}}}:or=(t,n,r)=>{const s=t.requestIdleCallback(n,typeof r=="number"?{timeout:r}:void 0);let i=!1;return{dispose(){i||(i=!0,t.cancelIdleCallback(s))}}},ju=(t,n)=>or(globalThis,t,n)})();var $i;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})($i||($i={}));var Bi;(function(e){async function t(r){let s;const i=await Promise.all(r.map(a=>a.then(u=>u,u=>{s||(s=u)})));if(typeof s<"u")throw s;return i}e.settled=t;function n(r){return new Promise(async(s,i)=>{try{await r(s,i)}catch(a){i(a)}})}e.withAsyncBody=n})(Bi||(Bi={}));var Vi;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})(Vi||(Vi={}));var Go=class he{static fromArray(t){return new he(n=>{n.emitMany(t)})}static fromPromise(t){return new he(async n=>{n.emitMany(await t)})}static fromPromisesResolveOrder(t){return new he(async n=>{await Promise.all(t.map(async r=>n.emitOne(await r)))})}static merge(t){return new he(async n=>{await Promise.all(t.map(async r=>{for await(const s of r)n.emitOne(s)}))})}static{this.EMPTY=he.fromArray([])}constructor(t,n){this.a=0,this.b=[],this.d=null,this.f=n,this.g=new ge,queueMicrotask(async()=>{const r={emitOne:s=>this.h(s),emitMany:s=>this.j(s),reject:s=>this.l(s)};try{await Promise.resolve(t(r)),this.k()}catch(s){this.l(s)}finally{r.emitOne=void 0,r.emitMany=void 0,r.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await Vt.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,n){return new he(async r=>{for await(const s of t)r.emitOne(n(s))})}map(t){return he.map(this,t)}static filter(t,n){return new he(async r=>{for await(const s of t)n(s)&&r.emitOne(s)})}filter(t){return he.filter(this,t)}static coalesce(t){return he.filter(t,n=>!!n)}coalesce(){return he.coalesce(this)}static async toPromise(t){const n=[];for await(const r of t)n.push(r);return n}toPromise(){return he.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}},Xo=Symbol("AsyncReaderEndOfStream"),Ou=class{constructor(e){this.a=e,this.b=new Uint32Array(e.length),this.c=new Int32Array(1),this.c[0]=-1}getCount(){return this.a.length}insertValues(e,t){e=nt(e);const n=this.a,r=this.b,s=t.length;return s===0?!1:(this.a=new Uint32Array(n.length+s),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e),e+s),this.a.set(t,e),e-1<this.c[0]&&(this.c[0]=e-1),this.b=new Uint32Array(this.a.length),this.c[0]>=0&&this.b.set(r.subarray(0,this.c[0]+1)),!0)}setValue(e,t){return e=nt(e),t=nt(t),this.a[e]===t?!1:(this.a[e]=t,e-1<this.c[0]&&(this.c[0]=e-1),!0)}removeValues(e,t){e=nt(e),t=nt(t);const n=this.a,r=this.b;if(e>=n.length)return!1;const s=n.length-e;return t>=s&&(t=s),t===0?!1:(this.a=new Uint32Array(n.length-t),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e+t),e),this.b=new Uint32Array(this.a.length),e-1<this.c[0]&&(this.c[0]=e-1),this.c[0]>=0&&this.b.set(r.subarray(0,this.c[0]+1)),!0)}getTotalSum(){return this.a.length===0?0:this.d(this.a.length-1)}getPrefixSum(e){return e<0?0:(e=nt(e),this.d(e))}d(e){if(e<=this.c[0])return this.b[e];let t=this.c[0]+1;t===0&&(this.b[0]=this.a[0],t++),e>=this.a.length&&(e=this.a.length-1);for(let n=t;n<=e;n++)this.b[n]=this.b[n-1]+this.a[n];return this.c[0]=Math.max(this.c[0],e),this.b[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.a.length-1,r=0,s=0,i=0;for(;t<=n;)if(r=t+(n-t)/2|0,s=this.b[r],i=s-this.a[r],e<i)n=r-1;else if(e>=s)t=r+1;else break;return new Wu(r,e-i)}},Wu=class{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}},Hu=class{constructor(e,t,n,r){this.a=e,this.b=t,this.c=n,this.d=r,this.f=null,this.g=null}dispose(){this.b.length=0}get version(){return this.d}getText(){return this.g===null&&(this.g=this.b.join(this.c)),this.g}onEvents(e){e.eol&&e.eol!==this.c&&(this.c=e.eol,this.f=null);const t=e.changes;for(const n of t)this.k(n.range),this.l(new O(n.range.startLineNumber,n.range.startColumn),n.text);this.d=e.versionId,this.g=null}h(){if(!this.f){const e=this.c.length,t=this.b.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this.b[r].length+e;this.f=new Ou(n)}}j(e,t){this.b[e]=t,this.f&&this.f.setValue(e,this.b[e].length+this.c.length)}k(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.startLineNumber-1].substring(e.endColumn-1));return}this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.endLineNumber-1].substring(e.endColumn-1)),this.b.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this.f&&this.f.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}l(e,t){if(t.length===0)return;const n=qr(t);if(n.length===1){this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]+this.b[e.lineNumber-1].substring(e.column-1));return}n[n.length-1]+=this.b[e.lineNumber-1].substring(e.column-1),this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]);const r=new Uint32Array(n.length-1);for(let s=1;s<n.length;s++)this.b.splice(e.lineNumber+s-1,0,n[s]),r[s-1]=n[s].length+this.c.length;this.f&&this.f.insertValues(e.lineNumber,r)}},Jo=60*1e3,zu="workerTextModelSync",Cu=class{constructor(){this.a=Object.create(null)}bindToServer(e){e.setChannel(zu,this)}getModel(e){return this.a[e]}getModels(){const e=[];return Object.keys(this.a).forEach(t=>e.push(this.a[t])),e}$acceptNewModel(e){this.a[e.url]=new Gu(oe.parse(e.url),e.lines,e.EOL,e.versionId)}$acceptModelChanged(e,t){if(!this.a[e])return;this.a[e].onEvents(t)}$acceptRemovedModel(e){this.a[e]&&delete this.a[e]}},Gu=class extends Hu{get uri(){return this.a}get eol(){return this.c}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this.b.length;n++){const r=this.b[n],s=this.offsetAt(new O(n+1,1)),i=r.matchAll(e);for(const a of i)(a.index||a.index===0)&&(a.index=a.index+s),t.push(a)}return t}getLinesContent(){return this.b.slice(0)}getLineCount(){return this.b.length}getLineContent(e){return this.b[e-1]}getWordAtPosition(e,t){const n=Kn(e.column,Ys(t),this.b[e.lineNumber-1],0);return n?new _(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}getWordUntilPosition(e,t){const n=this.getWordAtPosition(e,t);return n?{word:this.b[e.lineNumber-1].substring(n.startColumn-1,e.column-1),startColumn:n.startColumn,endColumn:e.column}:{word:"",startColumn:e.column,endColumn:e.column}}words(e){const t=this.b,n=this.m.bind(this);let r=0,s="",i=0,a=[];return{*[Symbol.iterator](){for(;;)if(i<a.length){const u=s.substring(a[i].start,a[i].end);i+=1,yield u}else if(r<t.length)s=t[r],a=n(s,e),i=0,r+=1;else break}}}getLineWords(e,t){const n=this.b[e-1],r=this.m(n,t),s=[];for(const i of r)s.push({word:n.substring(i.start,i.end),startColumn:i.start+1,endColumn:i.end+1});return s}m(e,t){const n=[];let r;for(t.lastIndex=0;(r=t.exec(e))&&r[0].length!==0;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if(e=this.n(e),e.startLineNumber===e.endLineNumber)return this.b[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this.c,n=e.startLineNumber-1,r=e.endLineNumber-1,s=[];s.push(this.b[n].substring(e.startColumn-1));for(let i=n+1;i<r;i++)s.push(this.b[i]);return s.push(this.b[r].substring(0,e.endColumn-1)),s.join(t)}offsetAt(e){return e=this.o(e),this.h(),this.f.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this.h();const t=this.f.getIndexOf(e),n=this.b[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}n(e){const t=this.o({lineNumber:e.startLineNumber,column:e.startColumn}),n=this.o({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}o(e){if(!O.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this.b.length)t=this.b.length,n=this.b[t-1].length+1,r=!0;else{const s=this.b[t-1].length+1;n<1?(n=1,r=!0):n>s&&(n=s,r=!0)}return r?{lineNumber:t,column:n}:e}},qi=class{constructor(e){this.replacements=e;let t=-1;for(const n of e){if(!(n.replaceRange.start>=t))throw new K(`Edits must be disjoint and sorted. Found ${n} after ${t}`);t=n.replaceRange.endExclusive}}equals(e){if(this.replacements.length!==e.replacements.length)return!1;for(let t=0;t<this.replacements.length;t++)if(!this.replacements[t].equals(e.replacements[t]))return!1;return!0}toString(){return`[${this.replacements.map(t=>t.toString()).join(", ")}]`}normalize(){const e=[];let t;for(const n of this.replacements)if(!(n.getNewLength()===0&&n.replaceRange.length===0)){if(t&&t.replaceRange.endExclusive===n.replaceRange.start){const r=t.tryJoinTouching(n);if(r){t=r;continue}}t&&e.push(t),t=n}return t&&e.push(t),this.a(e)}compose(e){const t=this.normalize(),n=e.normalize();if(t.isEmpty())return n;if(n.isEmpty())return t;const r=[...t.replacements],s=[];let i=0;for(const a of n.replacements){for(;;){const f=r[0];if(!f||f.replaceRange.start+i+f.getNewLength()>=a.replaceRange.start)break;r.shift(),s.push(f),i+=f.getNewLength()-f.replaceRange.length}const u=i;let o,c;for(;;){const f=r[0];if(!f||f.replaceRange.start+i>a.replaceRange.endExclusive)break;o||(o=f),c=f,r.shift(),i+=f.getNewLength()-f.replaceRange.length}if(!o)s.push(a.delta(-i));else{const f=Math.min(o.replaceRange.start,a.replaceRange.start-u),h=a.replaceRange.start-(o.replaceRange.start+u);if(h>0){const p=o.slice(V.emptyAt(f),new V(0,h));s.push(p)}if(!c)throw new K("Invariant violation: lastIntersecting is undefined");const g=c.replaceRange.endExclusive+i-a.replaceRange.endExclusive;if(g>0){const p=c.slice(V.ofStartAndLength(c.replaceRange.endExclusive,0),new V(c.getNewLength()-g,c.getNewLength()));r.unshift(p),i-=p.getNewLength()-p.replaceRange.length}const m=new V(f,a.replaceRange.endExclusive-i),d=a.slice(m,new V(0,a.getNewLength()));s.push(d)}}for(;;){const a=r.shift();if(!a)break;s.push(a)}return this.a(s).normalize()}decomposeSplit(e){const t=[],n=[];let r=0;for(const s of this.replacements)e(s)?(t.push(s),r+=s.getNewLength()-s.replaceRange.length):n.push(s.slice(s.replaceRange.delta(r),new V(0,s.getNewLength())));return{e1:this.a(t),e2:this.a(n)}}getNewRanges(){const e=[];let t=0;for(const n of this.replacements)e.push(V.ofStartAndLength(n.replaceRange.start+t,n.getNewLength())),t+=n.getLengthDelta();return e}getJoinedReplaceRange(){if(this.replacements.length!==0)return this.replacements[0].replaceRange.join(this.replacements.at(-1).replaceRange)}isEmpty(){return this.replacements.length===0}getLengthDelta(){return va(this.replacements,e=>e.getLengthDelta())}getNewDataLength(e){return e+this.getLengthDelta()}applyToOffset(e){let t=0;for(const n of this.replacements)if(n.replaceRange.start<=e){if(e<n.replaceRange.endExclusive)return n.replaceRange.start+t;t+=n.getNewLength()-n.replaceRange.length}else break;return e+t}applyToOffsetRange(e){return new V(this.applyToOffset(e.start),this.applyToOffset(e.endExclusive))}applyInverseToOffset(e){let t=0;for(const n of this.replacements){const r=n.getNewLength();if(n.replaceRange.start<=e-t){if(e-t<n.replaceRange.start+r)return n.replaceRange.start;t+=r-n.replaceRange.length}else break}return e-t}applyToOffsetOrUndefined(e){let t=0;for(const n of this.replacements)if(n.replaceRange.start<=e){if(e<n.replaceRange.endExclusive)return;t+=n.getNewLength()-n.replaceRange.length}else break;return e+t}applyToOffsetRangeOrUndefined(e){const t=this.applyToOffsetOrUndefined(e.start);if(t===void 0)return;const n=this.applyToOffsetOrUndefined(e.endExclusive);if(n!==void 0)return new V(t,n)}},Xu=class{constructor(e){this.replaceRange=e}delta(e){return this.slice(this.replaceRange.delta(e),new V(0,this.getNewLength()))}getLengthDelta(){return this.getNewLength()-this.replaceRange.length}toString(){return`{ ${this.replaceRange.toString()} -> ${this.getNewLength()} }`}get isEmpty(){return this.getNewLength()===0&&this.replaceRange.length===0}getRangeAfterReplace(){return new V(this.replaceRange.start,this.replaceRange.start+this.getNewLength())}},Yo=class Tt extends qi{static{this.empty=new Tt([])}static create(t){return new Tt(t)}static single(t){return new Tt([t])}a(t){return new Tt(t)}},Ii=class extends qi{get TReplacement(){throw new Error("TReplacement is not defined for BaseStringEdit")}static composeOrUndefined(e){if(e.length===0)return;let t=e[0];for(let n=1;n<e.length;n++)t=t.compose(e[n]);return t}static trySwap(e,t){const n=e.inverseOnSlice((i,a)=>" ".repeat(a-i)),r=t.tryRebase(n);if(!r)return;const s=e.tryRebase(r);if(s)return{e1:r,e2:s}}apply(e){const t=[];let n=0;for(const r of this.replacements)t.push(e.substring(n,r.replaceRange.start)),t.push(r.newText),n=r.replaceRange.endExclusive;return t.push(e.substring(n)),t.join("")}inverseOnSlice(e){const t=[];let n=0;for(const r of this.replacements)t.push(me.replace(V.ofStartAndLength(r.replaceRange.start+n,r.newText.length),e(r.replaceRange.start,r.replaceRange.endExclusive))),n+=r.newText.length-r.replaceRange.length;return new Se(t)}inverse(e){return this.inverseOnSlice((t,n)=>e.substring(t,n))}rebaseSkipConflicting(e){return this.b(e,!1)}tryRebase(e){return this.b(e,!0)}b(e,t){const n=[];let r=0,s=0,i=0;for(;s<this.replacements.length||r<e.replacements.length;){const a=e.replacements[r],u=this.replacements[s];if(u)if(!a)n.push(new me(u.replaceRange.delta(i),u.newText)),s++;else if(u.replaceRange.intersectsOrTouches(a.replaceRange)){if(s++,t)return}else u.replaceRange.start<a.replaceRange.start?(n.push(new me(u.replaceRange.delta(i),u.newText)),s++):(r++,i+=a.newText.length-a.replaceRange.length);else break}return new Se(n)}toJson(){return this.replacements.map(e=>e.toJson())}isNeutralOn(e){return this.replacements.every(t=>t.isNeutralOn(e))}removeCommonSuffixPrefix(e){const t=[];for(const n of this.replacements){const r=n.removeCommonSuffixPrefix(e);r.isEmpty||t.push(r)}return new Se(t)}normalizeEOL(e){return new Se(this.replacements.map(t=>t.normalizeEOL(e)))}normalizeOnSource(e){const t=this.apply(e),r=me.replace(V.ofLength(e.length),t).removeCommonSuffixAndPrefix(e);return r.isEmpty?Se.empty:r.toEdit()}removeCommonSuffixAndPrefix(e){return this.a(this.replacements.map(t=>t.removeCommonSuffixAndPrefix(e))).normalize()}applyOnText(e){return new Rt(this.apply(e.value))}mapData(e){return new Ju(this.replacements.map(t=>new on(t.replaceRange,t.newText,e(t))))}},Ui=class extends Xu{constructor(e,t){super(e),this.newText=t}getNewLength(){return this.newText.length}toString(){return`${this.replaceRange} -> "${this.newText}"`}replace(e){return e.substring(0,this.replaceRange.start)+this.newText+e.substring(this.replaceRange.endExclusive)}isNeutralOn(e){return this.newText===e.substring(this.replaceRange.start,this.replaceRange.endExclusive)}removeCommonSuffixPrefix(e){const t=e.substring(this.replaceRange.start,this.replaceRange.endExclusive),n=zt(t,this.newText),r=Math.min(t.length-n,this.newText.length-n,Ct(t,this.newText)),s=new V(this.replaceRange.start+n,this.replaceRange.endExclusive-r),i=this.newText.substring(n,this.newText.length-r);return new me(s,i)}normalizeEOL(e){const t=this.newText.replace(/\r\n|\n/g,e);return new me(this.replaceRange,t)}removeCommonSuffixAndPrefix(e){return this.removeCommonSuffix(e).removeCommonPrefix(e)}removeCommonPrefix(e){const t=this.replaceRange.substring(e),n=zt(t,this.newText);return n===0?this:this.slice(this.replaceRange.deltaStart(n),new V(n,this.newText.length))}removeCommonSuffix(e){const t=this.replaceRange.substring(e),n=Ct(t,this.newText);return n===0?this:this.slice(this.replaceRange.deltaEnd(-n),new V(0,this.newText.length-n))}toEdit(){return new Se([this])}toJson(){return{txt:this.newText,pos:this.replaceRange.start,len:this.replaceRange.length}}},Se=class fe extends Ii{static{this.empty=new fe([])}static create(t){return new fe(t)}static single(t){return new fe([t])}static replace(t,n){return new fe([new me(t,n)])}static insert(t,n){return new fe([new me(V.emptyAt(t),n)])}static delete(t){return new fe([new me(t,"")])}static fromJson(t){return new fe(t.map(me.fromJson))}static compose(t){if(t.length===0)return fe.empty;let n=t[0];for(let r=1;r<t.length;r++)n=n.compose(t[r]);return n}static composeSequentialReplacements(t){let n=fe.empty,r=[];for(const s of t){const i=r.at(-1);!i||s.replaceRange.isBefore(i.replaceRange)?r.push(s):(n=n.compose(fe.create(r.reverse())),r=[s])}return n=n.compose(fe.create(r.reverse())),n}constructor(t){super(t)}a(t){return new fe(t)}},me=class Ce extends Ui{static insert(t,n){return new Ce(V.emptyAt(t),n)}static replace(t,n){return new Ce(t,n)}static delete(t){return new Ce(t,"")}static fromJson(t){return new Ce(V.ofStartAndLength(t.pos,t.len),t.txt)}equals(t){return this.replaceRange.equals(t.replaceRange)&&this.newText===t.newText}tryJoinTouching(t){return new Ce(this.replaceRange.joinRightTouching(t.replaceRange),this.newText+t.newText)}slice(t,n){return new Ce(t,n?n.substring(this.newText):this.newText)}},Ju=class Fe extends Ii{static{this.empty=new Fe([])}static create(t){return new Fe(t)}static single(t){return new Fe([t])}static replace(t,n,r){return new Fe([new on(t,n,r)])}static insert(t,n,r){return new Fe([new on(V.emptyAt(t),n,r)])}static delete(t,n){return new Fe([new on(t,"",n)])}static compose(t){if(t.length===0)return Fe.empty;let n=t[0];for(let r=1;r<t.length;r++)n=n.compose(t[r]);return n}constructor(t){super(t)}a(t){return new Fe(t)}toStringEdit(){return new Se(this.replacements.map(t=>new me(t.replaceRange,t.newText)))}},on=class mt extends Ui{static insert(t,n,r){return new mt(V.emptyAt(t),n,r)}static replace(t,n,r){return new mt(t,n,r)}static delete(t,n){return new mt(t,"",n)}constructor(t,n,r){super(t,n),this.data=r}equals(t){return this.replaceRange.equals(t.replaceRange)&&this.newText===t.newText&&this.data===t.data}tryJoinTouching(t){const n=this.data.join(t.data);if(n!==void 0)return new mt(this.replaceRange.joinRightTouching(t.replaceRange),this.newText+t.newText,n)}slice(t,n){return new mt(t,n?n.substring(this.newText):this.newText,this.data)}};Q0({StringEdit:Se,StringReplacement:me,TextReplacement:Me,TextEdit:er,TextLength:st});function Zo(){}var Yu=class _t{constructor(t=null){this.f=t,this.d=new Cu}dispose(){}async $ping(){return"pong"}g(t){return this.d.getModel(t)}getModels(){return this.d.getModels()}$acceptNewModel(t){this.d.$acceptNewModel(t)}$acceptModelChanged(t,n){this.d.$acceptModelChanged(t,n)}$acceptRemovedModel(t){this.d.$acceptRemovedModel(t)}async $computeUnicodeHighlights(t,n,r){const s=this.g(t);return s?X0.computeUnicodeHighlights(s,n,r):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async $findSectionHeaders(t,n){const r=this.g(t);return r?ku(r,n):[]}async $computeDiff(t,n,r,s){const i=this.g(t),a=this.g(n);return!i||!a?null:_t.h(i,a,r,s)}static h(t,n,r,s){const i=s==="advanced"?At.getDefault():At.getLegacy(),a=t.getLinesContent(),u=n.getLinesContent(),o=i.computeDiff(a,u,r),c=o.changes.length>0?!1:this.j(t,n);function f(h){return h.map(g=>[g.original.startLineNumber,g.original.endLineNumberExclusive,g.modified.startLineNumber,g.modified.endLineNumberExclusive,g.innerChanges?.map(m=>[m.originalRange.startLineNumber,m.originalRange.startColumn,m.originalRange.endLineNumber,m.originalRange.endColumn,m.modifiedRange.startLineNumber,m.modifiedRange.startColumn,m.modifiedRange.endLineNumber,m.modifiedRange.endColumn])])}return{identical:c,quitEarly:o.hitTimeout,changes:f(o.changes),moves:o.moves.map(h=>[h.lineRangeMapping.original.startLineNumber,h.lineRangeMapping.original.endLineNumberExclusive,h.lineRangeMapping.modified.startLineNumber,h.lineRangeMapping.modified.endLineNumberExclusive,f(h.changes)])}}static j(t,n){const r=t.getLineCount(),s=n.getLineCount();if(r!==s)return!1;for(let i=1;i<=r;i++){const a=t.getLineContent(i),u=n.getLineContent(i);if(a!==u)return!1}return!0}async $computeDirtyDiff(t,n,r){const s=this.g(t),i=this.g(n);if(!s||!i)return null;const a=s.getLinesContent(),u=i.getLinesContent();return new ui(a,u,{shouldComputeCharChanges:!1,shouldPostProcessCharChanges:!1,shouldIgnoreTrimWhitespace:r,shouldMakePrettyDiff:!0,maxComputationTime:1e3}).computeDiff().changes}$computeStringDiff(t,n,r,s){const i=s==="advanced"?At.getDefault():At.getLegacy();const a=new Rt(t),u=a.getLines(),o=new Rt(n),c=o.getLines(),f=i.computeDiff(u,c,{ignoreTrimWhitespace:!1,maxComputationTimeMs:r.maxComputationTimeMs,computeMoves:!1,extendToSubwords:!1}),h=lt.toTextEdit(f.changes,o);return a.getTransformer().getStringEdit(h).toJson()}static{this.k=1e5}async $computeMoreMinimalEdits(t,n,r){const s=this.g(t);if(!s)return n;const i=[];let a;n=n.slice(0).sort((o,c)=>{if(o.range&&c.range)return _.compareRangesUsingStarts(o.range,c.range);const f=o.range?0:1,h=c.range?0:1;return f-h});let u=0;for(let o=1;o<n.length;o++)_.getEndPosition(n[u].range).equals(_.getStartPosition(n[o].range))?(n[u].range=_.fromPositions(_.getStartPosition(n[u].range),_.getEndPosition(n[o].range)),n[u].text+=n[o].text):(u++,n[u]=n[o]);n.length=u+1;for(let{range:o,text:c,eol:f}of n){if(typeof f=="number"&&(a=f),_.isEmpty(o)&&!c)continue;const h=s.getValueInRange(o);if(c=c.replace(/\r\n|\n|\r/g,s.eol),h===c)continue;if(Math.max(c.length,h.length)>_t.k){i.push({range:o,text:c});continue}const g=Cl(h,c,r),m=s.offsetAt(_.lift(o).getStartPosition());for(const d of g){const p=s.positionAt(m+d.originalStart),v=s.positionAt(m+d.originalStart+d.originalLength),w={text:c.substr(d.modifiedStart,d.modifiedLength),range:{startLineNumber:p.lineNumber,startColumn:p.column,endLineNumber:v.lineNumber,endColumn:v.column}};s.getValueInRange(w.range)!==w.text&&i.push(w)}}return typeof a=="number"&&i.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}$computeHumanReadableDiff(t,n,r){const s=this.g(t);if(!s)return n;const i=[];let a;n=n.slice(0).sort((c,f)=>{if(c.range&&f.range)return _.compareRangesUsingStarts(c.range,f.range);const h=c.range?0:1,g=f.range?0:1;return h-g});for(let{range:c,text:f,eol:h}of n){let g=function(N,x){return new O(N.lineNumber+x.lineNumber-1,x.lineNumber===1?N.column+x.column-1:x.column)},m=function(N,x){const k=[];for(let M=x.startLineNumber;M<=x.endLineNumber;M++){const L=N[M-1];M===x.startLineNumber&&M===x.endLineNumber?k.push(L.substring(x.startColumn-1,x.endColumn-1)):M===x.startLineNumber?k.push(L.substring(x.startColumn-1)):M===x.endLineNumber?k.push(L.substring(0,x.endColumn-1)):k.push(L)}return k};var u=g,o=m;if(typeof h=="number"&&(a=h),_.isEmpty(c)&&!f)continue;const d=s.getValueInRange(c);if(f=f.replace(/\r\n|\n|\r/g,s.eol),d===f)continue;if(Math.max(f.length,d.length)>_t.k){i.push({range:c,text:f});continue}const p=d.split(/\r\n|\n|\r/),v=f.split(/\r\n|\n|\r/),w=At.getDefault().computeDiff(p,v,r),A=_.lift(c).getStartPosition();for(const N of w.changes)if(N.innerChanges)for(const x of N.innerChanges)i.push({range:_.fromPositions(g(A,x.originalRange.getStartPosition()),g(A,x.originalRange.getEndPosition())),text:m(v,x.modifiedRange).join(s.eol)});else throw new K("The experimental diff algorithm always produces inner changes")}return typeof a=="number"&&i.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}async $computeLinks(t){const n=this.g(t);return n?Kl(n):null}async $computeDefaultDocumentColors(t){const n=this.g(t);return n?Eu(n):null}static{this.l=1e4}async $textualSuggest(t,n,r,s){const i=new Mr,a=new RegExp(r,s),u=new Set;e:for(const o of t){const c=this.g(o);if(c){for(const f of c.words(a))if(!(f===n||!isNaN(Number(f)))&&(u.add(f),u.size>_t.l))break e}}return{words:Array.from(u),duration:i.elapsed()}}async $computeWordRanges(t,n,r,s){const i=this.g(t);if(!i)return Object.create(null);const a=new RegExp(r,s),u=Object.create(null);for(let o=n.startLineNumber;o<n.endLineNumber;o++){const c=i.getLineWords(o,a);for(const f of c){if(!isNaN(Number(f.word)))continue;let h=u[f.word];h||(h=[],u[f.word]=h),h.push({startLineNumber:o,startColumn:f.startColumn,endLineNumber:o,endColumn:f.endColumn})}}return u}async $navigateValueSet(t,n,r,s,i){const a=this.g(t);if(!a)return null;const u=new RegExp(s,i);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});const o=a.getValueInRange(n),c=a.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},u);if(!c)return null;const f=a.getValueInRange(c);return e0.INSTANCE.navigateValueSet(n,o,c,f,r)}$fmr(t,n){if(!this.f||typeof this.f[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this.f[t].apply(this.f,n))}catch(r){return Promise.reject(r)}}};typeof importScripts=="function"&&(globalThis.monaco=V0()),Sl(()=>new Yu(null));

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6f17636121051a53c88d3e605c491d22af2ba755/core/vs/editor/common/services/editorWebWorkerMain.js.map
