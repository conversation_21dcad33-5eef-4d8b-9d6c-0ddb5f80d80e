import * as vscode from 'vscode';
import { OllamaClient } from './services/ollamaClient';
import { ChatProvider } from './providers/chatProvider';
import { CompletionProvider } from './providers/completionProvider';
import { ContextEngine } from './services/contextEngine';
import { ConfigurationManager } from './services/configurationManager';

let ollamaClient: OllamaClient;
let chatProvider: ChatProvider;
let completionProvider: CompletionProvider;
let contextEngine: ContextEngine;
let configManager: ConfigurationManager;

export async function activate(context: vscode.ExtensionContext) {
    console.log('Ollama Code Assistant is now active!');

    // Initialize services
    configManager = new ConfigurationManager();
    ollamaClient = new OllamaClient(configManager);
    contextEngine = new ContextEngine();
    chatProvider = new ChatProvider(context, ollamaClient, contextEngine);
    completionProvider = new CompletionProvider(ollamaClient, contextEngine, configManager);

    // Register commands
    const openChatCommand = vscode.commands.registerCommand('ollama-assistant.openChat', () => {
        chatProvider.openChat();
    });

    const selectModelCommand = vscode.commands.registerCommand('ollama-assistant.selectModel', async () => {
        await selectModel();
    });

    const refreshModelsCommand = vscode.commands.registerCommand('ollama-assistant.refreshModels', async () => {
        await ollamaClient.refreshModels();
        vscode.window.showInformationMessage('Models refreshed successfully!');
    });

    const clearHistoryCommand = vscode.commands.registerCommand('ollama-assistant.clearHistory', async () => {
        await chatProvider.clearHistory();
        vscode.window.showInformationMessage('Chat history cleared!');
    });

    // Register providers
    const completionDisposable = vscode.languages.registerInlineCompletionItemProvider(
        { pattern: '**' },
        completionProvider
    );

    // Register tree view
    const treeDataProvider = chatProvider.getTreeDataProvider();
    vscode.window.createTreeView('ollama-assistant.chatView', {
        treeDataProvider,
        showCollapseAll: true
    });

    // Add to subscriptions
    context.subscriptions.push(
        openChatCommand,
        selectModelCommand,
        refreshModelsCommand,
        clearHistoryCommand,
        completionDisposable
    );

    // Initialize connection to Ollama
    try {
        await ollamaClient.initialize();
        vscode.window.showInformationMessage('Connected to Ollama server successfully!');
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to connect to Ollama: ${error}`);
    }
}

async function selectModel() {
    try {
        const models = await ollamaClient.getAvailableModels();
        
        if (models.length === 0) {
            vscode.window.showWarningMessage('No models available. Please ensure Ollama is running and models are installed.');
            return;
        }

        const modelNames = models.map(model => model.name);
        const selected = await vscode.window.showQuickPick(modelNames, {
            placeHolder: 'Select an Ollama model'
        });

        if (selected) {
            await configManager.setDefaultModel(selected);
            vscode.window.showInformationMessage(`Selected model: ${selected}`);
        }
    } catch (error) {
        vscode.window.showErrorMessage(`Failed to load models: ${error}`);
    }
}

export function deactivate() {
    // Cleanup resources
    if (ollamaClient) {
        ollamaClient.dispose();
    }
    if (chatProvider) {
        chatProvider.dispose();
    }
}
