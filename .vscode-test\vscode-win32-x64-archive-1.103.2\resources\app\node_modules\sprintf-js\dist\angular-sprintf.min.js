/*! sprintf-js v1.1.3 | Copyright (c) 2007-present, <PERSON><PERSON><PERSON> <<EMAIL>> | BSD-3-Clause */
!function(){"use strict";angular.module("sprintf",[]).filter("sprintf",function(){return function(){return sprintf.apply(null,arguments)}}).filter("fmt",["$filter",function(t){return t("sprintf")}]).filter("vsprintf",function(){return function(t,n){return vsprintf(t,n)}}).filter("vfmt",["$filter",function(t){return t("vsprintf")}])}();//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/6f17636121051a53c88d3e605c491d22af2ba755/node_modules/sprintf-js/dist/angular-sprintf.min.js.map
